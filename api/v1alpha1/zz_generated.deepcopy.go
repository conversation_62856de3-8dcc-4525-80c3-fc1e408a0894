//go:build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	apisv1 "sigs.k8s.io/gateway-api/apis/v1"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIBackend) DeepCopyInto(out *AIBackend) {
	*out = *in
	if in.LLM != nil {
		in, out := &in.LLM, &out.LLM
		*out = new(LLMProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.MultiPool != nil {
		in, out := &in.MultiPool, &out.MultiPool
		*out = new(MultiPoolConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIBackend.
func (in *AIBackend) DeepCopy() *AIBackend {
	if in == nil {
		return nil
	}
	out := new(AIBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIPolicy) DeepCopyInto(out *AIPolicy) {
	*out = *in
	if in.PromptEnrichment != nil {
		in, out := &in.PromptEnrichment, &out.PromptEnrichment
		*out = new(AIPromptEnrichment)
		(*in).DeepCopyInto(*out)
	}
	if in.PromptGuard != nil {
		in, out := &in.PromptGuard, &out.PromptGuard
		*out = new(AIPromptGuard)
		(*in).DeepCopyInto(*out)
	}
	if in.Defaults != nil {
		in, out := &in.Defaults, &out.Defaults
		*out = make([]FieldDefault, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RouteType != nil {
		in, out := &in.RouteType, &out.RouteType
		*out = new(RouteType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIPolicy.
func (in *AIPolicy) DeepCopy() *AIPolicy {
	if in == nil {
		return nil
	}
	out := new(AIPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIPromptEnrichment) DeepCopyInto(out *AIPromptEnrichment) {
	*out = *in
	if in.Prepend != nil {
		in, out := &in.Prepend, &out.Prepend
		*out = make([]Message, len(*in))
		copy(*out, *in)
	}
	if in.Append != nil {
		in, out := &in.Append, &out.Append
		*out = make([]Message, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIPromptEnrichment.
func (in *AIPromptEnrichment) DeepCopy() *AIPromptEnrichment {
	if in == nil {
		return nil
	}
	out := new(AIPromptEnrichment)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIPromptGuard) DeepCopyInto(out *AIPromptGuard) {
	*out = *in
	if in.Request != nil {
		in, out := &in.Request, &out.Request
		*out = new(PromptguardRequest)
		(*in).DeepCopyInto(*out)
	}
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(PromptguardResponse)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIPromptGuard.
func (in *AIPromptGuard) DeepCopy() *AIPromptGuard {
	if in == nil {
		return nil
	}
	out := new(AIPromptGuard)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessLog) DeepCopyInto(out *AccessLog) {
	*out = *in
	if in.FileSink != nil {
		in, out := &in.FileSink, &out.FileSink
		*out = new(FileSink)
		(*in).DeepCopyInto(*out)
	}
	if in.GrpcService != nil {
		in, out := &in.GrpcService, &out.GrpcService
		*out = new(GrpcService)
		(*in).DeepCopyInto(*out)
	}
	if in.Filter != nil {
		in, out := &in.Filter, &out.Filter
		*out = new(AccessLogFilter)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessLog.
func (in *AccessLog) DeepCopy() *AccessLog {
	if in == nil {
		return nil
	}
	out := new(AccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessLogFilter) DeepCopyInto(out *AccessLogFilter) {
	*out = *in
	if in.FilterType != nil {
		in, out := &in.FilterType, &out.FilterType
		*out = new(FilterType)
		(*in).DeepCopyInto(*out)
	}
	if in.AndFilter != nil {
		in, out := &in.AndFilter, &out.AndFilter
		*out = make([]FilterType, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.OrFilter != nil {
		in, out := &in.OrFilter, &out.OrFilter
		*out = make([]FilterType, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessLogFilter.
func (in *AccessLogFilter) DeepCopy() *AccessLogFilter {
	if in == nil {
		return nil
	}
	out := new(AccessLogFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AgentGateway) DeepCopyInto(out *AgentGateway) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	if in.LogLevel != nil {
		in, out := &in.LogLevel, &out.LogLevel
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AgentGateway.
func (in *AgentGateway) DeepCopy() *AgentGateway {
	if in == nil {
		return nil
	}
	out := new(AgentGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AiExtension) DeepCopyInto(out *AiExtension) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(Image)
		(*in).DeepCopyInto(*out)
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(v1.SecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(v1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]v1.EnvVar, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Ports != nil {
		in, out := &in.Ports, &out.Ports
		*out = make([]v1.ContainerPort, len(*in))
		copy(*out, *in)
	}
	if in.Stats != nil {
		in, out := &in.Stats, &out.Stats
		*out = new(AiExtensionStats)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AiExtension.
func (in *AiExtension) DeepCopy() *AiExtension {
	if in == nil {
		return nil
	}
	out := new(AiExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AiExtensionStats) DeepCopyInto(out *AiExtensionStats) {
	*out = *in
	if in.CustomLabels != nil {
		in, out := &in.CustomLabels, &out.CustomLabels
		*out = make([]*CustomLabel, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(CustomLabel)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AiExtensionStats.
func (in *AiExtensionStats) DeepCopy() *AiExtensionStats {
	if in == nil {
		return nil
	}
	out := new(AiExtensionStats)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AnthropicConfig) DeepCopyInto(out *AnthropicConfig) {
	*out = *in
	in.AuthToken.DeepCopyInto(&out.AuthToken)
	if in.Model != nil {
		in, out := &in.Model, &out.Model
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AnthropicConfig.
func (in *AnthropicConfig) DeepCopy() *AnthropicConfig {
	if in == nil {
		return nil
	}
	out := new(AnthropicConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthHeaderOverride) DeepCopyInto(out *AuthHeaderOverride) {
	*out = *in
	if in.Prefix != nil {
		in, out := &in.Prefix, &out.Prefix
		*out = new(string)
		**out = **in
	}
	if in.HeaderName != nil {
		in, out := &in.HeaderName, &out.HeaderName
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthHeaderOverride.
func (in *AuthHeaderOverride) DeepCopy() *AuthHeaderOverride {
	if in == nil {
		return nil
	}
	out := new(AuthHeaderOverride)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AwsAuth) DeepCopyInto(out *AwsAuth) {
	*out = *in
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AwsAuth.
func (in *AwsAuth) DeepCopy() *AwsAuth {
	if in == nil {
		return nil
	}
	out := new(AwsAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AwsBackend) DeepCopyInto(out *AwsBackend) {
	*out = *in
	if in.Auth != nil {
		in, out := &in.Auth, &out.Auth
		*out = new(AwsAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.Lambda != nil {
		in, out := &in.Lambda, &out.Lambda
		*out = new(AwsLambda)
		**out = **in
	}
	if in.Region != nil {
		in, out := &in.Region, &out.Region
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AwsBackend.
func (in *AwsBackend) DeepCopy() *AwsBackend {
	if in == nil {
		return nil
	}
	out := new(AwsBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AwsLambda) DeepCopyInto(out *AwsLambda) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AwsLambda.
func (in *AwsLambda) DeepCopy() *AwsLambda {
	if in == nil {
		return nil
	}
	out := new(AwsLambda)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AzureOpenAIConfig) DeepCopyInto(out *AzureOpenAIConfig) {
	*out = *in
	in.AuthToken.DeepCopyInto(&out.AuthToken)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AzureOpenAIConfig.
func (in *AzureOpenAIConfig) DeepCopy() *AzureOpenAIConfig {
	if in == nil {
		return nil
	}
	out := new(AzureOpenAIConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Backend) DeepCopyInto(out *Backend) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Backend.
func (in *Backend) DeepCopy() *Backend {
	if in == nil {
		return nil
	}
	out := new(Backend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *Backend) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendConfigPolicy) DeepCopyInto(out *BackendConfigPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendConfigPolicy.
func (in *BackendConfigPolicy) DeepCopy() *BackendConfigPolicy {
	if in == nil {
		return nil
	}
	out := new(BackendConfigPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendConfigPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendConfigPolicyList) DeepCopyInto(out *BackendConfigPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]BackendConfigPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendConfigPolicyList.
func (in *BackendConfigPolicyList) DeepCopy() *BackendConfigPolicyList {
	if in == nil {
		return nil
	}
	out := new(BackendConfigPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendConfigPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendConfigPolicySpec) DeepCopyInto(out *BackendConfigPolicySpec) {
	*out = *in
	if in.TargetRefs != nil {
		in, out := &in.TargetRefs, &out.TargetRefs
		*out = make([]LocalPolicyTargetReference, len(*in))
		copy(*out, *in)
	}
	if in.TargetSelectors != nil {
		in, out := &in.TargetSelectors, &out.TargetSelectors
		*out = make([]LocalPolicyTargetSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ConnectTimeout != nil {
		in, out := &in.ConnectTimeout, &out.ConnectTimeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.PerConnectionBufferLimitBytes != nil {
		in, out := &in.PerConnectionBufferLimitBytes, &out.PerConnectionBufferLimitBytes
		*out = new(int)
		**out = **in
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.CommonHttpProtocolOptions != nil {
		in, out := &in.CommonHttpProtocolOptions, &out.CommonHttpProtocolOptions
		*out = new(CommonHttpProtocolOptions)
		(*in).DeepCopyInto(*out)
	}
	if in.Http1ProtocolOptions != nil {
		in, out := &in.Http1ProtocolOptions, &out.Http1ProtocolOptions
		*out = new(Http1ProtocolOptions)
		(*in).DeepCopyInto(*out)
	}
	if in.SSLConfig != nil {
		in, out := &in.SSLConfig, &out.SSLConfig
		*out = new(SSLConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendConfigPolicySpec.
func (in *BackendConfigPolicySpec) DeepCopy() *BackendConfigPolicySpec {
	if in == nil {
		return nil
	}
	out := new(BackendConfigPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendList) DeepCopyInto(out *BackendList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Backend, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendList.
func (in *BackendList) DeepCopy() *BackendList {
	if in == nil {
		return nil
	}
	out := new(BackendList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BackendList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendSpec) DeepCopyInto(out *BackendSpec) {
	*out = *in
	if in.AI != nil {
		in, out := &in.AI, &out.AI
		*out = new(AIBackend)
		(*in).DeepCopyInto(*out)
	}
	if in.Aws != nil {
		in, out := &in.Aws, &out.Aws
		*out = new(AwsBackend)
		(*in).DeepCopyInto(*out)
	}
	if in.Static != nil {
		in, out := &in.Static, &out.Static
		*out = new(StaticBackend)
		(*in).DeepCopyInto(*out)
	}
	if in.DynamicForwardProxy != nil {
		in, out := &in.DynamicForwardProxy, &out.DynamicForwardProxy
		*out = new(DynamicForwardProxyBackend)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendSpec.
func (in *BackendSpec) DeepCopy() *BackendSpec {
	if in == nil {
		return nil
	}
	out := new(BackendSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendStatus) DeepCopyInto(out *BackendStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendStatus.
func (in *BackendStatus) DeepCopy() *BackendStatus {
	if in == nil {
		return nil
	}
	out := new(BackendStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BodyTransformation) DeepCopyInto(out *BodyTransformation) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(InjaTemplate)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BodyTransformation.
func (in *BodyTransformation) DeepCopy() *BodyTransformation {
	if in == nil {
		return nil
	}
	out := new(BodyTransformation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BufferSettings) DeepCopyInto(out *BufferSettings) {
	*out = *in
	if in.AllowPartialMessage != nil {
		in, out := &in.AllowPartialMessage, &out.AllowPartialMessage
		*out = new(bool)
		**out = **in
	}
	if in.PackAsBytes != nil {
		in, out := &in.PackAsBytes, &out.PackAsBytes
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BufferSettings.
func (in *BufferSettings) DeepCopy() *BufferSettings {
	if in == nil {
		return nil
	}
	out := new(BufferSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CELFilter) DeepCopyInto(out *CELFilter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CELFilter.
func (in *CELFilter) DeepCopy() *CELFilter {
	if in == nil {
		return nil
	}
	out := new(CELFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CommonHttpProtocolOptions) DeepCopyInto(out *CommonHttpProtocolOptions) {
	*out = *in
	if in.IdleTimeout != nil {
		in, out := &in.IdleTimeout, &out.IdleTimeout
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.MaxHeadersCount != nil {
		in, out := &in.MaxHeadersCount, &out.MaxHeadersCount
		*out = new(int)
		**out = **in
	}
	if in.MaxStreamDuration != nil {
		in, out := &in.MaxStreamDuration, &out.MaxStreamDuration
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.HeadersWithUnderscoresAction != nil {
		in, out := &in.HeadersWithUnderscoresAction, &out.HeadersWithUnderscoresAction
		*out = new(HeadersWithUnderscoresAction)
		**out = **in
	}
	if in.MaxRequestsPerConnection != nil {
		in, out := &in.MaxRequestsPerConnection, &out.MaxRequestsPerConnection
		*out = new(int)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CommonHttpProtocolOptions.
func (in *CommonHttpProtocolOptions) DeepCopy() *CommonHttpProtocolOptions {
	if in == nil {
		return nil
	}
	out := new(CommonHttpProtocolOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ComparisonFilter) DeepCopyInto(out *ComparisonFilter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ComparisonFilter.
func (in *ComparisonFilter) DeepCopy() *ComparisonFilter {
	if in == nil {
		return nil
	}
	out := new(ComparisonFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CorsPolicy) DeepCopyInto(out *CorsPolicy) {
	*out = *in
	if in.HTTPCORSFilter != nil {
		in, out := &in.HTTPCORSFilter, &out.HTTPCORSFilter
		*out = new(apisv1.HTTPCORSFilter)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CorsPolicy.
func (in *CorsPolicy) DeepCopy() *CorsPolicy {
	if in == nil {
		return nil
	}
	out := new(CorsPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomLabel) DeepCopyInto(out *CustomLabel) {
	*out = *in
	if in.MetadataNamespace != nil {
		in, out := &in.MetadataNamespace, &out.MetadataNamespace
		*out = new(string)
		**out = **in
	}
	if in.KeyDelimiter != nil {
		in, out := &in.KeyDelimiter, &out.KeyDelimiter
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomLabel.
func (in *CustomLabel) DeepCopy() *CustomLabel {
	if in == nil {
		return nil
	}
	out := new(CustomLabel)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponse) DeepCopyInto(out *CustomResponse) {
	*out = *in
	if in.Message != nil {
		in, out := &in.Message, &out.Message
		*out = new(string)
		**out = **in
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponse.
func (in *CustomResponse) DeepCopy() *CustomResponse {
	if in == nil {
		return nil
	}
	out := new(CustomResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DirectResponse) DeepCopyInto(out *DirectResponse) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DirectResponse.
func (in *DirectResponse) DeepCopy() *DirectResponse {
	if in == nil {
		return nil
	}
	out := new(DirectResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DirectResponse) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DirectResponseList) DeepCopyInto(out *DirectResponseList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]DirectResponse, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DirectResponseList.
func (in *DirectResponseList) DeepCopy() *DirectResponseList {
	if in == nil {
		return nil
	}
	out := new(DirectResponseList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *DirectResponseList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DirectResponseSpec) DeepCopyInto(out *DirectResponseSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DirectResponseSpec.
func (in *DirectResponseSpec) DeepCopy() *DirectResponseSpec {
	if in == nil {
		return nil
	}
	out := new(DirectResponseSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DirectResponseStatus) DeepCopyInto(out *DirectResponseStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DirectResponseStatus.
func (in *DirectResponseStatus) DeepCopy() *DirectResponseStatus {
	if in == nil {
		return nil
	}
	out := new(DirectResponseStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DurationFilter) DeepCopyInto(out *DurationFilter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DurationFilter.
func (in *DurationFilter) DeepCopy() *DurationFilter {
	if in == nil {
		return nil
	}
	out := new(DurationFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DynamicForwardProxyBackend) DeepCopyInto(out *DynamicForwardProxyBackend) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DynamicForwardProxyBackend.
func (in *DynamicForwardProxyBackend) DeepCopy() *DynamicForwardProxyBackend {
	if in == nil {
		return nil
	}
	out := new(DynamicForwardProxyBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyBootstrap) DeepCopyInto(out *EnvoyBootstrap) {
	*out = *in
	if in.LogLevel != nil {
		in, out := &in.LogLevel, &out.LogLevel
		*out = new(string)
		**out = **in
	}
	if in.ComponentLogLevels != nil {
		in, out := &in.ComponentLogLevels, &out.ComponentLogLevels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyBootstrap.
func (in *EnvoyBootstrap) DeepCopy() *EnvoyBootstrap {
	if in == nil {
		return nil
	}
	out := new(EnvoyBootstrap)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyContainer) DeepCopyInto(out *EnvoyContainer) {
	*out = *in
	if in.Bootstrap != nil {
		in, out := &in.Bootstrap, &out.Bootstrap
		*out = new(EnvoyBootstrap)
		(*in).DeepCopyInto(*out)
	}
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(Image)
		(*in).DeepCopyInto(*out)
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(v1.SecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(v1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyContainer.
func (in *EnvoyContainer) DeepCopy() *EnvoyContainer {
	if in == nil {
		return nil
	}
	out := new(EnvoyContainer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtAuthPolicy) DeepCopyInto(out *ExtAuthPolicy) {
	*out = *in
	if in.ExtensionRef != nil {
		in, out := &in.ExtensionRef, &out.ExtensionRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
	if in.WithRequestBody != nil {
		in, out := &in.WithRequestBody, &out.WithRequestBody
		*out = new(BufferSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.ContextExtensions != nil {
		in, out := &in.ContextExtensions, &out.ContextExtensions
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtAuthPolicy.
func (in *ExtAuthPolicy) DeepCopy() *ExtAuthPolicy {
	if in == nil {
		return nil
	}
	out := new(ExtAuthPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtAuthProvider) DeepCopyInto(out *ExtAuthProvider) {
	*out = *in
	if in.GrpcService != nil {
		in, out := &in.GrpcService, &out.GrpcService
		*out = new(ExtGrpcService)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtAuthProvider.
func (in *ExtAuthProvider) DeepCopy() *ExtAuthProvider {
	if in == nil {
		return nil
	}
	out := new(ExtAuthProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtGrpcService) DeepCopyInto(out *ExtGrpcService) {
	*out = *in
	if in.BackendRef != nil {
		in, out := &in.BackendRef, &out.BackendRef
		*out = new(apisv1.BackendRef)
		(*in).DeepCopyInto(*out)
	}
	if in.Authority != nil {
		in, out := &in.Authority, &out.Authority
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtGrpcService.
func (in *ExtGrpcService) DeepCopy() *ExtGrpcService {
	if in == nil {
		return nil
	}
	out := new(ExtGrpcService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProcPolicy) DeepCopyInto(out *ExtProcPolicy) {
	*out = *in
	if in.ExtensionRef != nil {
		in, out := &in.ExtensionRef, &out.ExtensionRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
	if in.ProcessingMode != nil {
		in, out := &in.ProcessingMode, &out.ProcessingMode
		*out = new(ProcessingMode)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProcPolicy.
func (in *ExtProcPolicy) DeepCopy() *ExtProcPolicy {
	if in == nil {
		return nil
	}
	out := new(ExtProcPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProcProvider) DeepCopyInto(out *ExtProcProvider) {
	*out = *in
	if in.GrpcService != nil {
		in, out := &in.GrpcService, &out.GrpcService
		*out = new(ExtGrpcService)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProcProvider.
func (in *ExtProcProvider) DeepCopy() *ExtProcProvider {
	if in == nil {
		return nil
	}
	out := new(ExtProcProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FieldDefault) DeepCopyInto(out *FieldDefault) {
	*out = *in
	if in.Override != nil {
		in, out := &in.Override, &out.Override
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FieldDefault.
func (in *FieldDefault) DeepCopy() *FieldDefault {
	if in == nil {
		return nil
	}
	out := new(FieldDefault)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileSink) DeepCopyInto(out *FileSink) {
	*out = *in
	if in.JsonFormat != nil {
		in, out := &in.JsonFormat, &out.JsonFormat
		*out = new(runtime.RawExtension)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileSink.
func (in *FileSink) DeepCopy() *FileSink {
	if in == nil {
		return nil
	}
	out := new(FileSink)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FilterType) DeepCopyInto(out *FilterType) {
	*out = *in
	if in.StatusCodeFilter != nil {
		in, out := &in.StatusCodeFilter, &out.StatusCodeFilter
		*out = new(StatusCodeFilter)
		**out = **in
	}
	if in.DurationFilter != nil {
		in, out := &in.DurationFilter, &out.DurationFilter
		*out = new(DurationFilter)
		**out = **in
	}
	if in.HeaderFilter != nil {
		in, out := &in.HeaderFilter, &out.HeaderFilter
		*out = new(HeaderFilter)
		(*in).DeepCopyInto(*out)
	}
	if in.ResponseFlagFilter != nil {
		in, out := &in.ResponseFlagFilter, &out.ResponseFlagFilter
		*out = new(ResponseFlagFilter)
		(*in).DeepCopyInto(*out)
	}
	if in.GrpcStatusFilter != nil {
		in, out := &in.GrpcStatusFilter, &out.GrpcStatusFilter
		*out = new(GrpcStatusFilter)
		(*in).DeepCopyInto(*out)
	}
	if in.CELFilter != nil {
		in, out := &in.CELFilter, &out.CELFilter
		*out = new(CELFilter)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FilterType.
func (in *FilterType) DeepCopy() *FilterType {
	if in == nil {
		return nil
	}
	out := new(FilterType)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayExtension) DeepCopyInto(out *GatewayExtension) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayExtension.
func (in *GatewayExtension) DeepCopy() *GatewayExtension {
	if in == nil {
		return nil
	}
	out := new(GatewayExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GatewayExtension) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayExtensionList) DeepCopyInto(out *GatewayExtensionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]GatewayExtension, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayExtensionList.
func (in *GatewayExtensionList) DeepCopy() *GatewayExtensionList {
	if in == nil {
		return nil
	}
	out := new(GatewayExtensionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GatewayExtensionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayExtensionSpec) DeepCopyInto(out *GatewayExtensionSpec) {
	*out = *in
	if in.ExtAuth != nil {
		in, out := &in.ExtAuth, &out.ExtAuth
		*out = new(ExtAuthProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtProc != nil {
		in, out := &in.ExtProc, &out.ExtProc
		*out = new(ExtProcProvider)
		(*in).DeepCopyInto(*out)
	}
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimitProvider)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayExtensionSpec.
func (in *GatewayExtensionSpec) DeepCopy() *GatewayExtensionSpec {
	if in == nil {
		return nil
	}
	out := new(GatewayExtensionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayExtensionStatus) DeepCopyInto(out *GatewayExtensionStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayExtensionStatus.
func (in *GatewayExtensionStatus) DeepCopy() *GatewayExtensionStatus {
	if in == nil {
		return nil
	}
	out := new(GatewayExtensionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayParameters) DeepCopyInto(out *GatewayParameters) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayParameters.
func (in *GatewayParameters) DeepCopy() *GatewayParameters {
	if in == nil {
		return nil
	}
	out := new(GatewayParameters)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GatewayParameters) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayParametersList) DeepCopyInto(out *GatewayParametersList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]GatewayParameters, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayParametersList.
func (in *GatewayParametersList) DeepCopy() *GatewayParametersList {
	if in == nil {
		return nil
	}
	out := new(GatewayParametersList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *GatewayParametersList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayParametersSpec) DeepCopyInto(out *GatewayParametersSpec) {
	*out = *in
	if in.Kube != nil {
		in, out := &in.Kube, &out.Kube
		*out = new(KubernetesProxyConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.SelfManaged != nil {
		in, out := &in.SelfManaged, &out.SelfManaged
		*out = new(SelfManagedGateway)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayParametersSpec.
func (in *GatewayParametersSpec) DeepCopy() *GatewayParametersSpec {
	if in == nil {
		return nil
	}
	out := new(GatewayParametersSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GatewayParametersStatus) DeepCopyInto(out *GatewayParametersStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GatewayParametersStatus.
func (in *GatewayParametersStatus) DeepCopy() *GatewayParametersStatus {
	if in == nil {
		return nil
	}
	out := new(GatewayParametersStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GeminiConfig) DeepCopyInto(out *GeminiConfig) {
	*out = *in
	in.AuthToken.DeepCopyInto(&out.AuthToken)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GeminiConfig.
func (in *GeminiConfig) DeepCopy() *GeminiConfig {
	if in == nil {
		return nil
	}
	out := new(GeminiConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GracefulShutdownSpec) DeepCopyInto(out *GracefulShutdownSpec) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	if in.SleepTimeSeconds != nil {
		in, out := &in.SleepTimeSeconds, &out.SleepTimeSeconds
		*out = new(int)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GracefulShutdownSpec.
func (in *GracefulShutdownSpec) DeepCopy() *GracefulShutdownSpec {
	if in == nil {
		return nil
	}
	out := new(GracefulShutdownSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GrpcService) DeepCopyInto(out *GrpcService) {
	*out = *in
	if in.BackendRef != nil {
		in, out := &in.BackendRef, &out.BackendRef
		*out = new(apisv1.BackendRef)
		(*in).DeepCopyInto(*out)
	}
	if in.AdditionalRequestHeadersToLog != nil {
		in, out := &in.AdditionalRequestHeadersToLog, &out.AdditionalRequestHeadersToLog
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AdditionalResponseHeadersToLog != nil {
		in, out := &in.AdditionalResponseHeadersToLog, &out.AdditionalResponseHeadersToLog
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AdditionalResponseTrailersToLog != nil {
		in, out := &in.AdditionalResponseTrailersToLog, &out.AdditionalResponseTrailersToLog
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GrpcService.
func (in *GrpcService) DeepCopy() *GrpcService {
	if in == nil {
		return nil
	}
	out := new(GrpcService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GrpcStatusFilter) DeepCopyInto(out *GrpcStatusFilter) {
	*out = *in
	if in.Statuses != nil {
		in, out := &in.Statuses, &out.Statuses
		*out = make([]GrpcStatus, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GrpcStatusFilter.
func (in *GrpcStatusFilter) DeepCopy() *GrpcStatusFilter {
	if in == nil {
		return nil
	}
	out := new(GrpcStatusFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPListenerPolicy) DeepCopyInto(out *HTTPListenerPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPListenerPolicy.
func (in *HTTPListenerPolicy) DeepCopy() *HTTPListenerPolicy {
	if in == nil {
		return nil
	}
	out := new(HTTPListenerPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HTTPListenerPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPListenerPolicyList) DeepCopyInto(out *HTTPListenerPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]HTTPListenerPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPListenerPolicyList.
func (in *HTTPListenerPolicyList) DeepCopy() *HTTPListenerPolicyList {
	if in == nil {
		return nil
	}
	out := new(HTTPListenerPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *HTTPListenerPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPListenerPolicySpec) DeepCopyInto(out *HTTPListenerPolicySpec) {
	*out = *in
	if in.TargetRefs != nil {
		in, out := &in.TargetRefs, &out.TargetRefs
		*out = make([]LocalPolicyTargetReference, len(*in))
		copy(*out, *in)
	}
	if in.TargetSelectors != nil {
		in, out := &in.TargetSelectors, &out.TargetSelectors
		*out = make([]LocalPolicyTargetSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.AccessLog != nil {
		in, out := &in.AccessLog, &out.AccessLog
		*out = make([]AccessLog, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.UpgradeConfig != nil {
		in, out := &in.UpgradeConfig, &out.UpgradeConfig
		*out = new(UpgradeConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPListenerPolicySpec.
func (in *HTTPListenerPolicySpec) DeepCopy() *HTTPListenerPolicySpec {
	if in == nil {
		return nil
	}
	out := new(HTTPListenerPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderFilter) DeepCopyInto(out *HeaderFilter) {
	*out = *in
	in.Header.DeepCopyInto(&out.Header)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderFilter.
func (in *HeaderFilter) DeepCopy() *HeaderFilter {
	if in == nil {
		return nil
	}
	out := new(HeaderFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderTransformation) DeepCopyInto(out *HeaderTransformation) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderTransformation.
func (in *HeaderTransformation) DeepCopy() *HeaderTransformation {
	if in == nil {
		return nil
	}
	out := new(HeaderTransformation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Host) DeepCopyInto(out *Host) {
	*out = *in
	if in.InsecureSkipVerify != nil {
		in, out := &in.InsecureSkipVerify, &out.InsecureSkipVerify
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Host.
func (in *Host) DeepCopy() *Host {
	if in == nil {
		return nil
	}
	out := new(Host)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Http1ProtocolOptions) DeepCopyInto(out *Http1ProtocolOptions) {
	*out = *in
	if in.EnableTrailers != nil {
		in, out := &in.EnableTrailers, &out.EnableTrailers
		*out = new(bool)
		**out = **in
	}
	if in.HeaderFormat != nil {
		in, out := &in.HeaderFormat, &out.HeaderFormat
		*out = new(HeaderFormat)
		**out = **in
	}
	if in.OverrideStreamErrorOnInvalidHttpMessage != nil {
		in, out := &in.OverrideStreamErrorOnInvalidHttpMessage, &out.OverrideStreamErrorOnInvalidHttpMessage
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Http1ProtocolOptions.
func (in *Http1ProtocolOptions) DeepCopy() *Http1ProtocolOptions {
	if in == nil {
		return nil
	}
	out := new(Http1ProtocolOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Image) DeepCopyInto(out *Image) {
	*out = *in
	if in.Registry != nil {
		in, out := &in.Registry, &out.Registry
		*out = new(string)
		**out = **in
	}
	if in.Repository != nil {
		in, out := &in.Repository, &out.Repository
		*out = new(string)
		**out = **in
	}
	if in.Tag != nil {
		in, out := &in.Tag, &out.Tag
		*out = new(string)
		**out = **in
	}
	if in.Digest != nil {
		in, out := &in.Digest, &out.Digest
		*out = new(string)
		**out = **in
	}
	if in.PullPolicy != nil {
		in, out := &in.PullPolicy, &out.PullPolicy
		*out = new(v1.PullPolicy)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Image.
func (in *Image) DeepCopy() *Image {
	if in == nil {
		return nil
	}
	out := new(Image)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IstioContainer) DeepCopyInto(out *IstioContainer) {
	*out = *in
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(Image)
		(*in).DeepCopyInto(*out)
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(v1.SecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(v1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
	if in.LogLevel != nil {
		in, out := &in.LogLevel, &out.LogLevel
		*out = new(string)
		**out = **in
	}
	if in.IstioDiscoveryAddress != nil {
		in, out := &in.IstioDiscoveryAddress, &out.IstioDiscoveryAddress
		*out = new(string)
		**out = **in
	}
	if in.IstioMetaMeshId != nil {
		in, out := &in.IstioMetaMeshId, &out.IstioMetaMeshId
		*out = new(string)
		**out = **in
	}
	if in.IstioMetaClusterId != nil {
		in, out := &in.IstioMetaClusterId, &out.IstioMetaClusterId
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IstioContainer.
func (in *IstioContainer) DeepCopy() *IstioContainer {
	if in == nil {
		return nil
	}
	out := new(IstioContainer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IstioIntegration) DeepCopyInto(out *IstioIntegration) {
	*out = *in
	if in.IstioProxyContainer != nil {
		in, out := &in.IstioProxyContainer, &out.IstioProxyContainer
		*out = new(IstioContainer)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomSidecars != nil {
		in, out := &in.CustomSidecars, &out.CustomSidecars
		*out = make([]v1.Container, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IstioIntegration.
func (in *IstioIntegration) DeepCopy() *IstioIntegration {
	if in == nil {
		return nil
	}
	out := new(IstioIntegration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubernetesProxyConfig) DeepCopyInto(out *KubernetesProxyConfig) {
	*out = *in
	if in.Deployment != nil {
		in, out := &in.Deployment, &out.Deployment
		*out = new(ProxyDeployment)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyContainer != nil {
		in, out := &in.EnvoyContainer, &out.EnvoyContainer
		*out = new(EnvoyContainer)
		(*in).DeepCopyInto(*out)
	}
	if in.SdsContainer != nil {
		in, out := &in.SdsContainer, &out.SdsContainer
		*out = new(SdsContainer)
		(*in).DeepCopyInto(*out)
	}
	if in.PodTemplate != nil {
		in, out := &in.PodTemplate, &out.PodTemplate
		*out = new(Pod)
		(*in).DeepCopyInto(*out)
	}
	if in.Service != nil {
		in, out := &in.Service, &out.Service
		*out = new(Service)
		(*in).DeepCopyInto(*out)
	}
	if in.ServiceAccount != nil {
		in, out := &in.ServiceAccount, &out.ServiceAccount
		*out = new(ServiceAccount)
		(*in).DeepCopyInto(*out)
	}
	if in.Istio != nil {
		in, out := &in.Istio, &out.Istio
		*out = new(IstioIntegration)
		(*in).DeepCopyInto(*out)
	}
	if in.Stats != nil {
		in, out := &in.Stats, &out.Stats
		*out = new(StatsConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.AiExtension != nil {
		in, out := &in.AiExtension, &out.AiExtension
		*out = new(AiExtension)
		(*in).DeepCopyInto(*out)
	}
	if in.AgentGateway != nil {
		in, out := &in.AgentGateway, &out.AgentGateway
		*out = new(AgentGateway)
		(*in).DeepCopyInto(*out)
	}
	if in.FloatingUserId != nil {
		in, out := &in.FloatingUserId, &out.FloatingUserId
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubernetesProxyConfig.
func (in *KubernetesProxyConfig) DeepCopy() *KubernetesProxyConfig {
	if in == nil {
		return nil
	}
	out := new(KubernetesProxyConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LLMProvider) DeepCopyInto(out *LLMProvider) {
	*out = *in
	in.Provider.DeepCopyInto(&out.Provider)
	if in.HostOverride != nil {
		in, out := &in.HostOverride, &out.HostOverride
		*out = new(Host)
		(*in).DeepCopyInto(*out)
	}
	if in.PathOverride != nil {
		in, out := &in.PathOverride, &out.PathOverride
		*out = new(PathOverride)
		(*in).DeepCopyInto(*out)
	}
	if in.AuthHeaderOverride != nil {
		in, out := &in.AuthHeaderOverride, &out.AuthHeaderOverride
		*out = new(AuthHeaderOverride)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LLMProvider.
func (in *LLMProvider) DeepCopy() *LLMProvider {
	if in == nil {
		return nil
	}
	out := new(LLMProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalPolicyTargetReference) DeepCopyInto(out *LocalPolicyTargetReference) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalPolicyTargetReference.
func (in *LocalPolicyTargetReference) DeepCopy() *LocalPolicyTargetReference {
	if in == nil {
		return nil
	}
	out := new(LocalPolicyTargetReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalPolicyTargetReferenceWithSectionName) DeepCopyInto(out *LocalPolicyTargetReferenceWithSectionName) {
	*out = *in
	out.LocalPolicyTargetReference = in.LocalPolicyTargetReference
	if in.SectionName != nil {
		in, out := &in.SectionName, &out.SectionName
		*out = new(apisv1.SectionName)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalPolicyTargetReferenceWithSectionName.
func (in *LocalPolicyTargetReferenceWithSectionName) DeepCopy() *LocalPolicyTargetReferenceWithSectionName {
	if in == nil {
		return nil
	}
	out := new(LocalPolicyTargetReferenceWithSectionName)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalPolicyTargetSelector) DeepCopyInto(out *LocalPolicyTargetSelector) {
	*out = *in
	if in.MatchLabels != nil {
		in, out := &in.MatchLabels, &out.MatchLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalPolicyTargetSelector.
func (in *LocalPolicyTargetSelector) DeepCopy() *LocalPolicyTargetSelector {
	if in == nil {
		return nil
	}
	out := new(LocalPolicyTargetSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalRateLimitPolicy) DeepCopyInto(out *LocalRateLimitPolicy) {
	*out = *in
	if in.TokenBucket != nil {
		in, out := &in.TokenBucket, &out.TokenBucket
		*out = new(TokenBucket)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalRateLimitPolicy.
func (in *LocalRateLimitPolicy) DeepCopy() *LocalRateLimitPolicy {
	if in == nil {
		return nil
	}
	out := new(LocalRateLimitPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Message) DeepCopyInto(out *Message) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Message.
func (in *Message) DeepCopy() *Message {
	if in == nil {
		return nil
	}
	out := new(Message)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Moderation) DeepCopyInto(out *Moderation) {
	*out = *in
	if in.OpenAIModeration != nil {
		in, out := &in.OpenAIModeration, &out.OpenAIModeration
		*out = new(OpenAIConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Moderation.
func (in *Moderation) DeepCopy() *Moderation {
	if in == nil {
		return nil
	}
	out := new(Moderation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MultiPoolConfig) DeepCopyInto(out *MultiPoolConfig) {
	*out = *in
	if in.Priorities != nil {
		in, out := &in.Priorities, &out.Priorities
		*out = make([]Priority, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MultiPoolConfig.
func (in *MultiPoolConfig) DeepCopy() *MultiPoolConfig {
	if in == nil {
		return nil
	}
	out := new(MultiPoolConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OpenAIConfig) DeepCopyInto(out *OpenAIConfig) {
	*out = *in
	in.AuthToken.DeepCopyInto(&out.AuthToken)
	if in.Model != nil {
		in, out := &in.Model, &out.Model
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OpenAIConfig.
func (in *OpenAIConfig) DeepCopy() *OpenAIConfig {
	if in == nil {
		return nil
	}
	out := new(OpenAIConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PathOverride) DeepCopyInto(out *PathOverride) {
	*out = *in
	if in.FullPath != nil {
		in, out := &in.FullPath, &out.FullPath
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PathOverride.
func (in *PathOverride) DeepCopy() *PathOverride {
	if in == nil {
		return nil
	}
	out := new(PathOverride)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Pod) DeepCopyInto(out *Pod) {
	*out = *in
	if in.ExtraLabels != nil {
		in, out := &in.ExtraLabels, &out.ExtraLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.ExtraAnnotations != nil {
		in, out := &in.ExtraAnnotations, &out.ExtraAnnotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(v1.PodSecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.ImagePullSecrets != nil {
		in, out := &in.ImagePullSecrets, &out.ImagePullSecrets
		*out = make([]v1.LocalObjectReference, len(*in))
		copy(*out, *in)
	}
	if in.NodeSelector != nil {
		in, out := &in.NodeSelector, &out.NodeSelector
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Affinity != nil {
		in, out := &in.Affinity, &out.Affinity
		*out = new(v1.Affinity)
		(*in).DeepCopyInto(*out)
	}
	if in.Tolerations != nil {
		in, out := &in.Tolerations, &out.Tolerations
		*out = make([]v1.Toleration, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.GracefulShutdown != nil {
		in, out := &in.GracefulShutdown, &out.GracefulShutdown
		*out = new(GracefulShutdownSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.TerminationGracePeriodSeconds != nil {
		in, out := &in.TerminationGracePeriodSeconds, &out.TerminationGracePeriodSeconds
		*out = new(int)
		**out = **in
	}
	if in.ReadinessProbe != nil {
		in, out := &in.ReadinessProbe, &out.ReadinessProbe
		*out = new(v1.Probe)
		(*in).DeepCopyInto(*out)
	}
	if in.LivenessProbe != nil {
		in, out := &in.LivenessProbe, &out.LivenessProbe
		*out = new(v1.Probe)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Pod.
func (in *Pod) DeepCopy() *Pod {
	if in == nil {
		return nil
	}
	out := new(Pod)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyAncestorStatus) DeepCopyInto(out *PolicyAncestorStatus) {
	*out = *in
	in.AncestorRef.DeepCopyInto(&out.AncestorRef)
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyAncestorStatus.
func (in *PolicyAncestorStatus) DeepCopy() *PolicyAncestorStatus {
	if in == nil {
		return nil
	}
	out := new(PolicyAncestorStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PolicyStatus) DeepCopyInto(out *PolicyStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Ancestors != nil {
		in, out := &in.Ancestors, &out.Ancestors
		*out = make([]PolicyAncestorStatus, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PolicyStatus.
func (in *PolicyStatus) DeepCopy() *PolicyStatus {
	if in == nil {
		return nil
	}
	out := new(PolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Port) DeepCopyInto(out *Port) {
	*out = *in
	if in.NodePort != nil {
		in, out := &in.NodePort, &out.NodePort
		*out = new(uint16)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Port.
func (in *Port) DeepCopy() *Port {
	if in == nil {
		return nil
	}
	out := new(Port)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Priority) DeepCopyInto(out *Priority) {
	*out = *in
	if in.Pool != nil {
		in, out := &in.Pool, &out.Pool
		*out = make([]LLMProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Priority.
func (in *Priority) DeepCopy() *Priority {
	if in == nil {
		return nil
	}
	out := new(Priority)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProcessingMode) DeepCopyInto(out *ProcessingMode) {
	*out = *in
	if in.RequestHeaderMode != nil {
		in, out := &in.RequestHeaderMode, &out.RequestHeaderMode
		*out = new(string)
		**out = **in
	}
	if in.ResponseHeaderMode != nil {
		in, out := &in.ResponseHeaderMode, &out.ResponseHeaderMode
		*out = new(string)
		**out = **in
	}
	if in.RequestBodyMode != nil {
		in, out := &in.RequestBodyMode, &out.RequestBodyMode
		*out = new(string)
		**out = **in
	}
	if in.ResponseBodyMode != nil {
		in, out := &in.ResponseBodyMode, &out.ResponseBodyMode
		*out = new(string)
		**out = **in
	}
	if in.RequestTrailerMode != nil {
		in, out := &in.RequestTrailerMode, &out.RequestTrailerMode
		*out = new(string)
		**out = **in
	}
	if in.ResponseTrailerMode != nil {
		in, out := &in.ResponseTrailerMode, &out.ResponseTrailerMode
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProcessingMode.
func (in *ProcessingMode) DeepCopy() *ProcessingMode {
	if in == nil {
		return nil
	}
	out := new(ProcessingMode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PromptguardRequest) DeepCopyInto(out *PromptguardRequest) {
	*out = *in
	if in.CustomResponse != nil {
		in, out := &in.CustomResponse, &out.CustomResponse
		*out = new(CustomResponse)
		(*in).DeepCopyInto(*out)
	}
	if in.Regex != nil {
		in, out := &in.Regex, &out.Regex
		*out = new(Regex)
		(*in).DeepCopyInto(*out)
	}
	if in.Webhook != nil {
		in, out := &in.Webhook, &out.Webhook
		*out = new(Webhook)
		(*in).DeepCopyInto(*out)
	}
	if in.Moderation != nil {
		in, out := &in.Moderation, &out.Moderation
		*out = new(Moderation)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PromptguardRequest.
func (in *PromptguardRequest) DeepCopy() *PromptguardRequest {
	if in == nil {
		return nil
	}
	out := new(PromptguardRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PromptguardResponse) DeepCopyInto(out *PromptguardResponse) {
	*out = *in
	if in.Regex != nil {
		in, out := &in.Regex, &out.Regex
		*out = new(Regex)
		(*in).DeepCopyInto(*out)
	}
	if in.Webhook != nil {
		in, out := &in.Webhook, &out.Webhook
		*out = new(Webhook)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PromptguardResponse.
func (in *PromptguardResponse) DeepCopy() *PromptguardResponse {
	if in == nil {
		return nil
	}
	out := new(PromptguardResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyDeployment) DeepCopyInto(out *ProxyDeployment) {
	*out = *in
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyDeployment.
func (in *ProxyDeployment) DeepCopy() *ProxyDeployment {
	if in == nil {
		return nil
	}
	out := new(ProxyDeployment)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimit) DeepCopyInto(out *RateLimit) {
	*out = *in
	if in.Local != nil {
		in, out := &in.Local, &out.Local
		*out = new(LocalRateLimitPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.Global != nil {
		in, out := &in.Global, &out.Global
		*out = new(RateLimitPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimit.
func (in *RateLimit) DeepCopy() *RateLimit {
	if in == nil {
		return nil
	}
	out := new(RateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitDescriptor) DeepCopyInto(out *RateLimitDescriptor) {
	*out = *in
	if in.Entries != nil {
		in, out := &in.Entries, &out.Entries
		*out = make([]RateLimitDescriptorEntry, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitDescriptor.
func (in *RateLimitDescriptor) DeepCopy() *RateLimitDescriptor {
	if in == nil {
		return nil
	}
	out := new(RateLimitDescriptor)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitDescriptorEntry) DeepCopyInto(out *RateLimitDescriptorEntry) {
	*out = *in
	if in.Generic != nil {
		in, out := &in.Generic, &out.Generic
		*out = new(RateLimitDescriptorEntryGeneric)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitDescriptorEntry.
func (in *RateLimitDescriptorEntry) DeepCopy() *RateLimitDescriptorEntry {
	if in == nil {
		return nil
	}
	out := new(RateLimitDescriptorEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitDescriptorEntryGeneric) DeepCopyInto(out *RateLimitDescriptorEntryGeneric) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitDescriptorEntryGeneric.
func (in *RateLimitDescriptorEntryGeneric) DeepCopy() *RateLimitDescriptorEntryGeneric {
	if in == nil {
		return nil
	}
	out := new(RateLimitDescriptorEntryGeneric)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitPolicy) DeepCopyInto(out *RateLimitPolicy) {
	*out = *in
	if in.Descriptors != nil {
		in, out := &in.Descriptors, &out.Descriptors
		*out = make([]RateLimitDescriptor, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ExtensionRef != nil {
		in, out := &in.ExtensionRef, &out.ExtensionRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitPolicy.
func (in *RateLimitPolicy) DeepCopy() *RateLimitPolicy {
	if in == nil {
		return nil
	}
	out := new(RateLimitPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitProvider) DeepCopyInto(out *RateLimitProvider) {
	*out = *in
	if in.GrpcService != nil {
		in, out := &in.GrpcService, &out.GrpcService
		*out = new(ExtGrpcService)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitProvider.
func (in *RateLimitProvider) DeepCopy() *RateLimitProvider {
	if in == nil {
		return nil
	}
	out := new(RateLimitProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Regex) DeepCopyInto(out *Regex) {
	*out = *in
	if in.Matches != nil {
		in, out := &in.Matches, &out.Matches
		*out = make([]RegexMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Builtins != nil {
		in, out := &in.Builtins, &out.Builtins
		*out = make([]BuiltIn, len(*in))
		copy(*out, *in)
	}
	if in.Action != nil {
		in, out := &in.Action, &out.Action
		*out = new(Action)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Regex.
func (in *Regex) DeepCopy() *Regex {
	if in == nil {
		return nil
	}
	out := new(Regex)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RegexMatch) DeepCopyInto(out *RegexMatch) {
	*out = *in
	if in.Pattern != nil {
		in, out := &in.Pattern, &out.Pattern
		*out = new(string)
		**out = **in
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RegexMatch.
func (in *RegexMatch) DeepCopy() *RegexMatch {
	if in == nil {
		return nil
	}
	out := new(RegexMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResponseFlagFilter) DeepCopyInto(out *ResponseFlagFilter) {
	*out = *in
	if in.Flags != nil {
		in, out := &in.Flags, &out.Flags
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResponseFlagFilter.
func (in *ResponseFlagFilter) DeepCopy() *ResponseFlagFilter {
	if in == nil {
		return nil
	}
	out := new(ResponseFlagFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SSLConfig) DeepCopyInto(out *SSLConfig) {
	*out = *in
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
	if in.SSLFiles != nil {
		in, out := &in.SSLFiles, &out.SSLFiles
		*out = new(SSLFiles)
		**out = **in
	}
	if in.VerifySubjectAltName != nil {
		in, out := &in.VerifySubjectAltName, &out.VerifySubjectAltName
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SSLParameters != nil {
		in, out := &in.SSLParameters, &out.SSLParameters
		*out = new(SSLParameters)
		(*in).DeepCopyInto(*out)
	}
	if in.AlpnProtocols != nil {
		in, out := &in.AlpnProtocols, &out.AlpnProtocols
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowRenegotiation != nil {
		in, out := &in.AllowRenegotiation, &out.AllowRenegotiation
		*out = new(bool)
		**out = **in
	}
	if in.OneWayTLS != nil {
		in, out := &in.OneWayTLS, &out.OneWayTLS
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SSLConfig.
func (in *SSLConfig) DeepCopy() *SSLConfig {
	if in == nil {
		return nil
	}
	out := new(SSLConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SSLFiles) DeepCopyInto(out *SSLFiles) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SSLFiles.
func (in *SSLFiles) DeepCopy() *SSLFiles {
	if in == nil {
		return nil
	}
	out := new(SSLFiles)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SSLParameters) DeepCopyInto(out *SSLParameters) {
	*out = *in
	if in.TLSMinVersion != nil {
		in, out := &in.TLSMinVersion, &out.TLSMinVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.TLSMaxVersion != nil {
		in, out := &in.TLSMaxVersion, &out.TLSMaxVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.CipherSuites != nil {
		in, out := &in.CipherSuites, &out.CipherSuites
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.EcdhCurves != nil {
		in, out := &in.EcdhCurves, &out.EcdhCurves
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SSLParameters.
func (in *SSLParameters) DeepCopy() *SSLParameters {
	if in == nil {
		return nil
	}
	out := new(SSLParameters)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SdsBootstrap) DeepCopyInto(out *SdsBootstrap) {
	*out = *in
	if in.LogLevel != nil {
		in, out := &in.LogLevel, &out.LogLevel
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SdsBootstrap.
func (in *SdsBootstrap) DeepCopy() *SdsBootstrap {
	if in == nil {
		return nil
	}
	out := new(SdsBootstrap)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SdsContainer) DeepCopyInto(out *SdsContainer) {
	*out = *in
	if in.Image != nil {
		in, out := &in.Image, &out.Image
		*out = new(Image)
		(*in).DeepCopyInto(*out)
	}
	if in.SecurityContext != nil {
		in, out := &in.SecurityContext, &out.SecurityContext
		*out = new(v1.SecurityContext)
		(*in).DeepCopyInto(*out)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = new(v1.ResourceRequirements)
		(*in).DeepCopyInto(*out)
	}
	if in.Bootstrap != nil {
		in, out := &in.Bootstrap, &out.Bootstrap
		*out = new(SdsBootstrap)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SdsContainer.
func (in *SdsContainer) DeepCopy() *SdsContainer {
	if in == nil {
		return nil
	}
	out := new(SdsContainer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SelfManagedGateway) DeepCopyInto(out *SelfManagedGateway) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SelfManagedGateway.
func (in *SelfManagedGateway) DeepCopy() *SelfManagedGateway {
	if in == nil {
		return nil
	}
	out := new(SelfManagedGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Service) DeepCopyInto(out *Service) {
	*out = *in
	if in.Type != nil {
		in, out := &in.Type, &out.Type
		*out = new(v1.ServiceType)
		**out = **in
	}
	if in.ClusterIP != nil {
		in, out := &in.ClusterIP, &out.ClusterIP
		*out = new(string)
		**out = **in
	}
	if in.ExtraLabels != nil {
		in, out := &in.ExtraLabels, &out.ExtraLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.ExtraAnnotations != nil {
		in, out := &in.ExtraAnnotations, &out.ExtraAnnotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Ports != nil {
		in, out := &in.Ports, &out.Ports
		*out = make([]*Port, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(Port)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Service.
func (in *Service) DeepCopy() *Service {
	if in == nil {
		return nil
	}
	out := new(Service)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServiceAccount) DeepCopyInto(out *ServiceAccount) {
	*out = *in
	if in.ExtraLabels != nil {
		in, out := &in.ExtraLabels, &out.ExtraLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.ExtraAnnotations != nil {
		in, out := &in.ExtraAnnotations, &out.ExtraAnnotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceAccount.
func (in *ServiceAccount) DeepCopy() *ServiceAccount {
	if in == nil {
		return nil
	}
	out := new(ServiceAccount)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SingleAuthToken) DeepCopyInto(out *SingleAuthToken) {
	*out = *in
	if in.Inline != nil {
		in, out := &in.Inline, &out.Inline
		*out = new(string)
		**out = **in
	}
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(v1.LocalObjectReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SingleAuthToken.
func (in *SingleAuthToken) DeepCopy() *SingleAuthToken {
	if in == nil {
		return nil
	}
	out := new(SingleAuthToken)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StaticBackend) DeepCopyInto(out *StaticBackend) {
	*out = *in
	if in.Hosts != nil {
		in, out := &in.Hosts, &out.Hosts
		*out = make([]Host, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StaticBackend.
func (in *StaticBackend) DeepCopy() *StaticBackend {
	if in == nil {
		return nil
	}
	out := new(StaticBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatsConfig) DeepCopyInto(out *StatsConfig) {
	*out = *in
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	if in.RoutePrefixRewrite != nil {
		in, out := &in.RoutePrefixRewrite, &out.RoutePrefixRewrite
		*out = new(string)
		**out = **in
	}
	if in.EnableStatsRoute != nil {
		in, out := &in.EnableStatsRoute, &out.EnableStatsRoute
		*out = new(bool)
		**out = **in
	}
	if in.StatsRoutePrefixRewrite != nil {
		in, out := &in.StatsRoutePrefixRewrite, &out.StatsRoutePrefixRewrite
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatsConfig.
func (in *StatsConfig) DeepCopy() *StatsConfig {
	if in == nil {
		return nil
	}
	out := new(StatsConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatusCodeFilter) DeepCopyInto(out *StatusCodeFilter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatusCodeFilter.
func (in *StatusCodeFilter) DeepCopy() *StatusCodeFilter {
	if in == nil {
		return nil
	}
	out := new(StatusCodeFilter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SupportedLLMProvider) DeepCopyInto(out *SupportedLLMProvider) {
	*out = *in
	if in.OpenAI != nil {
		in, out := &in.OpenAI, &out.OpenAI
		*out = new(OpenAIConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.AzureOpenAI != nil {
		in, out := &in.AzureOpenAI, &out.AzureOpenAI
		*out = new(AzureOpenAIConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Anthropic != nil {
		in, out := &in.Anthropic, &out.Anthropic
		*out = new(AnthropicConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Gemini != nil {
		in, out := &in.Gemini, &out.Gemini
		*out = new(GeminiConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.VertexAI != nil {
		in, out := &in.VertexAI, &out.VertexAI
		*out = new(VertexAIConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SupportedLLMProvider.
func (in *SupportedLLMProvider) DeepCopy() *SupportedLLMProvider {
	if in == nil {
		return nil
	}
	out := new(SupportedLLMProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPKeepalive) DeepCopyInto(out *TCPKeepalive) {
	*out = *in
	if in.KeepAliveProbes != nil {
		in, out := &in.KeepAliveProbes, &out.KeepAliveProbes
		*out = new(int)
		**out = **in
	}
	if in.KeepAliveTime != nil {
		in, out := &in.KeepAliveTime, &out.KeepAliveTime
		*out = new(metav1.Duration)
		**out = **in
	}
	if in.KeepAliveInterval != nil {
		in, out := &in.KeepAliveInterval, &out.KeepAliveInterval
		*out = new(metav1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPKeepalive.
func (in *TCPKeepalive) DeepCopy() *TCPKeepalive {
	if in == nil {
		return nil
	}
	out := new(TCPKeepalive)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TokenBucket) DeepCopyInto(out *TokenBucket) {
	*out = *in
	if in.TokensPerFill != nil {
		in, out := &in.TokensPerFill, &out.TokensPerFill
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TokenBucket.
func (in *TokenBucket) DeepCopy() *TokenBucket {
	if in == nil {
		return nil
	}
	out := new(TokenBucket)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TrafficPolicy) DeepCopyInto(out *TrafficPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TrafficPolicy.
func (in *TrafficPolicy) DeepCopy() *TrafficPolicy {
	if in == nil {
		return nil
	}
	out := new(TrafficPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *TrafficPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TrafficPolicyList) DeepCopyInto(out *TrafficPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]TrafficPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TrafficPolicyList.
func (in *TrafficPolicyList) DeepCopy() *TrafficPolicyList {
	if in == nil {
		return nil
	}
	out := new(TrafficPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *TrafficPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TrafficPolicySpec) DeepCopyInto(out *TrafficPolicySpec) {
	*out = *in
	if in.TargetRefs != nil {
		in, out := &in.TargetRefs, &out.TargetRefs
		*out = make([]LocalPolicyTargetReferenceWithSectionName, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.TargetSelectors != nil {
		in, out := &in.TargetSelectors, &out.TargetSelectors
		*out = make([]LocalPolicyTargetSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.AI != nil {
		in, out := &in.AI, &out.AI
		*out = new(AIPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.Transformation != nil {
		in, out := &in.Transformation, &out.Transformation
		*out = new(TransformationPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtProc != nil {
		in, out := &in.ExtProc, &out.ExtProc
		*out = new(ExtProcPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtAuth != nil {
		in, out := &in.ExtAuth, &out.ExtAuth
		*out = new(ExtAuthPolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.Cors != nil {
		in, out := &in.Cors, &out.Cors
		*out = new(CorsPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TrafficPolicySpec.
func (in *TrafficPolicySpec) DeepCopy() *TrafficPolicySpec {
	if in == nil {
		return nil
	}
	out := new(TrafficPolicySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Transform) DeepCopyInto(out *Transform) {
	*out = *in
	if in.Set != nil {
		in, out := &in.Set, &out.Set
		*out = make([]HeaderTransformation, len(*in))
		copy(*out, *in)
	}
	if in.Add != nil {
		in, out := &in.Add, &out.Add
		*out = make([]HeaderTransformation, len(*in))
		copy(*out, *in)
	}
	if in.Remove != nil {
		in, out := &in.Remove, &out.Remove
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Body != nil {
		in, out := &in.Body, &out.Body
		*out = new(BodyTransformation)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Transform.
func (in *Transform) DeepCopy() *Transform {
	if in == nil {
		return nil
	}
	out := new(Transform)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TransformationPolicy) DeepCopyInto(out *TransformationPolicy) {
	*out = *in
	if in.Request != nil {
		in, out := &in.Request, &out.Request
		*out = new(Transform)
		(*in).DeepCopyInto(*out)
	}
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(Transform)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TransformationPolicy.
func (in *TransformationPolicy) DeepCopy() *TransformationPolicy {
	if in == nil {
		return nil
	}
	out := new(TransformationPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UpgradeConfig) DeepCopyInto(out *UpgradeConfig) {
	*out = *in
	if in.EnabledUpgrades != nil {
		in, out := &in.EnabledUpgrades, &out.EnabledUpgrades
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UpgradeConfig.
func (in *UpgradeConfig) DeepCopy() *UpgradeConfig {
	if in == nil {
		return nil
	}
	out := new(UpgradeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VertexAIConfig) DeepCopyInto(out *VertexAIConfig) {
	*out = *in
	in.AuthToken.DeepCopyInto(&out.AuthToken)
	if in.ModelPath != nil {
		in, out := &in.ModelPath, &out.ModelPath
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VertexAIConfig.
func (in *VertexAIConfig) DeepCopy() *VertexAIConfig {
	if in == nil {
		return nil
	}
	out := new(VertexAIConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Webhook) DeepCopyInto(out *Webhook) {
	*out = *in
	in.Host.DeepCopyInto(&out.Host)
	if in.ForwardHeaders != nil {
		in, out := &in.ForwardHeaders, &out.ForwardHeaders
		*out = make([]apisv1.HTTPHeaderMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Webhook.
func (in *Webhook) DeepCopy() *Webhook {
	if in == nil {
		return nil
	}
	out := new(Webhook)
	in.DeepCopyInto(out)
	return out
}
