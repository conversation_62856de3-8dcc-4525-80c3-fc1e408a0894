// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// ExtAuthProviderApplyConfiguration represents a declarative configuration of the ExtAuthProvider type for use
// with apply.
type ExtAuthProviderApplyConfiguration struct {
	GrpcService *ExtGrpcServiceApplyConfiguration `json:"grpcService,omitempty"`
}

// ExtAuthProviderApplyConfiguration constructs a declarative configuration of the ExtAuthProvider type for use with
// apply.
func ExtAuthProvider() *ExtAuthProviderApplyConfiguration {
	return &ExtAuthProviderApplyConfiguration{}
}

// WithGrpcService sets the GrpcService field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GrpcService field is set to the value of the last call.
func (b *ExtAuthProviderApplyConfiguration) WithGrpcService(value *ExtGrpcServiceApplyConfiguration) *ExtAuthProviderApplyConfiguration {
	b.GrpcService = value
	return b
}
