// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// LocalPolicyTargetReferenceWithSectionNameApplyConfiguration represents a declarative configuration of the LocalPolicyTargetReferenceWithSectionName type for use
// with apply.
type LocalPolicyTargetReferenceWithSectionNameApplyConfiguration struct {
	LocalPolicyTargetReferenceApplyConfiguration `json:",inline"`
	SectionName                                  *v1.SectionName `json:"sectionName,omitempty"`
}

// LocalPolicyTargetReferenceWithSectionNameApplyConfiguration constructs a declarative configuration of the LocalPolicyTargetReferenceWithSectionName type for use with
// apply.
func LocalPolicyTargetReferenceWithSectionName() *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration {
	return &LocalPolicyTargetReferenceWithSectionNameApplyConfiguration{}
}

// WithGroup sets the Group field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Group field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration) WithGroup(value v1.Group) *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration {
	b.LocalPolicyTargetReferenceApplyConfiguration.Group = &value
	return b
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration) WithKind(value v1.Kind) *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration {
	b.LocalPolicyTargetReferenceApplyConfiguration.Kind = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration) WithName(value v1.ObjectName) *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration {
	b.LocalPolicyTargetReferenceApplyConfiguration.Name = &value
	return b
}

// WithSectionName sets the SectionName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SectionName field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration) WithSectionName(value v1.SectionName) *LocalPolicyTargetReferenceWithSectionNameApplyConfiguration {
	b.SectionName = &value
	return b
}
