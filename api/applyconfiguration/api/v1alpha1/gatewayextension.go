// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	managedfields "k8s.io/apimachinery/pkg/util/managedfields"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"

	internal "github.com/kgateway-dev/kgateway/v2/api/applyconfiguration/internal"
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// GatewayExtensionApplyConfiguration represents a declarative configuration of the GatewayExtension type for use
// with apply.
type GatewayExtensionApplyConfiguration struct {
	v1.TypeMetaApplyConfiguration    `json:",inline"`
	*v1.ObjectMetaApplyConfiguration `json:"metadata,omitempty"`
	Spec                             *GatewayExtensionSpecApplyConfiguration   `json:"spec,omitempty"`
	Status                           *GatewayExtensionStatusApplyConfiguration `json:"status,omitempty"`
}

// GatewayExtension constructs a declarative configuration of the GatewayExtension type for use with
// apply.
func GatewayExtension(name, namespace string) *GatewayExtensionApplyConfiguration {
	b := &GatewayExtensionApplyConfiguration{}
	b.WithName(name)
	b.WithNamespace(namespace)
	b.WithKind("GatewayExtension")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b
}

// ExtractGatewayExtension extracts the applied configuration owned by fieldManager from
// gatewayExtension. If no managedFields are found in gatewayExtension for fieldManager, a
// GatewayExtensionApplyConfiguration is returned with only the Name, Namespace (if applicable),
// APIVersion and Kind populated. It is possible that no managed fields were found for because other
// field managers have taken ownership of all the fields previously owned by fieldManager, or because
// the fieldManager never owned fields any fields.
// gatewayExtension must be a unmodified GatewayExtension API object that was retrieved from the Kubernetes API.
// ExtractGatewayExtension provides a way to perform a extract/modify-in-place/apply workflow.
// Note that an extracted apply configuration will contain fewer fields than what the fieldManager previously
// applied if another fieldManager has updated or force applied any of the previously applied fields.
// Experimental!
func ExtractGatewayExtension(gatewayExtension *apiv1alpha1.GatewayExtension, fieldManager string) (*GatewayExtensionApplyConfiguration, error) {
	return extractGatewayExtension(gatewayExtension, fieldManager, "")
}

// ExtractGatewayExtensionStatus is the same as ExtractGatewayExtension except
// that it extracts the status subresource applied configuration.
// Experimental!
func ExtractGatewayExtensionStatus(gatewayExtension *apiv1alpha1.GatewayExtension, fieldManager string) (*GatewayExtensionApplyConfiguration, error) {
	return extractGatewayExtension(gatewayExtension, fieldManager, "status")
}

func extractGatewayExtension(gatewayExtension *apiv1alpha1.GatewayExtension, fieldManager string, subresource string) (*GatewayExtensionApplyConfiguration, error) {
	b := &GatewayExtensionApplyConfiguration{}
	err := managedfields.ExtractInto(gatewayExtension, internal.Parser().Type("com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtension"), fieldManager, b, subresource)
	if err != nil {
		return nil, err
	}
	b.WithName(gatewayExtension.Name)
	b.WithNamespace(gatewayExtension.Namespace)

	b.WithKind("GatewayExtension")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b, nil
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithKind(value string) *GatewayExtensionApplyConfiguration {
	b.TypeMetaApplyConfiguration.Kind = &value
	return b
}

// WithAPIVersion sets the APIVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the APIVersion field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithAPIVersion(value string) *GatewayExtensionApplyConfiguration {
	b.TypeMetaApplyConfiguration.APIVersion = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithName(value string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Name = &value
	return b
}

// WithGenerateName sets the GenerateName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GenerateName field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithGenerateName(value string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.GenerateName = &value
	return b
}

// WithNamespace sets the Namespace field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Namespace field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithNamespace(value string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Namespace = &value
	return b
}

// WithUID sets the UID field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UID field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithUID(value types.UID) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.UID = &value
	return b
}

// WithResourceVersion sets the ResourceVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResourceVersion field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithResourceVersion(value string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.ResourceVersion = &value
	return b
}

// WithGeneration sets the Generation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Generation field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithGeneration(value int64) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Generation = &value
	return b
}

// WithCreationTimestamp sets the CreationTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CreationTimestamp field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithCreationTimestamp(value metav1.Time) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.CreationTimestamp = &value
	return b
}

// WithDeletionTimestamp sets the DeletionTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionTimestamp field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithDeletionTimestamp(value metav1.Time) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionTimestamp = &value
	return b
}

// WithDeletionGracePeriodSeconds sets the DeletionGracePeriodSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionGracePeriodSeconds field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithDeletionGracePeriodSeconds(value int64) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionGracePeriodSeconds = &value
	return b
}

// WithLabels puts the entries into the Labels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Labels field,
// overwriting an existing map entries in Labels field with the same key.
func (b *GatewayExtensionApplyConfiguration) WithLabels(entries map[string]string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Labels == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Labels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Labels[k] = v
	}
	return b
}

// WithAnnotations puts the entries into the Annotations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Annotations field,
// overwriting an existing map entries in Annotations field with the same key.
func (b *GatewayExtensionApplyConfiguration) WithAnnotations(entries map[string]string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Annotations == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Annotations = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Annotations[k] = v
	}
	return b
}

// WithOwnerReferences adds the given value to the OwnerReferences field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the OwnerReferences field.
func (b *GatewayExtensionApplyConfiguration) WithOwnerReferences(values ...*v1.OwnerReferenceApplyConfiguration) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithOwnerReferences")
		}
		b.ObjectMetaApplyConfiguration.OwnerReferences = append(b.ObjectMetaApplyConfiguration.OwnerReferences, *values[i])
	}
	return b
}

// WithFinalizers adds the given value to the Finalizers field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Finalizers field.
func (b *GatewayExtensionApplyConfiguration) WithFinalizers(values ...string) *GatewayExtensionApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		b.ObjectMetaApplyConfiguration.Finalizers = append(b.ObjectMetaApplyConfiguration.Finalizers, values[i])
	}
	return b
}

func (b *GatewayExtensionApplyConfiguration) ensureObjectMetaApplyConfigurationExists() {
	if b.ObjectMetaApplyConfiguration == nil {
		b.ObjectMetaApplyConfiguration = &v1.ObjectMetaApplyConfiguration{}
	}
}

// WithSpec sets the Spec field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Spec field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithSpec(value *GatewayExtensionSpecApplyConfiguration) *GatewayExtensionApplyConfiguration {
	b.Spec = value
	return b
}

// WithStatus sets the Status field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Status field is set to the value of the last call.
func (b *GatewayExtensionApplyConfiguration) WithStatus(value *GatewayExtensionStatusApplyConfiguration) *GatewayExtensionApplyConfiguration {
	b.Status = value
	return b
}

// GetName retrieves the value of the Name field in the declarative configuration.
func (b *GatewayExtensionApplyConfiguration) GetName() *string {
	b.ensureObjectMetaApplyConfigurationExists()
	return b.ObjectMetaApplyConfiguration.Name
}
