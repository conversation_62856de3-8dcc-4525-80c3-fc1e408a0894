// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// PriorityApplyConfiguration represents a declarative configuration of the Priority type for use
// with apply.
type PriorityApplyConfiguration struct {
	Pool []LLMProviderApplyConfiguration `json:"pool,omitempty"`
}

// PriorityApplyConfiguration constructs a declarative configuration of the Priority type for use with
// apply.
func Priority() *PriorityApplyConfiguration {
	return &PriorityApplyConfiguration{}
}

// WithPool adds the given value to the Pool field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Pool field.
func (b *PriorityApplyConfiguration) WithPool(values ...*LLMProviderApplyConfiguration) *PriorityApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to <PERSON><PERSON><PERSON>")
		}
		b.Pool = append(b.Pool, *values[i])
	}
	return b
}
