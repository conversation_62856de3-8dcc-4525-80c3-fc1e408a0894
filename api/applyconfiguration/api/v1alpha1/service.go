// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"

	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// ServiceApplyConfiguration represents a declarative configuration of the Service type for use
// with apply.
type ServiceApplyConfiguration struct {
	Type             *v1.ServiceType     `json:"type,omitempty"`
	ClusterIP        *string             `json:"clusterIP,omitempty"`
	ExtraLabels      map[string]string   `json:"extraLabels,omitempty"`
	ExtraAnnotations map[string]string   `json:"extraAnnotations,omitempty"`
	Ports            []*apiv1alpha1.Port `json:"ports,omitempty"`
}

// ServiceApplyConfiguration constructs a declarative configuration of the Service type for use with
// apply.
func Service() *ServiceApplyConfiguration {
	return &ServiceApplyConfiguration{}
}

// WithType sets the Type field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Type field is set to the value of the last call.
func (b *ServiceApplyConfiguration) WithType(value v1.ServiceType) *ServiceApplyConfiguration {
	b.Type = &value
	return b
}

// WithClusterIP sets the ClusterIP field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ClusterIP field is set to the value of the last call.
func (b *ServiceApplyConfiguration) WithClusterIP(value string) *ServiceApplyConfiguration {
	b.ClusterIP = &value
	return b
}

// WithExtraLabels puts the entries into the ExtraLabels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ExtraLabels field,
// overwriting an existing map entries in ExtraLabels field with the same key.
func (b *ServiceApplyConfiguration) WithExtraLabels(entries map[string]string) *ServiceApplyConfiguration {
	if b.ExtraLabels == nil && len(entries) > 0 {
		b.ExtraLabels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ExtraLabels[k] = v
	}
	return b
}

// WithExtraAnnotations puts the entries into the ExtraAnnotations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ExtraAnnotations field,
// overwriting an existing map entries in ExtraAnnotations field with the same key.
func (b *ServiceApplyConfiguration) WithExtraAnnotations(entries map[string]string) *ServiceApplyConfiguration {
	if b.ExtraAnnotations == nil && len(entries) > 0 {
		b.ExtraAnnotations = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ExtraAnnotations[k] = v
	}
	return b
}

// WithPorts adds the given value to the Ports field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Ports field.
func (b *ServiceApplyConfiguration) WithPorts(values ...**apiv1alpha1.Port) *ServiceApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithPorts")
		}
		b.Ports = append(b.Ports, *values[i])
	}
	return b
}
