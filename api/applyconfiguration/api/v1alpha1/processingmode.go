// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// ProcessingModeApplyConfiguration represents a declarative configuration of the ProcessingMode type for use
// with apply.
type ProcessingModeApplyConfiguration struct {
	RequestHeaderMode   *string `json:"requestHeaderMode,omitempty"`
	ResponseHeaderMode  *string `json:"responseHeaderMode,omitempty"`
	RequestBodyMode     *string `json:"requestBodyMode,omitempty"`
	ResponseBodyMode    *string `json:"responseBodyMode,omitempty"`
	RequestTrailerMode  *string `json:"requestTrailerMode,omitempty"`
	ResponseTrailerMode *string `json:"responseTrailerMode,omitempty"`
}

// ProcessingModeApplyConfiguration constructs a declarative configuration of the ProcessingMode type for use with
// apply.
func ProcessingMode() *ProcessingModeApplyConfiguration {
	return &ProcessingModeApplyConfiguration{}
}

// WithRequestHeaderMode sets the RequestHeaderMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RequestHeaderMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithRequestHeaderMode(value string) *ProcessingModeApplyConfiguration {
	b.RequestHeaderMode = &value
	return b
}

// WithResponseHeaderMode sets the ResponseHeaderMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResponseHeaderMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithResponseHeaderMode(value string) *ProcessingModeApplyConfiguration {
	b.ResponseHeaderMode = &value
	return b
}

// WithRequestBodyMode sets the RequestBodyMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RequestBodyMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithRequestBodyMode(value string) *ProcessingModeApplyConfiguration {
	b.RequestBodyMode = &value
	return b
}

// WithResponseBodyMode sets the ResponseBodyMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResponseBodyMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithResponseBodyMode(value string) *ProcessingModeApplyConfiguration {
	b.ResponseBodyMode = &value
	return b
}

// WithRequestTrailerMode sets the RequestTrailerMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RequestTrailerMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithRequestTrailerMode(value string) *ProcessingModeApplyConfiguration {
	b.RequestTrailerMode = &value
	return b
}

// WithResponseTrailerMode sets the ResponseTrailerMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResponseTrailerMode field is set to the value of the last call.
func (b *ProcessingModeApplyConfiguration) WithResponseTrailerMode(value string) *ProcessingModeApplyConfiguration {
	b.ResponseTrailerMode = &value
	return b
}
