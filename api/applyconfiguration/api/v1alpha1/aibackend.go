// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AIBackendApplyConfiguration represents a declarative configuration of the AIBackend type for use
// with apply.
type AIBackendApplyConfiguration struct {
	LLM       *LLMProviderApplyConfiguration     `json:"llm,omitempty"`
	MultiPool *MultiPoolConfigApplyConfiguration `json:"multipool,omitempty"`
}

// AIBackendApplyConfiguration constructs a declarative configuration of the AIBackend type for use with
// apply.
func AIBackend() *AIBackendApplyConfiguration {
	return &AIBackendApplyConfiguration{}
}

// WithLLM sets the LLM field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LLM field is set to the value of the last call.
func (b *AIBackendApplyConfiguration) WithLLM(value *LLMProviderApplyConfiguration) *AIBackendApplyConfiguration {
	b.LLM = value
	return b
}

// WithMultiPool sets the MultiPool field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MultiPool field is set to the value of the last call.
func (b *AIBackendApplyConfiguration) WithMultiPool(value *MultiPoolConfigApplyConfiguration) *AIBackendApplyConfiguration {
	b.MultiPool = value
	return b
}
