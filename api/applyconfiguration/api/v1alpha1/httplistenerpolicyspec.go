// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// HTTPListenerPolicySpecApplyConfiguration represents a declarative configuration of the HTTPListenerPolicySpec type for use
// with apply.
type HTTPListenerPolicySpecApplyConfiguration struct {
	TargetRefs      []LocalPolicyTargetReferenceApplyConfiguration `json:"targetRefs,omitempty"`
	TargetSelectors []LocalPolicyTargetSelectorApplyConfiguration  `json:"targetSelectors,omitempty"`
	AccessLog       []AccessLogApplyConfiguration                  `json:"accessLog,omitempty"`
	UpgradeConfig   *UpgradeConfigApplyConfiguration               `json:"upgradeConfig,omitempty"`
}

// HTTPListenerPolicySpecApplyConfiguration constructs a declarative configuration of the HTTPListenerPolicySpec type for use with
// apply.
func HTTPListenerPolicySpec() *HTTPListenerPolicySpecApplyConfiguration {
	return &HTTPListenerPolicySpecApplyConfiguration{}
}

// WithTargetRefs adds the given value to the TargetRefs field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetRefs field.
func (b *HTTPListenerPolicySpecApplyConfiguration) WithTargetRefs(values ...*LocalPolicyTargetReferenceApplyConfiguration) *HTTPListenerPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetRefs")
		}
		b.TargetRefs = append(b.TargetRefs, *values[i])
	}
	return b
}

// WithTargetSelectors adds the given value to the TargetSelectors field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetSelectors field.
func (b *HTTPListenerPolicySpecApplyConfiguration) WithTargetSelectors(values ...*LocalPolicyTargetSelectorApplyConfiguration) *HTTPListenerPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetSelectors")
		}
		b.TargetSelectors = append(b.TargetSelectors, *values[i])
	}
	return b
}

// WithAccessLog adds the given value to the AccessLog field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AccessLog field.
func (b *HTTPListenerPolicySpecApplyConfiguration) WithAccessLog(values ...*AccessLogApplyConfiguration) *HTTPListenerPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAccessLog")
		}
		b.AccessLog = append(b.AccessLog, *values[i])
	}
	return b
}

// WithUpgradeConfig sets the UpgradeConfig field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UpgradeConfig field is set to the value of the last call.
func (b *HTTPListenerPolicySpecApplyConfiguration) WithUpgradeConfig(value *UpgradeConfigApplyConfiguration) *HTTPListenerPolicySpecApplyConfiguration {
	b.UpgradeConfig = value
	return b
}
