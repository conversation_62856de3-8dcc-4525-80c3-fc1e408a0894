// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AccessLogApplyConfiguration represents a declarative configuration of the AccessLog type for use
// with apply.
type AccessLogApplyConfiguration struct {
	FileSink    *FileSinkApplyConfiguration        `json:"fileSink,omitempty"`
	GrpcService *GrpcServiceApplyConfiguration     `json:"grpcService,omitempty"`
	Filter      *AccessLogFilterApplyConfiguration `json:"filter,omitempty"`
}

// AccessLogApplyConfiguration constructs a declarative configuration of the AccessLog type for use with
// apply.
func AccessLog() *AccessLogApplyConfiguration {
	return &AccessLogApplyConfiguration{}
}

// WithFileSink sets the FileSink field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FileSink field is set to the value of the last call.
func (b *AccessLogApplyConfiguration) WithFileSink(value *FileSinkApplyConfiguration) *AccessLogApplyConfiguration {
	b.FileSink = value
	return b
}

// WithGrpcService sets the GrpcService field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GrpcService field is set to the value of the last call.
func (b *AccessLogApplyConfiguration) WithGrpcService(value *GrpcServiceApplyConfiguration) *AccessLogApplyConfiguration {
	b.GrpcService = value
	return b
}

// WithFilter sets the Filter field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Filter field is set to the value of the last call.
func (b *AccessLogApplyConfiguration) WithFilter(value *AccessLogFilterApplyConfiguration) *AccessLogApplyConfiguration {
	b.Filter = value
	return b
}
