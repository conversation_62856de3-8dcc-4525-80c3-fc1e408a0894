// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// HostApplyConfiguration represents a declarative configuration of the Host type for use
// with apply.
type HostApplyConfiguration struct {
	Host               *string        `json:"host,omitempty"`
	Port               *v1.PortNumber `json:"port,omitempty"`
	InsecureSkipVerify *bool          `json:"insecureSkipVerify,omitempty"`
}

// HostApplyConfiguration constructs a declarative configuration of the Host type for use with
// apply.
func Host() *HostApplyConfiguration {
	return &HostApplyConfiguration{}
}

// WithHost sets the Host field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Host field is set to the value of the last call.
func (b *HostApplyConfiguration) WithHost(value string) *HostApplyConfiguration {
	b.Host = &value
	return b
}

// WithPort sets the Port field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Port field is set to the value of the last call.
func (b *HostApplyConfiguration) WithPort(value v1.PortNumber) *HostApplyConfiguration {
	b.Port = &value
	return b
}

// WithInsecureSkipVerify sets the InsecureSkipVerify field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the InsecureSkipVerify field is set to the value of the last call.
func (b *HostApplyConfiguration) WithInsecureSkipVerify(value bool) *HostApplyConfiguration {
	b.InsecureSkipVerify = &value
	return b
}
