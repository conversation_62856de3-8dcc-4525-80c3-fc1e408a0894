// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RateLimitDescriptorEntryGenericApplyConfiguration represents a declarative configuration of the RateLimitDescriptorEntryGeneric type for use
// with apply.
type RateLimitDescriptorEntryGenericApplyConfiguration struct {
	Key   *string `json:"key,omitempty"`
	Value *string `json:"value,omitempty"`
}

// RateLimitDescriptorEntryGenericApplyConfiguration constructs a declarative configuration of the RateLimitDescriptorEntryGeneric type for use with
// apply.
func RateLimitDescriptorEntryGeneric() *RateLimitDescriptorEntryGenericApplyConfiguration {
	return &RateLimitDescriptorEntryGenericApplyConfiguration{}
}

// With<PERSON><PERSON> sets the Key field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Key field is set to the value of the last call.
func (b *RateLimitDescriptorEntryGenericApplyConfiguration) WithKey(value string) *RateLimitDescriptorEntryGenericApplyConfiguration {
	b.Key = &value
	return b
}

// WithValue sets the Value field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Value field is set to the value of the last call.
func (b *RateLimitDescriptorEntryGenericApplyConfiguration) WithValue(value string) *RateLimitDescriptorEntryGenericApplyConfiguration {
	b.Value = &value
	return b
}
