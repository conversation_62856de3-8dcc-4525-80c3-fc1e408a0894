// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// CustomLabelApplyConfiguration represents a declarative configuration of the CustomLabel type for use
// with apply.
type CustomLabelApplyConfiguration struct {
	Name              *string `json:"name,omitempty"`
	MetadataNamespace *string `json:"metadataNamespace,omitempty"`
	MetdataKey        *string `json:"metadataKey,omitempty"`
	KeyDelimiter      *string `json:"keyDelimiter,omitempty"`
}

// CustomLabelApplyConfiguration constructs a declarative configuration of the CustomLabel type for use with
// apply.
func CustomLabel() *CustomLabelApplyConfiguration {
	return &CustomLabelApplyConfiguration{}
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *CustomLabelApplyConfiguration) WithName(value string) *CustomLabelApplyConfiguration {
	b.Name = &value
	return b
}

// WithMetadataNamespace sets the MetadataNamespace field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetadataNamespace field is set to the value of the last call.
func (b *CustomLabelApplyConfiguration) WithMetadataNamespace(value string) *CustomLabelApplyConfiguration {
	b.MetadataNamespace = &value
	return b
}

// WithMetdataKey sets the MetdataKey field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MetdataKey field is set to the value of the last call.
func (b *CustomLabelApplyConfiguration) WithMetdataKey(value string) *CustomLabelApplyConfiguration {
	b.MetdataKey = &value
	return b
}

// WithKeyDelimiter sets the KeyDelimiter field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the KeyDelimiter field is set to the value of the last call.
func (b *CustomLabelApplyConfiguration) WithKeyDelimiter(value string) *CustomLabelApplyConfiguration {
	b.KeyDelimiter = &value
	return b
}
