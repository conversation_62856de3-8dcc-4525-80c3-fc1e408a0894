// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AIPromptEnrichmentApplyConfiguration represents a declarative configuration of the AIPromptEnrichment type for use
// with apply.
type AIPromptEnrichmentApplyConfiguration struct {
	Prepend []MessageApplyConfiguration `json:"prepend,omitempty"`
	Append  []MessageApplyConfiguration `json:"append,omitempty"`
}

// AIPromptEnrichmentApplyConfiguration constructs a declarative configuration of the AIPromptEnrichment type for use with
// apply.
func AIPromptEnrichment() *AIPromptEnrichmentApplyConfiguration {
	return &AIPromptEnrichmentApplyConfiguration{}
}

// WithPrepend adds the given value to the Prepend field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Prepend field.
func (b *AIPromptEnrichmentApplyConfiguration) WithPrepend(values ...*MessageApplyConfiguration) *AIPromptEnrichmentApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithPrepend")
		}
		b.Prepend = append(b.Prepend, *values[i])
	}
	return b
}

// WithAppend adds the given value to the Append field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Append field.
func (b *AIPromptEnrichmentApplyConfiguration) WithAppend(values ...*MessageApplyConfiguration) *AIPromptEnrichmentApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAppend")
		}
		b.Append = append(b.Append, *values[i])
	}
	return b
}
