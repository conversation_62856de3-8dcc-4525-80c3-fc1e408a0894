// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// EnvoyBootstrapApplyConfiguration represents a declarative configuration of the EnvoyBootstrap type for use
// with apply.
type EnvoyBootstrapApplyConfiguration struct {
	LogLevel           *string           `json:"logLevel,omitempty"`
	ComponentLogLevels map[string]string `json:"componentLogLevels,omitempty"`
}

// EnvoyBootstrapApplyConfiguration constructs a declarative configuration of the EnvoyBootstrap type for use with
// apply.
func EnvoyBootstrap() *EnvoyBootstrapApplyConfiguration {
	return &EnvoyBootstrapApplyConfiguration{}
}

// WithLogLevel sets the LogLevel field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LogLevel field is set to the value of the last call.
func (b *EnvoyBootstrapApplyConfiguration) WithLogLevel(value string) *EnvoyBootstrapApplyConfiguration {
	b.Log<PERSON>evel = &value
	return b
}

// WithComponentLogLevels puts the entries into the ComponentLogLevels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ComponentLogLevels field,
// overwriting an existing map entries in ComponentLogLevels field with the same key.
func (b *EnvoyBootstrapApplyConfiguration) WithComponentLogLevels(entries map[string]string) *EnvoyBootstrapApplyConfiguration {
	if b.ComponentLogLevels == nil && len(entries) > 0 {
		b.ComponentLogLevels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ComponentLogLevels[k] = v
	}
	return b
}
