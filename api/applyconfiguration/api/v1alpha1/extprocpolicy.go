// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// ExtProcPolicyApplyConfiguration represents a declarative configuration of the ExtProcPolicy type for use
// with apply.
type ExtProcPolicyApplyConfiguration struct {
	ExtensionRef   *v1.LocalObjectReference          `json:"extensionRef,omitempty"`
	ProcessingMode *ProcessingModeApplyConfiguration `json:"processingMode,omitempty"`
}

// ExtProcPolicyApplyConfiguration constructs a declarative configuration of the ExtProcPolicy type for use with
// apply.
func ExtProcPolicy() *ExtProcPolicyApplyConfiguration {
	return &ExtProcPolicyApplyConfiguration{}
}

// WithExtensionRef sets the ExtensionRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtensionRef field is set to the value of the last call.
func (b *ExtProcPolicyApplyConfiguration) WithExtensionRef(value v1.LocalObjectReference) *ExtProcPolicyApplyConfiguration {
	b.ExtensionRef = &value
	return b
}

// WithProcessingMode sets the ProcessingMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ProcessingMode field is set to the value of the last call.
func (b *ExtProcPolicyApplyConfiguration) WithProcessingMode(value *ProcessingModeApplyConfiguration) *ExtProcPolicyApplyConfiguration {
	b.ProcessingMode = value
	return b
}
