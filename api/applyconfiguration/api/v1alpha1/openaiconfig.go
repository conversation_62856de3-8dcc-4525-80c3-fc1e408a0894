// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// OpenAIConfigApplyConfiguration represents a declarative configuration of the OpenAIConfig type for use
// with apply.
type OpenAIConfigApplyConfiguration struct {
	AuthToken *SingleAuthTokenApplyConfiguration `json:"authToken,omitempty"`
	Model     *string                            `json:"model,omitempty"`
}

// OpenAIConfigApplyConfiguration constructs a declarative configuration of the OpenAIConfig type for use with
// apply.
func OpenAIConfig() *OpenAIConfigApplyConfiguration {
	return &OpenAIConfigApplyConfiguration{}
}

// WithAuthToken sets the AuthToken field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AuthToken field is set to the value of the last call.
func (b *OpenAIConfigApplyConfiguration) WithAuthToken(value *SingleAuthTokenApplyConfiguration) *OpenAIConfigApplyConfiguration {
	b.AuthToken = value
	return b
}

// WithModel sets the Model field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Model field is set to the value of the last call.
func (b *OpenAIConfigApplyConfiguration) WithModel(value string) *OpenAIConfigApplyConfiguration {
	b.Model = &value
	return b
}
