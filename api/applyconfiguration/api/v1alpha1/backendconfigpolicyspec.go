// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// BackendConfigPolicySpecApplyConfiguration represents a declarative configuration of the BackendConfigPolicySpec type for use
// with apply.
type BackendConfigPolicySpecApplyConfiguration struct {
	TargetRefs                    []LocalPolicyTargetReferenceApplyConfiguration `json:"targetRefs,omitempty"`
	TargetSelectors               []LocalPolicyTargetSelectorApplyConfiguration  `json:"targetSelectors,omitempty"`
	ConnectTimeout                *v1.Duration                                   `json:"connectTimeout,omitempty"`
	PerConnectionBufferLimitBytes *int                                           `json:"perConnectionBufferLimitBytes,omitempty"`
	TCPKeepalive                  *TCPKeepaliveApplyConfiguration                `json:"tcpKeepalive,omitempty"`
	CommonHttpProtocolOptions     *CommonHttpProtocolOptionsApplyConfiguration   `json:"commonHttpProtocolOptions,omitempty"`
	Http1ProtocolOptions          *Http1ProtocolOptionsApplyConfiguration        `json:"http1ProtocolOptions,omitempty"`
	SSLConfig                     *SSLConfigApplyConfiguration                   `json:"sslConfig,omitempty"`
}

// BackendConfigPolicySpecApplyConfiguration constructs a declarative configuration of the BackendConfigPolicySpec type for use with
// apply.
func BackendConfigPolicySpec() *BackendConfigPolicySpecApplyConfiguration {
	return &BackendConfigPolicySpecApplyConfiguration{}
}

// WithTargetRefs adds the given value to the TargetRefs field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetRefs field.
func (b *BackendConfigPolicySpecApplyConfiguration) WithTargetRefs(values ...*LocalPolicyTargetReferenceApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetRefs")
		}
		b.TargetRefs = append(b.TargetRefs, *values[i])
	}
	return b
}

// WithTargetSelectors adds the given value to the TargetSelectors field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetSelectors field.
func (b *BackendConfigPolicySpecApplyConfiguration) WithTargetSelectors(values ...*LocalPolicyTargetSelectorApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetSelectors")
		}
		b.TargetSelectors = append(b.TargetSelectors, *values[i])
	}
	return b
}

// WithConnectTimeout sets the ConnectTimeout field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ConnectTimeout field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithConnectTimeout(value v1.Duration) *BackendConfigPolicySpecApplyConfiguration {
	b.ConnectTimeout = &value
	return b
}

// WithPerConnectionBufferLimitBytes sets the PerConnectionBufferLimitBytes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PerConnectionBufferLimitBytes field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithPerConnectionBufferLimitBytes(value int) *BackendConfigPolicySpecApplyConfiguration {
	b.PerConnectionBufferLimitBytes = &value
	return b
}

// WithTCPKeepalive sets the TCPKeepalive field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TCPKeepalive field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithTCPKeepalive(value *TCPKeepaliveApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	b.TCPKeepalive = value
	return b
}

// WithCommonHttpProtocolOptions sets the CommonHttpProtocolOptions field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CommonHttpProtocolOptions field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithCommonHttpProtocolOptions(value *CommonHttpProtocolOptionsApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	b.CommonHttpProtocolOptions = value
	return b
}

// WithHttp1ProtocolOptions sets the Http1ProtocolOptions field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Http1ProtocolOptions field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithHttp1ProtocolOptions(value *Http1ProtocolOptionsApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	b.Http1ProtocolOptions = value
	return b
}

// WithSSLConfig sets the SSLConfig field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SSLConfig field is set to the value of the last call.
func (b *BackendConfigPolicySpecApplyConfiguration) WithSSLConfig(value *SSLConfigApplyConfiguration) *BackendConfigPolicySpecApplyConfiguration {
	b.SSLConfig = value
	return b
}
