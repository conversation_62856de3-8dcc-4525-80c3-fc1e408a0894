// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// FileSinkApplyConfiguration represents a declarative configuration of the FileSink type for use
// with apply.
type FileSinkApplyConfiguration struct {
	Path         *string               `json:"path,omitempty"`
	StringFormat *string               `json:"stringFormat,omitempty"`
	JsonFormat   *runtime.RawExtension `json:"jsonFormat,omitempty"`
}

// FileSinkApplyConfiguration constructs a declarative configuration of the FileSink type for use with
// apply.
func FileSink() *FileSinkApplyConfiguration {
	return &FileSinkApplyConfiguration{}
}

// WithPath sets the Path field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Path field is set to the value of the last call.
func (b *FileSinkApplyConfiguration) WithPath(value string) *FileSinkApplyConfiguration {
	b.Path = &value
	return b
}

// WithStringFormat sets the StringFormat field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StringFormat field is set to the value of the last call.
func (b *FileSinkApplyConfiguration) WithStringFormat(value string) *FileSinkApplyConfiguration {
	b.StringFormat = &value
	return b
}

// WithJsonFormat sets the JsonFormat field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JsonFormat field is set to the value of the last call.
func (b *FileSinkApplyConfiguration) WithJsonFormat(value runtime.RawExtension) *FileSinkApplyConfiguration {
	b.JsonFormat = &value
	return b
}
