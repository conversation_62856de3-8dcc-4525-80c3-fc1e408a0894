// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// BackendStatusApplyConfiguration represents a declarative configuration of the BackendStatus type for use
// with apply.
type BackendStatusApplyConfiguration struct {
	Conditions []v1.ConditionApplyConfiguration `json:"conditions,omitempty"`
}

// BackendStatusApplyConfiguration constructs a declarative configuration of the BackendStatus type for use with
// apply.
func BackendStatus() *BackendStatusApplyConfiguration {
	return &BackendStatusApplyConfiguration{}
}

// WithConditions adds the given value to the Conditions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Conditions field.
func (b *BackendStatusApplyConfiguration) WithConditions(values ...*v1.ConditionApplyConfiguration) *BackendStatusApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithConditions")
		}
		b.Conditions = append(b.Conditions, *values[i])
	}
	return b
}
