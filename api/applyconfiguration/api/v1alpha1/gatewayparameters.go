// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	managedfields "k8s.io/apimachinery/pkg/util/managedfields"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"

	internal "github.com/kgateway-dev/kgateway/v2/api/applyconfiguration/internal"
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// GatewayParametersApplyConfiguration represents a declarative configuration of the GatewayParameters type for use
// with apply.
type GatewayParametersApplyConfiguration struct {
	v1.TypeMetaApplyConfiguration    `json:",inline"`
	*v1.ObjectMetaApplyConfiguration `json:"metadata,omitempty"`
	Spec                             *GatewayParametersSpecApplyConfiguration `json:"spec,omitempty"`
	Status                           *apiv1alpha1.GatewayParametersStatus     `json:"status,omitempty"`
}

// GatewayParameters constructs a declarative configuration of the GatewayParameters type for use with
// apply.
func GatewayParameters(name, namespace string) *GatewayParametersApplyConfiguration {
	b := &GatewayParametersApplyConfiguration{}
	b.WithName(name)
	b.WithNamespace(namespace)
	b.WithKind("GatewayParameters")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b
}

// ExtractGatewayParameters extracts the applied configuration owned by fieldManager from
// gatewayParameters. If no managedFields are found in gatewayParameters for fieldManager, a
// GatewayParametersApplyConfiguration is returned with only the Name, Namespace (if applicable),
// APIVersion and Kind populated. It is possible that no managed fields were found for because other
// field managers have taken ownership of all the fields previously owned by fieldManager, or because
// the fieldManager never owned fields any fields.
// gatewayParameters must be a unmodified GatewayParameters API object that was retrieved from the Kubernetes API.
// ExtractGatewayParameters provides a way to perform a extract/modify-in-place/apply workflow.
// Note that an extracted apply configuration will contain fewer fields than what the fieldManager previously
// applied if another fieldManager has updated or force applied any of the previously applied fields.
// Experimental!
func ExtractGatewayParameters(gatewayParameters *apiv1alpha1.GatewayParameters, fieldManager string) (*GatewayParametersApplyConfiguration, error) {
	return extractGatewayParameters(gatewayParameters, fieldManager, "")
}

// ExtractGatewayParametersStatus is the same as ExtractGatewayParameters except
// that it extracts the status subresource applied configuration.
// Experimental!
func ExtractGatewayParametersStatus(gatewayParameters *apiv1alpha1.GatewayParameters, fieldManager string) (*GatewayParametersApplyConfiguration, error) {
	return extractGatewayParameters(gatewayParameters, fieldManager, "status")
}

func extractGatewayParameters(gatewayParameters *apiv1alpha1.GatewayParameters, fieldManager string, subresource string) (*GatewayParametersApplyConfiguration, error) {
	b := &GatewayParametersApplyConfiguration{}
	err := managedfields.ExtractInto(gatewayParameters, internal.Parser().Type("com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParameters"), fieldManager, b, subresource)
	if err != nil {
		return nil, err
	}
	b.WithName(gatewayParameters.Name)
	b.WithNamespace(gatewayParameters.Namespace)

	b.WithKind("GatewayParameters")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b, nil
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithKind(value string) *GatewayParametersApplyConfiguration {
	b.TypeMetaApplyConfiguration.Kind = &value
	return b
}

// WithAPIVersion sets the APIVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the APIVersion field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithAPIVersion(value string) *GatewayParametersApplyConfiguration {
	b.TypeMetaApplyConfiguration.APIVersion = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithName(value string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Name = &value
	return b
}

// WithGenerateName sets the GenerateName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GenerateName field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithGenerateName(value string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.GenerateName = &value
	return b
}

// WithNamespace sets the Namespace field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Namespace field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithNamespace(value string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Namespace = &value
	return b
}

// WithUID sets the UID field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UID field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithUID(value types.UID) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.UID = &value
	return b
}

// WithResourceVersion sets the ResourceVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResourceVersion field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithResourceVersion(value string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.ResourceVersion = &value
	return b
}

// WithGeneration sets the Generation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Generation field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithGeneration(value int64) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Generation = &value
	return b
}

// WithCreationTimestamp sets the CreationTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CreationTimestamp field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithCreationTimestamp(value metav1.Time) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.CreationTimestamp = &value
	return b
}

// WithDeletionTimestamp sets the DeletionTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionTimestamp field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithDeletionTimestamp(value metav1.Time) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionTimestamp = &value
	return b
}

// WithDeletionGracePeriodSeconds sets the DeletionGracePeriodSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionGracePeriodSeconds field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithDeletionGracePeriodSeconds(value int64) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionGracePeriodSeconds = &value
	return b
}

// WithLabels puts the entries into the Labels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Labels field,
// overwriting an existing map entries in Labels field with the same key.
func (b *GatewayParametersApplyConfiguration) WithLabels(entries map[string]string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Labels == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Labels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Labels[k] = v
	}
	return b
}

// WithAnnotations puts the entries into the Annotations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Annotations field,
// overwriting an existing map entries in Annotations field with the same key.
func (b *GatewayParametersApplyConfiguration) WithAnnotations(entries map[string]string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Annotations == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Annotations = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Annotations[k] = v
	}
	return b
}

// WithOwnerReferences adds the given value to the OwnerReferences field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the OwnerReferences field.
func (b *GatewayParametersApplyConfiguration) WithOwnerReferences(values ...*v1.OwnerReferenceApplyConfiguration) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithOwnerReferences")
		}
		b.ObjectMetaApplyConfiguration.OwnerReferences = append(b.ObjectMetaApplyConfiguration.OwnerReferences, *values[i])
	}
	return b
}

// WithFinalizers adds the given value to the Finalizers field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Finalizers field.
func (b *GatewayParametersApplyConfiguration) WithFinalizers(values ...string) *GatewayParametersApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		b.ObjectMetaApplyConfiguration.Finalizers = append(b.ObjectMetaApplyConfiguration.Finalizers, values[i])
	}
	return b
}

func (b *GatewayParametersApplyConfiguration) ensureObjectMetaApplyConfigurationExists() {
	if b.ObjectMetaApplyConfiguration == nil {
		b.ObjectMetaApplyConfiguration = &v1.ObjectMetaApplyConfiguration{}
	}
}

// WithSpec sets the Spec field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Spec field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithSpec(value *GatewayParametersSpecApplyConfiguration) *GatewayParametersApplyConfiguration {
	b.Spec = value
	return b
}

// WithStatus sets the Status field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Status field is set to the value of the last call.
func (b *GatewayParametersApplyConfiguration) WithStatus(value apiv1alpha1.GatewayParametersStatus) *GatewayParametersApplyConfiguration {
	b.Status = &value
	return b
}

// GetName retrieves the value of the Name field in the declarative configuration.
func (b *GatewayParametersApplyConfiguration) GetName() *string {
	b.ensureObjectMetaApplyConfigurationExists()
	return b.ObjectMetaApplyConfiguration.Name
}
