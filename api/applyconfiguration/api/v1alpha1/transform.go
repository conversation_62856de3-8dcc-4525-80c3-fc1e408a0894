// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// TransformApplyConfiguration represents a declarative configuration of the Transform type for use
// with apply.
type TransformApplyConfiguration struct {
	Set    []HeaderTransformationApplyConfiguration `json:"set,omitempty"`
	Add    []HeaderTransformationApplyConfiguration `json:"add,omitempty"`
	Remove []string                                 `json:"remove,omitempty"`
	Body   *BodyTransformationApplyConfiguration    `json:"body,omitempty"`
}

// TransformApplyConfiguration constructs a declarative configuration of the Transform type for use with
// apply.
func Transform() *TransformApplyConfiguration {
	return &TransformApplyConfiguration{}
}

// WithSet adds the given value to the Set field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Set field.
func (b *TransformApplyConfiguration) WithSet(values ...*HeaderTransformationApplyConfiguration) *TransformApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithSet")
		}
		b.Set = append(b.Set, *values[i])
	}
	return b
}

// WithAdd adds the given value to the Add field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Add field.
func (b *TransformApplyConfiguration) WithAdd(values ...*HeaderTransformationApplyConfiguration) *TransformApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAdd")
		}
		b.Add = append(b.Add, *values[i])
	}
	return b
}

// WithRemove adds the given value to the Remove field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Remove field.
func (b *TransformApplyConfiguration) WithRemove(values ...string) *TransformApplyConfiguration {
	for i := range values {
		b.Remove = append(b.Remove, values[i])
	}
	return b
}

// WithBody sets the Body field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Body field is set to the value of the last call.
func (b *TransformApplyConfiguration) WithBody(value *BodyTransformationApplyConfiguration) *TransformApplyConfiguration {
	b.Body = value
	return b
}
