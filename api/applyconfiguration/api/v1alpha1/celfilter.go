// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// CELFilterApplyConfiguration represents a declarative configuration of the CELFilter type for use
// with apply.
type CELFilterApplyConfiguration struct {
	Match *string `json:"match,omitempty"`
}

// CELFilterApplyConfiguration constructs a declarative configuration of the CELFilter type for use with
// apply.
func CELFilter() *CELFilterApplyConfiguration {
	return &CELFilterApplyConfiguration{}
}

// WithMatch sets the Match field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Match field is set to the value of the last call.
func (b *CELFilterApplyConfiguration) WithMatch(value string) *CELFilterApplyConfiguration {
	b.Match = &value
	return b
}
