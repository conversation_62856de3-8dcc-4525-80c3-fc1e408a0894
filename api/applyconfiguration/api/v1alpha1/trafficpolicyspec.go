// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// TrafficPolicySpecApplyConfiguration represents a declarative configuration of the TrafficPolicySpec type for use
// with apply.
type TrafficPolicySpecApplyConfiguration struct {
	TargetRefs      []LocalPolicyTargetReferenceWithSectionNameApplyConfiguration `json:"targetRefs,omitempty"`
	TargetSelectors []LocalPolicyTargetSelectorApplyConfiguration                 `json:"targetSelectors,omitempty"`
	AI              *AIPolicyApplyConfiguration                                   `json:"ai,omitempty"`
	Transformation  *TransformationPolicyApplyConfiguration                       `json:"transformation,omitempty"`
	ExtProc         *ExtProcPolicyApplyConfiguration                              `json:"extProc,omitempty"`
	ExtAuth         *ExtAuthPolicyApplyConfiguration                              `json:"extAuth,omitempty"`
	RateLimit       *RateLimitApplyConfiguration                                  `json:"rateLimit,omitempty"`
	Cors            *CorsPolicyApplyConfiguration                                 `json:"cors,omitempty"`
}

// TrafficPolicySpecApplyConfiguration constructs a declarative configuration of the TrafficPolicySpec type for use with
// apply.
func TrafficPolicySpec() *TrafficPolicySpecApplyConfiguration {
	return &TrafficPolicySpecApplyConfiguration{}
}

// WithTargetRefs adds the given value to the TargetRefs field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetRefs field.
func (b *TrafficPolicySpecApplyConfiguration) WithTargetRefs(values ...*LocalPolicyTargetReferenceWithSectionNameApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetRefs")
		}
		b.TargetRefs = append(b.TargetRefs, *values[i])
	}
	return b
}

// WithTargetSelectors adds the given value to the TargetSelectors field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the TargetSelectors field.
func (b *TrafficPolicySpecApplyConfiguration) WithTargetSelectors(values ...*LocalPolicyTargetSelectorApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithTargetSelectors")
		}
		b.TargetSelectors = append(b.TargetSelectors, *values[i])
	}
	return b
}

// WithAI sets the AI field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AI field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithAI(value *AIPolicyApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.AI = value
	return b
}

// WithTransformation sets the Transformation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Transformation field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithTransformation(value *TransformationPolicyApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.Transformation = value
	return b
}

// WithExtProc sets the ExtProc field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtProc field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithExtProc(value *ExtProcPolicyApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.ExtProc = value
	return b
}

// WithExtAuth sets the ExtAuth field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtAuth field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithExtAuth(value *ExtAuthPolicyApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.ExtAuth = value
	return b
}

// WithRateLimit sets the RateLimit field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RateLimit field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithRateLimit(value *RateLimitApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.RateLimit = value
	return b
}

// WithCors sets the Cors field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Cors field is set to the value of the last call.
func (b *TrafficPolicySpecApplyConfiguration) WithCors(value *CorsPolicyApplyConfiguration) *TrafficPolicySpecApplyConfiguration {
	b.Cors = value
	return b
}
