// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AwsBackendApplyConfiguration represents a declarative configuration of the AwsBackend type for use
// with apply.
type AwsBackendApplyConfiguration struct {
	AccountId *string                      `json:"accountId,omitempty"`
	Auth      *AwsAuthApplyConfiguration   `json:"auth,omitempty"`
	Lambda    *AwsLambdaApplyConfiguration `json:"lambda,omitempty"`
	Region    *string                      `json:"region,omitempty"`
}

// AwsBackendApplyConfiguration constructs a declarative configuration of the AwsBackend type for use with
// apply.
func AwsBackend() *AwsBackendApplyConfiguration {
	return &AwsBackendApplyConfiguration{}
}

// WithAccountId sets the AccountId field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AccountId field is set to the value of the last call.
func (b *AwsBackendApplyConfiguration) WithAccountId(value string) *AwsBackendApplyConfiguration {
	b.AccountId = &value
	return b
}

// WithAuth sets the Auth field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Auth field is set to the value of the last call.
func (b *AwsBackendApplyConfiguration) WithAuth(value *AwsAuthApplyConfiguration) *AwsBackendApplyConfiguration {
	b.Auth = value
	return b
}

// WithLambda sets the Lambda field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Lambda field is set to the value of the last call.
func (b *AwsBackendApplyConfiguration) WithLambda(value *AwsLambdaApplyConfiguration) *AwsBackendApplyConfiguration {
	b.Lambda = value
	return b
}

// WithRegion sets the Region field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Region field is set to the value of the last call.
func (b *AwsBackendApplyConfiguration) WithRegion(value string) *AwsBackendApplyConfiguration {
	b.Region = &value
	return b
}
