// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RateLimitDescriptorApplyConfiguration represents a declarative configuration of the RateLimitDescriptor type for use
// with apply.
type RateLimitDescriptorApplyConfiguration struct {
	Entries []RateLimitDescriptorEntryApplyConfiguration `json:"entries,omitempty"`
}

// RateLimitDescriptorApplyConfiguration constructs a declarative configuration of the RateLimitDescriptor type for use with
// apply.
func RateLimitDescriptor() *RateLimitDescriptorApplyConfiguration {
	return &RateLimitDescriptorApplyConfiguration{}
}

// WithEntries adds the given value to the Entries field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Entries field.
func (b *RateLimitDescriptorApplyConfiguration) WithEntries(values ...*RateLimitDescriptorEntryApplyConfiguration) *RateLimitDescriptorApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithEntries")
		}
		b.Entries = append(b.Entries, *values[i])
	}
	return b
}
