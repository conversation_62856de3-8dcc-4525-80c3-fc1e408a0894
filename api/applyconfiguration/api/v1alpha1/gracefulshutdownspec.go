// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// GracefulShutdownSpecApplyConfiguration represents a declarative configuration of the GracefulShutdownSpec type for use
// with apply.
type GracefulShutdownSpecApplyConfiguration struct {
	Enabled          *bool `json:"enabled,omitempty"`
	SleepTimeSeconds *int  `json:"sleepTimeSeconds,omitempty"`
}

// GracefulShutdownSpecApplyConfiguration constructs a declarative configuration of the GracefulShutdownSpec type for use with
// apply.
func GracefulShutdownSpec() *GracefulShutdownSpecApplyConfiguration {
	return &GracefulShutdownSpecApplyConfiguration{}
}

// WithEnabled sets the Enabled field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Enabled field is set to the value of the last call.
func (b *GracefulShutdownSpecApplyConfiguration) WithEnabled(value bool) *GracefulShutdownSpecApplyConfiguration {
	b.Enabled = &value
	return b
}

// WithSleepTimeSeconds sets the SleepTimeSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SleepTimeSeconds field is set to the value of the last call.
func (b *GracefulShutdownSpecApplyConfiguration) WithSleepTimeSeconds(value int) *GracefulShutdownSpecApplyConfiguration {
	b.SleepTimeSeconds = &value
	return b
}
