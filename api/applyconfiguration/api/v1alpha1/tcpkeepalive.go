// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TCPKeepaliveApplyConfiguration represents a declarative configuration of the TCPKeepalive type for use
// with apply.
type TCPKeepaliveApplyConfiguration struct {
	KeepAliveProbes   *int         `json:"keepAliveProbes,omitempty"`
	KeepAliveTime     *v1.Duration `json:"keepAliveTime,omitempty"`
	KeepAliveInterval *v1.Duration `json:"keepAliveInterval,omitempty"`
}

// TCPKeepaliveApplyConfiguration constructs a declarative configuration of the TCPKeepalive type for use with
// apply.
func TCPKeepalive() *TCPKeepaliveApplyConfiguration {
	return &TCPKeepaliveApplyConfiguration{}
}

// WithKeepAliveProbes sets the KeepAliveProbes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the KeepAliveProbes field is set to the value of the last call.
func (b *TCPKeepaliveApplyConfiguration) WithKeepAliveProbes(value int) *TCPKeepaliveApplyConfiguration {
	b.KeepAliveProbes = &value
	return b
}

// WithKeepAliveTime sets the KeepAliveTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the KeepAliveTime field is set to the value of the last call.
func (b *TCPKeepaliveApplyConfiguration) WithKeepAliveTime(value v1.Duration) *TCPKeepaliveApplyConfiguration {
	b.KeepAliveTime = &value
	return b
}

// WithKeepAliveInterval sets the KeepAliveInterval field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the KeepAliveInterval field is set to the value of the last call.
func (b *TCPKeepaliveApplyConfiguration) WithKeepAliveInterval(value v1.Duration) *TCPKeepaliveApplyConfiguration {
	b.KeepAliveInterval = &value
	return b
}
