// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// TransformationPolicyApplyConfiguration represents a declarative configuration of the TransformationPolicy type for use
// with apply.
type TransformationPolicyApplyConfiguration struct {
	Request  *TransformApplyConfiguration `json:"request,omitempty"`
	Response *TransformApplyConfiguration `json:"response,omitempty"`
}

// TransformationPolicyApplyConfiguration constructs a declarative configuration of the TransformationPolicy type for use with
// apply.
func TransformationPolicy() *TransformationPolicyApplyConfiguration {
	return &TransformationPolicyApplyConfiguration{}
}

// WithRequest sets the Request field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Request field is set to the value of the last call.
func (b *TransformationPolicyApplyConfiguration) WithRequest(value *TransformApplyConfiguration) *TransformationPolicyApplyConfiguration {
	b.Request = value
	return b
}

// WithResponse sets the Response field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Response field is set to the value of the last call.
func (b *TransformationPolicyApplyConfiguration) WithResponse(value *TransformApplyConfiguration) *TransformationPolicyApplyConfiguration {
	b.Response = value
	return b
}
