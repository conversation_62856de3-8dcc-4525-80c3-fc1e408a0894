// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AIPromptGuardApplyConfiguration represents a declarative configuration of the AIPromptGuard type for use
// with apply.
type AIPromptGuardApplyConfiguration struct {
	Request  *PromptguardRequestApplyConfiguration  `json:"request,omitempty"`
	Response *PromptguardResponseApplyConfiguration `json:"response,omitempty"`
}

// AIPromptGuardApplyConfiguration constructs a declarative configuration of the AIPromptGuard type for use with
// apply.
func AIPromptGuard() *AIPromptGuardApplyConfiguration {
	return &AIPromptGuardApplyConfiguration{}
}

// WithRequest sets the Request field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Request field is set to the value of the last call.
func (b *AIPromptGuardApplyConfiguration) WithRequest(value *PromptguardRequestApplyConfiguration) *AIPromptGuardApplyConfiguration {
	b.Request = value
	return b
}

// WithResponse sets the Response field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Response field is set to the value of the last call.
func (b *AIPromptGuardApplyConfiguration) WithResponse(value *PromptguardResponseApplyConfiguration) *AIPromptGuardApplyConfiguration {
	b.Response = value
	return b
}
