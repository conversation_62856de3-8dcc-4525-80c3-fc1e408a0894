// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// BufferSettingsApplyConfiguration represents a declarative configuration of the BufferSettings type for use
// with apply.
type BufferSettingsApplyConfiguration struct {
	MaxRequestBytes     *uint32 `json:"maxRequestBytes,omitempty"`
	AllowPartialMessage *bool   `json:"allowPartialMessage,omitempty"`
	PackAsBytes         *bool   `json:"packAsBytes,omitempty"`
}

// BufferSettingsApplyConfiguration constructs a declarative configuration of the BufferSettings type for use with
// apply.
func BufferSettings() *BufferSettingsApplyConfiguration {
	return &BufferSettingsApplyConfiguration{}
}

// WithMaxRequestBytes sets the MaxRequestBytes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxRequestBytes field is set to the value of the last call.
func (b *BufferSettingsApplyConfiguration) WithMaxRequestBytes(value uint32) *BufferSettingsApplyConfiguration {
	b.MaxRequestBytes = &value
	return b
}

// WithAllowPartialMessage sets the AllowPartialMessage field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AllowPartialMessage field is set to the value of the last call.
func (b *BufferSettingsApplyConfiguration) WithAllowPartialMessage(value bool) *BufferSettingsApplyConfiguration {
	b.AllowPartialMessage = &value
	return b
}

// WithPackAsBytes sets the PackAsBytes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PackAsBytes field is set to the value of the last call.
func (b *BufferSettingsApplyConfiguration) WithPackAsBytes(value bool) *BufferSettingsApplyConfiguration {
	b.PackAsBytes = &value
	return b
}
