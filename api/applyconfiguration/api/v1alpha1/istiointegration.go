// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// IstioIntegrationApplyConfiguration represents a declarative configuration of the IstioIntegration type for use
// with apply.
type IstioIntegrationApplyConfiguration struct {
	IstioProxyContainer *IstioContainerApplyConfiguration `json:"istioProxyContainer,omitempty"`
	CustomSidecars      []v1.Container                    `json:"customSidecars,omitempty"`
}

// IstioIntegrationApplyConfiguration constructs a declarative configuration of the IstioIntegration type for use with
// apply.
func IstioIntegration() *IstioIntegrationApplyConfiguration {
	return &IstioIntegrationApplyConfiguration{}
}

// WithIstioProxyContainer sets the IstioProxyContainer field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the IstioProxyContainer field is set to the value of the last call.
func (b *IstioIntegrationApplyConfiguration) WithIstioProxyContainer(value *IstioContainerApplyConfiguration) *IstioIntegrationApplyConfiguration {
	b.IstioProxyContainer = value
	return b
}

// WithCustomSidecars adds the given value to the CustomSidecars field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the CustomSidecars field.
func (b *IstioIntegrationApplyConfiguration) WithCustomSidecars(values ...v1.Container) *IstioIntegrationApplyConfiguration {
	for i := range values {
		b.CustomSidecars = append(b.CustomSidecars, values[i])
	}
	return b
}
