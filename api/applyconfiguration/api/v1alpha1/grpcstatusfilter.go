// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// GrpcStatusFilterApplyConfiguration represents a declarative configuration of the GrpcStatusFilter type for use
// with apply.
type GrpcStatusFilterApplyConfiguration struct {
	Statuses []apiv1alpha1.GrpcStatus `json:"statuses,omitempty"`
	Exclude  *bool                    `json:"exclude,omitempty"`
}

// GrpcStatusFilterApplyConfiguration constructs a declarative configuration of the GrpcStatusFilter type for use with
// apply.
func GrpcStatusFilter() *GrpcStatusFilterApplyConfiguration {
	return &GrpcStatusFilterApplyConfiguration{}
}

// WithStatuses adds the given value to the Statuses field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Statuses field.
func (b *GrpcStatusFilterApplyConfiguration) WithStatuses(values ...apiv1alpha1.GrpcStatus) *GrpcStatusFilterApplyConfiguration {
	for i := range values {
		b.Statuses = append(b.Statuses, values[i])
	}
	return b
}

// WithExclude sets the Exclude field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Exclude field is set to the value of the last call.
func (b *GrpcStatusFilterApplyConfiguration) WithExclude(value bool) *GrpcStatusFilterApplyConfiguration {
	b.Exclude = &value
	return b
}
