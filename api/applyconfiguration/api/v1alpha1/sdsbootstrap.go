// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// SdsBootstrapApplyConfiguration represents a declarative configuration of the SdsBootstrap type for use
// with apply.
type SdsBootstrapApplyConfiguration struct {
	LogLevel *string `json:"logLevel,omitempty"`
}

// SdsBootstrapApplyConfiguration constructs a declarative configuration of the SdsBootstrap type for use with
// apply.
func SdsBootstrap() *SdsBootstrapApplyConfiguration {
	return &SdsBootstrapApplyConfiguration{}
}

// WithLogLevel sets the LogLevel field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LogLevel field is set to the value of the last call.
func (b *SdsBootstrapApplyConfiguration) WithLogLevel(value string) *SdsBootstrapApplyConfiguration {
	b.LogLevel = &value
	return b
}
