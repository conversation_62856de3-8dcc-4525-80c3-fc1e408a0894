// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// SSLFilesApplyConfiguration represents a declarative configuration of the SSLFiles type for use
// with apply.
type SSLFilesApplyConfiguration struct {
	TLSCertificate *string `json:"tlsCertificate,omitempty"`
	TLSKey         *string `json:"tlsKey,omitempty"`
	RootCA         *string `json:"rootCA,omitempty"`
}

// SSLFilesApplyConfiguration constructs a declarative configuration of the SSLFiles type for use with
// apply.
func SSLFiles() *SSLFilesApplyConfiguration {
	return &SSLFilesApplyConfiguration{}
}

// WithTLSCertificate sets the TLSCertificate field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TLSCertificate field is set to the value of the last call.
func (b *SSLFilesApplyConfiguration) WithTLSCertificate(value string) *SSLFilesApplyConfiguration {
	b.TLSCertificate = &value
	return b
}

// WithTL<PERSON><PERSON>ey sets the TLSKey field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TLSKey field is set to the value of the last call.
func (b *SSLFilesApplyConfiguration) WithTLSKey(value string) *SSLFilesApplyConfiguration {
	b.TLSKey = &value
	return b
}

// WithRootCA sets the RootCA field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RootCA field is set to the value of the last call.
func (b *SSLFilesApplyConfiguration) WithRootCA(value string) *SSLFilesApplyConfiguration {
	b.RootCA = &value
	return b
}
