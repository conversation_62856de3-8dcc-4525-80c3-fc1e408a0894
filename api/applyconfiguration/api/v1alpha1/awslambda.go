// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AwsLambdaApplyConfiguration represents a declarative configuration of the AwsLambda type for use
// with apply.
type AwsLambdaApplyConfiguration struct {
	EndpointURL    *string `json:"endpointURL,omitempty"`
	FunctionName   *string `json:"functionName,omitempty"`
	InvocationMode *string `json:"invocationMode,omitempty"`
	Qualifier      *string `json:"qualifier,omitempty"`
}

// AwsLambdaApplyConfiguration constructs a declarative configuration of the AwsLambda type for use with
// apply.
func AwsLambda() *AwsLambdaApplyConfiguration {
	return &AwsLambdaApplyConfiguration{}
}

// WithEndpointURL sets the EndpointURL field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EndpointURL field is set to the value of the last call.
func (b *AwsLambdaApplyConfiguration) WithEndpointURL(value string) *AwsLambdaApplyConfiguration {
	b.EndpointURL = &value
	return b
}

// WithFunctionName sets the FunctionName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FunctionName field is set to the value of the last call.
func (b *AwsLambdaApplyConfiguration) WithFunctionName(value string) *AwsLambdaApplyConfiguration {
	b.FunctionName = &value
	return b
}

// WithInvocationMode sets the InvocationMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the InvocationMode field is set to the value of the last call.
func (b *AwsLambdaApplyConfiguration) WithInvocationMode(value string) *AwsLambdaApplyConfiguration {
	b.InvocationMode = &value
	return b
}

// WithQualifier sets the Qualifier field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Qualifier field is set to the value of the last call.
func (b *AwsLambdaApplyConfiguration) WithQualifier(value string) *AwsLambdaApplyConfiguration {
	b.Qualifier = &value
	return b
}
