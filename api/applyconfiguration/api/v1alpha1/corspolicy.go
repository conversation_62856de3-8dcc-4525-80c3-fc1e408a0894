// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// CorsPolicyApplyConfiguration represents a declarative configuration of the CorsPolicy type for use
// with apply.
type CorsPolicyApplyConfiguration struct {
	v1.HTTPCORSFilter `json:",inline"`
}

// CorsPolicyApplyConfiguration constructs a declarative configuration of the CorsPolicy type for use with
// apply.
func CorsPolicy() *CorsPolicyApplyConfiguration {
	return &CorsPolicyApplyConfiguration{}
}
