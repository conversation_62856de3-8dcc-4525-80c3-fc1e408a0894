// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// UpgradeConfigApplyConfiguration represents a declarative configuration of the UpgradeConfig type for use
// with apply.
type UpgradeConfigApplyConfiguration struct {
	EnabledUpgrades []string `json:"enabledUpgrades,omitempty"`
}

// UpgradeConfigApplyConfiguration constructs a declarative configuration of the UpgradeConfig type for use with
// apply.
func UpgradeConfig() *UpgradeConfigApplyConfiguration {
	return &UpgradeConfigApplyConfiguration{}
}

// WithEnabledUpgrades adds the given value to the EnabledUpgrades field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the EnabledUpgrades field.
func (b *UpgradeConfigApplyConfiguration) WithEnabledUpgrades(values ...string) *UpgradeConfigApplyConfiguration {
	for i := range values {
		b.EnabledUpgrades = append(b.EnabledUpgrades, values[i])
	}
	return b
}
