// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// LocalRateLimitPolicyApplyConfiguration represents a declarative configuration of the LocalRateLimitPolicy type for use
// with apply.
type LocalRateLimitPolicyApplyConfiguration struct {
	TokenBucket *TokenBucketApplyConfiguration `json:"tokenBucket,omitempty"`
}

// LocalRateLimitPolicyApplyConfiguration constructs a declarative configuration of the LocalRateLimitPolicy type for use with
// apply.
func LocalRateLimitPolicy() *LocalRateLimitPolicyApplyConfiguration {
	return &LocalRateLimitPolicyApplyConfiguration{}
}

// WithTokenBucket sets the TokenBucket field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TokenBucket field is set to the value of the last call.
func (b *LocalRateLimitPolicyApplyConfiguration) WithTokenBucket(value *TokenBucketApplyConfiguration) *LocalRateLimitPolicyApplyConfiguration {
	b.TokenBucket = value
	return b
}
