// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// LocalPolicyTargetReferenceApplyConfiguration represents a declarative configuration of the LocalPolicyTargetReference type for use
// with apply.
type LocalPolicyTargetReferenceApplyConfiguration struct {
	Group *v1.Group      `json:"group,omitempty"`
	Kind  *v1.Kind       `json:"kind,omitempty"`
	Name  *v1.ObjectName `json:"name,omitempty"`
}

// LocalPolicyTargetReferenceApplyConfiguration constructs a declarative configuration of the LocalPolicyTargetReference type for use with
// apply.
func LocalPolicyTargetReference() *LocalPolicyTargetReferenceApplyConfiguration {
	return &LocalPolicyTargetReferenceApplyConfiguration{}
}

// WithGroup sets the Group field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Group field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceApplyConfiguration) WithGroup(value v1.Group) *LocalPolicyTargetReferenceApplyConfiguration {
	b.Group = &value
	return b
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceApplyConfiguration) WithKind(value v1.Kind) *LocalPolicyTargetReferenceApplyConfiguration {
	b.Kind = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *LocalPolicyTargetReferenceApplyConfiguration) WithName(value v1.ObjectName) *LocalPolicyTargetReferenceApplyConfiguration {
	b.Name = &value
	return b
}
