// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// CommonHttpProtocolOptionsApplyConfiguration represents a declarative configuration of the CommonHttpProtocolOptions type for use
// with apply.
type CommonHttpProtocolOptionsApplyConfiguration struct {
	IdleTimeout                  *v1.Duration                              `json:"idleTimeout,omitempty"`
	MaxHeadersCount              *int                                      `json:"maxHeadersCount,omitempty"`
	MaxStreamDuration            *v1.Duration                              `json:"maxStreamDuration,omitempty"`
	HeadersWithUnderscoresAction *apiv1alpha1.HeadersWithUnderscoresAction `json:"headersWithUnderscoresAction,omitempty"`
	MaxRequestsPerConnection     *int                                      `json:"maxRequestsPerConnection,omitempty"`
}

// CommonHttpProtocolOptionsApplyConfiguration constructs a declarative configuration of the CommonHttpProtocolOptions type for use with
// apply.
func CommonHttpProtocolOptions() *CommonHttpProtocolOptionsApplyConfiguration {
	return &CommonHttpProtocolOptionsApplyConfiguration{}
}

// WithIdleTimeout sets the IdleTimeout field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the IdleTimeout field is set to the value of the last call.
func (b *CommonHttpProtocolOptionsApplyConfiguration) WithIdleTimeout(value v1.Duration) *CommonHttpProtocolOptionsApplyConfiguration {
	b.IdleTimeout = &value
	return b
}

// WithMaxHeadersCount sets the MaxHeadersCount field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxHeadersCount field is set to the value of the last call.
func (b *CommonHttpProtocolOptionsApplyConfiguration) WithMaxHeadersCount(value int) *CommonHttpProtocolOptionsApplyConfiguration {
	b.MaxHeadersCount = &value
	return b
}

// WithMaxStreamDuration sets the MaxStreamDuration field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxStreamDuration field is set to the value of the last call.
func (b *CommonHttpProtocolOptionsApplyConfiguration) WithMaxStreamDuration(value v1.Duration) *CommonHttpProtocolOptionsApplyConfiguration {
	b.MaxStreamDuration = &value
	return b
}

// WithHeadersWithUnderscoresAction sets the HeadersWithUnderscoresAction field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the HeadersWithUnderscoresAction field is set to the value of the last call.
func (b *CommonHttpProtocolOptionsApplyConfiguration) WithHeadersWithUnderscoresAction(value apiv1alpha1.HeadersWithUnderscoresAction) *CommonHttpProtocolOptionsApplyConfiguration {
	b.HeadersWithUnderscoresAction = &value
	return b
}

// WithMaxRequestsPerConnection sets the MaxRequestsPerConnection field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxRequestsPerConnection field is set to the value of the last call.
func (b *CommonHttpProtocolOptionsApplyConfiguration) WithMaxRequestsPerConnection(value int) *CommonHttpProtocolOptionsApplyConfiguration {
	b.MaxRequestsPerConnection = &value
	return b
}
