// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// GatewayParametersSpecApplyConfiguration represents a declarative configuration of the GatewayParametersSpec type for use
// with apply.
type GatewayParametersSpecApplyConfiguration struct {
	Kube        *KubernetesProxyConfigApplyConfiguration `json:"kube,omitempty"`
	SelfManaged *apiv1alpha1.SelfManagedGateway          `json:"selfManaged,omitempty"`
}

// GatewayParametersSpecApplyConfiguration constructs a declarative configuration of the GatewayParametersSpec type for use with
// apply.
func GatewayParametersSpec() *GatewayParametersSpecApplyConfiguration {
	return &GatewayParametersSpecApplyConfiguration{}
}

// WithKube sets the Kube field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kube field is set to the value of the last call.
func (b *GatewayParametersSpecApplyConfiguration) WithKube(value *KubernetesProxyConfigApplyConfiguration) *GatewayParametersSpecApplyConfiguration {
	b.Kube = value
	return b
}

// WithSelfManaged sets the SelfManaged field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SelfManaged field is set to the value of the last call.
func (b *GatewayParametersSpecApplyConfiguration) WithSelfManaged(value apiv1alpha1.SelfManagedGateway) *GatewayParametersSpecApplyConfiguration {
	b.SelfManaged = &value
	return b
}
