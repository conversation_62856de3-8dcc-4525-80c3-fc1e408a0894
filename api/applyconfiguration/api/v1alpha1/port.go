// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// PortApplyConfiguration represents a declarative configuration of the Port type for use
// with apply.
type PortApplyConfiguration struct {
	Port     *uint16 `json:"port,omitempty"`
	NodePort *uint16 `json:"nodePort,omitempty"`
}

// PortApplyConfiguration constructs a declarative configuration of the Port type for use with
// apply.
func Port() *PortApplyConfiguration {
	return &PortApplyConfiguration{}
}

// WithPort sets the Port field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Port field is set to the value of the last call.
func (b *PortApplyConfiguration) WithPort(value uint16) *PortApplyConfiguration {
	b.Port = &value
	return b
}

// WithNodePort sets the NodePort field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the NodePort field is set to the value of the last call.
func (b *PortApplyConfiguration) WithNodePort(value uint16) *PortApplyConfiguration {
	b.NodePort = &value
	return b
}
