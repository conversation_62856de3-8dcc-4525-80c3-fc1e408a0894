// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// CustomResponseApplyConfiguration represents a declarative configuration of the CustomResponse type for use
// with apply.
type CustomResponseApplyConfiguration struct {
	Message    *string `json:"message,omitempty"`
	StatusCode *uint32 `json:"statusCode,omitempty"`
}

// CustomResponseApplyConfiguration constructs a declarative configuration of the CustomResponse type for use with
// apply.
func CustomResponse() *CustomResponseApplyConfiguration {
	return &CustomResponseApplyConfiguration{}
}

// WithMessage sets the Message field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Message field is set to the value of the last call.
func (b *CustomResponseApplyConfiguration) WithMessage(value string) *CustomResponseApplyConfiguration {
	b.Message = &value
	return b
}

// WithStatusCode sets the StatusCode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StatusCode field is set to the value of the last call.
func (b *CustomResponseApplyConfiguration) WithStatusCode(value uint32) *CustomResponseApplyConfiguration {
	b.StatusCode = &value
	return b
}
