// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"

	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// ExtAuthPolicyApplyConfiguration represents a declarative configuration of the ExtAuthPolicy type for use
// with apply.
type ExtAuthPolicyApplyConfiguration struct {
	ExtensionRef      *v1.LocalObjectReference          `json:"extensionRef,omitempty"`
	Enablement        *apiv1alpha1.ExtAuthEnabled       `json:"enablement,omitempty"`
	WithRequestBody   *BufferSettingsApplyConfiguration `json:"withRequestBody,omitempty"`
	ContextExtensions map[string]string                 `json:"contextExtensions,omitempty"`
}

// ExtAuthPolicyApplyConfiguration constructs a declarative configuration of the ExtAuthPolicy type for use with
// apply.
func ExtAuthPolicy() *ExtAuthPolicyApplyConfiguration {
	return &ExtAuthPolicyApplyConfiguration{}
}

// WithExtensionRef sets the ExtensionRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtensionRef field is set to the value of the last call.
func (b *ExtAuthPolicyApplyConfiguration) WithExtensionRef(value v1.LocalObjectReference) *ExtAuthPolicyApplyConfiguration {
	b.ExtensionRef = &value
	return b
}

// WithEnablement sets the Enablement field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Enablement field is set to the value of the last call.
func (b *ExtAuthPolicyApplyConfiguration) WithEnablement(value apiv1alpha1.ExtAuthEnabled) *ExtAuthPolicyApplyConfiguration {
	b.Enablement = &value
	return b
}

// WithWithRequestBody sets the WithRequestBody field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the WithRequestBody field is set to the value of the last call.
func (b *ExtAuthPolicyApplyConfiguration) WithWithRequestBody(value *BufferSettingsApplyConfiguration) *ExtAuthPolicyApplyConfiguration {
	b.WithRequestBody = value
	return b
}

// WithContextExtensions puts the entries into the ContextExtensions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ContextExtensions field,
// overwriting an existing map entries in ContextExtensions field with the same key.
func (b *ExtAuthPolicyApplyConfiguration) WithContextExtensions(entries map[string]string) *ExtAuthPolicyApplyConfiguration {
	if b.ContextExtensions == nil && len(entries) > 0 {
		b.ContextExtensions = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ContextExtensions[k] = v
	}
	return b
}
