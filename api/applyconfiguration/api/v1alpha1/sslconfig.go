// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// SSLConfigApplyConfiguration represents a declarative configuration of the SSLConfig type for use
// with apply.
type SSLConfigApplyConfiguration struct {
	SecretRef            *v1.LocalObjectReference         `json:"secretRef,omitempty"`
	SSLFiles             *SSLFilesApplyConfiguration      `json:"sslFiles,omitempty"`
	Sni                  *string                          `json:"sni,omitempty"`
	VerifySubjectAltName []string                         `json:"verifySubjectAltName,omitempty"`
	SSLParameters        *SSLParametersApplyConfiguration `json:"sslParameters,omitempty"`
	AlpnProtocols        []string                         `json:"alpnProtocols,omitempty"`
	AllowRenegotiation   *bool                            `json:"allowRenegotiation,omitempty"`
	OneWayTLS            *bool                            `json:"oneWayTLS,omitempty"`
}

// SSLConfigApplyConfiguration constructs a declarative configuration of the SSLConfig type for use with
// apply.
func SSLConfig() *SSLConfigApplyConfiguration {
	return &SSLConfigApplyConfiguration{}
}

// WithSecretRef sets the SecretRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecretRef field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithSecretRef(value v1.LocalObjectReference) *SSLConfigApplyConfiguration {
	b.SecretRef = &value
	return b
}

// WithSSLFiles sets the SSLFiles field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SSLFiles field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithSSLFiles(value *SSLFilesApplyConfiguration) *SSLConfigApplyConfiguration {
	b.SSLFiles = value
	return b
}

// WithSni sets the Sni field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Sni field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithSni(value string) *SSLConfigApplyConfiguration {
	b.Sni = &value
	return b
}

// WithVerifySubjectAltName adds the given value to the VerifySubjectAltName field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the VerifySubjectAltName field.
func (b *SSLConfigApplyConfiguration) WithVerifySubjectAltName(values ...string) *SSLConfigApplyConfiguration {
	for i := range values {
		b.VerifySubjectAltName = append(b.VerifySubjectAltName, values[i])
	}
	return b
}

// WithSSLParameters sets the SSLParameters field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SSLParameters field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithSSLParameters(value *SSLParametersApplyConfiguration) *SSLConfigApplyConfiguration {
	b.SSLParameters = value
	return b
}

// WithAlpnProtocols adds the given value to the AlpnProtocols field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AlpnProtocols field.
func (b *SSLConfigApplyConfiguration) WithAlpnProtocols(values ...string) *SSLConfigApplyConfiguration {
	for i := range values {
		b.AlpnProtocols = append(b.AlpnProtocols, values[i])
	}
	return b
}

// WithAllowRenegotiation sets the AllowRenegotiation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AllowRenegotiation field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithAllowRenegotiation(value bool) *SSLConfigApplyConfiguration {
	b.AllowRenegotiation = &value
	return b
}

// WithOneWayTLS sets the OneWayTLS field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the OneWayTLS field is set to the value of the last call.
func (b *SSLConfigApplyConfiguration) WithOneWayTLS(value bool) *SSLConfigApplyConfiguration {
	b.OneWayTLS = &value
	return b
}
