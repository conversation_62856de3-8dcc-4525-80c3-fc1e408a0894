// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// TokenBucketApplyConfiguration represents a declarative configuration of the TokenBucket type for use
// with apply.
type TokenBucketApplyConfiguration struct {
	MaxTokens     *uint32      `json:"maxTokens,omitempty"`
	TokensPerFill *uint32      `json:"tokensPerFill,omitempty"`
	FillInterval  *v1.Duration `json:"fillInterval,omitempty"`
}

// TokenBucketApplyConfiguration constructs a declarative configuration of the TokenBucket type for use with
// apply.
func TokenBucket() *TokenBucketApplyConfiguration {
	return &TokenBucketApplyConfiguration{}
}

// WithMaxTokens sets the MaxTokens field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxTokens field is set to the value of the last call.
func (b *TokenBucketApplyConfiguration) WithMaxTokens(value uint32) *TokenBucketApplyConfiguration {
	b.MaxTokens = &value
	return b
}

// WithTokensPerFill sets the TokensPerFill field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TokensPerFill field is set to the value of the last call.
func (b *TokenBucketApplyConfiguration) WithTokensPerFill(value uint32) *TokenBucketApplyConfiguration {
	b.TokensPerFill = &value
	return b
}

// WithFillInterval sets the FillInterval field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FillInterval field is set to the value of the last call.
func (b *TokenBucketApplyConfiguration) WithFillInterval(value v1.Duration) *TokenBucketApplyConfiguration {
	b.FillInterval = &value
	return b
}
