// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// RateLimitPolicyApplyConfiguration represents a declarative configuration of the RateLimitPolicy type for use
// with apply.
type RateLimitPolicyApplyConfiguration struct {
	Descriptors  []RateLimitDescriptorApplyConfiguration `json:"descriptors,omitempty"`
	ExtensionRef *v1.LocalObjectReference                `json:"extensionRef,omitempty"`
}

// RateLimitPolicyApplyConfiguration constructs a declarative configuration of the RateLimitPolicy type for use with
// apply.
func RateLimitPolicy() *RateLimitPolicyApplyConfiguration {
	return &RateLimitPolicyApplyConfiguration{}
}

// WithDescriptors adds the given value to the Descriptors field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Descriptors field.
func (b *RateLimitPolicyApplyConfiguration) WithDescriptors(values ...*RateLimitDescriptorApplyConfiguration) *RateLimitPolicyApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithDescriptors")
		}
		b.Descriptors = append(b.Descriptors, *values[i])
	}
	return b
}

// WithExtensionRef sets the ExtensionRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtensionRef field is set to the value of the last call.
func (b *RateLimitPolicyApplyConfiguration) WithExtensionRef(value v1.LocalObjectReference) *RateLimitPolicyApplyConfiguration {
	b.ExtensionRef = &value
	return b
}
