// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// PathOverrideApplyConfiguration represents a declarative configuration of the PathOverride type for use
// with apply.
type PathOverrideApplyConfiguration struct {
	FullPath *string `json:"fullPath,omitempty"`
}

// PathOverrideApplyConfiguration constructs a declarative configuration of the PathOverride type for use with
// apply.
func PathOverride() *PathOverrideApplyConfiguration {
	return &PathOverrideApplyConfiguration{}
}

// WithFullPath sets the FullPath field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the FullPath field is set to the value of the last call.
func (b *PathOverrideApplyConfiguration) WithFullPath(value string) *PathOverrideApplyConfiguration {
	b.FullPath = &value
	return b
}
