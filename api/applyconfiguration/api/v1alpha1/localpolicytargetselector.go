// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// LocalPolicyTargetSelectorApplyConfiguration represents a declarative configuration of the LocalPolicyTargetSelector type for use
// with apply.
type LocalPolicyTargetSelectorApplyConfiguration struct {
	Group       *v1.Group         `json:"group,omitempty"`
	Kind        *v1.Kind          `json:"kind,omitempty"`
	MatchLabels map[string]string `json:"matchLabels,omitempty"`
}

// LocalPolicyTargetSelectorApplyConfiguration constructs a declarative configuration of the LocalPolicyTargetSelector type for use with
// apply.
func LocalPolicyTargetSelector() *LocalPolicyTargetSelectorApplyConfiguration {
	return &LocalPolicyTargetSelectorApplyConfiguration{}
}

// WithGroup sets the Group field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Group field is set to the value of the last call.
func (b *LocalPolicyTargetSelectorApplyConfiguration) WithGroup(value v1.Group) *LocalPolicyTargetSelectorApplyConfiguration {
	b.Group = &value
	return b
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *LocalPolicyTargetSelectorApplyConfiguration) WithKind(value v1.Kind) *LocalPolicyTargetSelectorApplyConfiguration {
	b.Kind = &value
	return b
}

// WithMatchLabels puts the entries into the MatchLabels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the MatchLabels field,
// overwriting an existing map entries in MatchLabels field with the same key.
func (b *LocalPolicyTargetSelectorApplyConfiguration) WithMatchLabels(entries map[string]string) *LocalPolicyTargetSelectorApplyConfiguration {
	if b.MatchLabels == nil && len(entries) > 0 {
		b.MatchLabels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.MatchLabels[k] = v
	}
	return b
}
