// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// AIPolicyApplyConfiguration represents a declarative configuration of the AIPolicy type for use
// with apply.
type AIPolicyApplyConfiguration struct {
	PromptEnrichment *AIPromptEnrichmentApplyConfiguration `json:"promptEnrichment,omitempty"`
	PromptGuard      *AIPromptGuardApplyConfiguration      `json:"promptGuard,omitempty"`
	Defaults         []FieldDefaultApplyConfiguration      `json:"defaults,omitempty"`
	RouteType        *apiv1alpha1.RouteType                `json:"routeType,omitempty"`
}

// AIPolicyApplyConfiguration constructs a declarative configuration of the AIPolicy type for use with
// apply.
func AIPolicy() *AIPolicyApplyConfiguration {
	return &AIPolicyApplyConfiguration{}
}

// WithPromptEnrichment sets the PromptEnrichment field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PromptEnrichment field is set to the value of the last call.
func (b *AIPolicyApplyConfiguration) WithPromptEnrichment(value *AIPromptEnrichmentApplyConfiguration) *AIPolicyApplyConfiguration {
	b.PromptEnrichment = value
	return b
}

// WithPromptGuard sets the PromptGuard field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PromptGuard field is set to the value of the last call.
func (b *AIPolicyApplyConfiguration) WithPromptGuard(value *AIPromptGuardApplyConfiguration) *AIPolicyApplyConfiguration {
	b.PromptGuard = value
	return b
}

// WithDefaults adds the given value to the Defaults field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Defaults field.
func (b *AIPolicyApplyConfiguration) WithDefaults(values ...*FieldDefaultApplyConfiguration) *AIPolicyApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithDefaults")
		}
		b.Defaults = append(b.Defaults, *values[i])
	}
	return b
}

// WithRouteType sets the RouteType field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RouteType field is set to the value of the last call.
func (b *AIPolicyApplyConfiguration) WithRouteType(value apiv1alpha1.RouteType) *AIPolicyApplyConfiguration {
	b.RouteType = &value
	return b
}
