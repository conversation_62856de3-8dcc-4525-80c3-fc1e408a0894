// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RegexMatchApplyConfiguration represents a declarative configuration of the RegexMatch type for use
// with apply.
type RegexMatchApplyConfiguration struct {
	Pattern *string `json:"pattern,omitempty"`
	Name    *string `json:"name,omitempty"`
}

// RegexMatchApplyConfiguration constructs a declarative configuration of the RegexMatch type for use with
// apply.
func RegexMatch() *RegexMatchApplyConfiguration {
	return &RegexMatchApplyConfiguration{}
}

// WithPattern sets the Pattern field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Pattern field is set to the value of the last call.
func (b *RegexMatchApplyConfiguration) WithPattern(value string) *RegexMatchApplyConfiguration {
	b.Pattern = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *RegexMatchApplyConfiguration) WithName(value string) *RegexMatchApplyConfiguration {
	b.Name = &value
	return b
}
