// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// SdsContainerApplyConfiguration represents a declarative configuration of the SdsContainer type for use
// with apply.
type SdsContainerApplyConfiguration struct {
	Image           *ImageApplyConfiguration        `json:"image,omitempty"`
	SecurityContext *v1.SecurityContext             `json:"securityContext,omitempty"`
	Resources       *v1.ResourceRequirements        `json:"resources,omitempty"`
	Bootstrap       *SdsBootstrapApplyConfiguration `json:"bootstrap,omitempty"`
}

// SdsContainerApplyConfiguration constructs a declarative configuration of the SdsContainer type for use with
// apply.
func SdsContainer() *SdsContainerApplyConfiguration {
	return &SdsContainerApplyConfiguration{}
}

// WithImage sets the Image field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Image field is set to the value of the last call.
func (b *SdsContainerApplyConfiguration) WithImage(value *ImageApplyConfiguration) *SdsContainerApplyConfiguration {
	b.Image = value
	return b
}

// WithSecurityContext sets the SecurityContext field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecurityContext field is set to the value of the last call.
func (b *SdsContainerApplyConfiguration) WithSecurityContext(value v1.SecurityContext) *SdsContainerApplyConfiguration {
	b.SecurityContext = &value
	return b
}

// WithResources sets the Resources field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Resources field is set to the value of the last call.
func (b *SdsContainerApplyConfiguration) WithResources(value v1.ResourceRequirements) *SdsContainerApplyConfiguration {
	b.Resources = &value
	return b
}

// WithBootstrap sets the Bootstrap field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Bootstrap field is set to the value of the last call.
func (b *SdsContainerApplyConfiguration) WithBootstrap(value *SdsBootstrapApplyConfiguration) *SdsContainerApplyConfiguration {
	b.Bootstrap = value
	return b
}
