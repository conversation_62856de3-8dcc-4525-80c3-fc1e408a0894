// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	managedfields "k8s.io/apimachinery/pkg/util/managedfields"
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
	v1alpha2 "sigs.k8s.io/gateway-api/apis/v1alpha2"

	internal "github.com/kgateway-dev/kgateway/v2/api/applyconfiguration/internal"
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// BackendConfigPolicyApplyConfiguration represents a declarative configuration of the BackendConfigPolicy type for use
// with apply.
type BackendConfigPolicyApplyConfiguration struct {
	v1.TypeMetaApplyConfiguration    `json:",inline"`
	*v1.ObjectMetaApplyConfiguration `json:"metadata,omitempty"`
	Spec                             *BackendConfigPolicySpecApplyConfiguration `json:"spec,omitempty"`
	Status                           *v1alpha2.PolicyStatus                     `json:"status,omitempty"`
}

// BackendConfigPolicy constructs a declarative configuration of the BackendConfigPolicy type for use with
// apply.
func BackendConfigPolicy(name, namespace string) *BackendConfigPolicyApplyConfiguration {
	b := &BackendConfigPolicyApplyConfiguration{}
	b.WithName(name)
	b.WithNamespace(namespace)
	b.WithKind("BackendConfigPolicy")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b
}

// ExtractBackendConfigPolicy extracts the applied configuration owned by fieldManager from
// backendConfigPolicy. If no managedFields are found in backendConfigPolicy for fieldManager, a
// BackendConfigPolicyApplyConfiguration is returned with only the Name, Namespace (if applicable),
// APIVersion and Kind populated. It is possible that no managed fields were found for because other
// field managers have taken ownership of all the fields previously owned by fieldManager, or because
// the fieldManager never owned fields any fields.
// backendConfigPolicy must be a unmodified BackendConfigPolicy API object that was retrieved from the Kubernetes API.
// ExtractBackendConfigPolicy provides a way to perform a extract/modify-in-place/apply workflow.
// Note that an extracted apply configuration will contain fewer fields than what the fieldManager previously
// applied if another fieldManager has updated or force applied any of the previously applied fields.
// Experimental!
func ExtractBackendConfigPolicy(backendConfigPolicy *apiv1alpha1.BackendConfigPolicy, fieldManager string) (*BackendConfigPolicyApplyConfiguration, error) {
	return extractBackendConfigPolicy(backendConfigPolicy, fieldManager, "")
}

// ExtractBackendConfigPolicyStatus is the same as ExtractBackendConfigPolicy except
// that it extracts the status subresource applied configuration.
// Experimental!
func ExtractBackendConfigPolicyStatus(backendConfigPolicy *apiv1alpha1.BackendConfigPolicy, fieldManager string) (*BackendConfigPolicyApplyConfiguration, error) {
	return extractBackendConfigPolicy(backendConfigPolicy, fieldManager, "status")
}

func extractBackendConfigPolicy(backendConfigPolicy *apiv1alpha1.BackendConfigPolicy, fieldManager string, subresource string) (*BackendConfigPolicyApplyConfiguration, error) {
	b := &BackendConfigPolicyApplyConfiguration{}
	err := managedfields.ExtractInto(backendConfigPolicy, internal.Parser().Type("com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendConfigPolicy"), fieldManager, b, subresource)
	if err != nil {
		return nil, err
	}
	b.WithName(backendConfigPolicy.Name)
	b.WithNamespace(backendConfigPolicy.Namespace)

	b.WithKind("BackendConfigPolicy")
	b.WithAPIVersion("gateway.kgateway.dev/v1alpha1")
	return b, nil
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithKind(value string) *BackendConfigPolicyApplyConfiguration {
	b.TypeMetaApplyConfiguration.Kind = &value
	return b
}

// WithAPIVersion sets the APIVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the APIVersion field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithAPIVersion(value string) *BackendConfigPolicyApplyConfiguration {
	b.TypeMetaApplyConfiguration.APIVersion = &value
	return b
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithName(value string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Name = &value
	return b
}

// WithGenerateName sets the GenerateName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GenerateName field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithGenerateName(value string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.GenerateName = &value
	return b
}

// WithNamespace sets the Namespace field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Namespace field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithNamespace(value string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Namespace = &value
	return b
}

// WithUID sets the UID field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UID field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithUID(value types.UID) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.UID = &value
	return b
}

// WithResourceVersion sets the ResourceVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResourceVersion field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithResourceVersion(value string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.ResourceVersion = &value
	return b
}

// WithGeneration sets the Generation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Generation field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithGeneration(value int64) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.Generation = &value
	return b
}

// WithCreationTimestamp sets the CreationTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CreationTimestamp field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithCreationTimestamp(value metav1.Time) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.CreationTimestamp = &value
	return b
}

// WithDeletionTimestamp sets the DeletionTimestamp field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionTimestamp field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithDeletionTimestamp(value metav1.Time) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionTimestamp = &value
	return b
}

// WithDeletionGracePeriodSeconds sets the DeletionGracePeriodSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionGracePeriodSeconds field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithDeletionGracePeriodSeconds(value int64) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	b.ObjectMetaApplyConfiguration.DeletionGracePeriodSeconds = &value
	return b
}

// WithLabels puts the entries into the Labels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Labels field,
// overwriting an existing map entries in Labels field with the same key.
func (b *BackendConfigPolicyApplyConfiguration) WithLabels(entries map[string]string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Labels == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Labels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Labels[k] = v
	}
	return b
}

// WithAnnotations puts the entries into the Annotations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Annotations field,
// overwriting an existing map entries in Annotations field with the same key.
func (b *BackendConfigPolicyApplyConfiguration) WithAnnotations(entries map[string]string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	if b.ObjectMetaApplyConfiguration.Annotations == nil && len(entries) > 0 {
		b.ObjectMetaApplyConfiguration.Annotations = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ObjectMetaApplyConfiguration.Annotations[k] = v
	}
	return b
}

// WithOwnerReferences adds the given value to the OwnerReferences field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the OwnerReferences field.
func (b *BackendConfigPolicyApplyConfiguration) WithOwnerReferences(values ...*v1.OwnerReferenceApplyConfiguration) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithOwnerReferences")
		}
		b.ObjectMetaApplyConfiguration.OwnerReferences = append(b.ObjectMetaApplyConfiguration.OwnerReferences, *values[i])
	}
	return b
}

// WithFinalizers adds the given value to the Finalizers field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Finalizers field.
func (b *BackendConfigPolicyApplyConfiguration) WithFinalizers(values ...string) *BackendConfigPolicyApplyConfiguration {
	b.ensureObjectMetaApplyConfigurationExists()
	for i := range values {
		b.ObjectMetaApplyConfiguration.Finalizers = append(b.ObjectMetaApplyConfiguration.Finalizers, values[i])
	}
	return b
}

func (b *BackendConfigPolicyApplyConfiguration) ensureObjectMetaApplyConfigurationExists() {
	if b.ObjectMetaApplyConfiguration == nil {
		b.ObjectMetaApplyConfiguration = &v1.ObjectMetaApplyConfiguration{}
	}
}

// WithSpec sets the Spec field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Spec field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithSpec(value *BackendConfigPolicySpecApplyConfiguration) *BackendConfigPolicyApplyConfiguration {
	b.Spec = value
	return b
}

// WithStatus sets the Status field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Status field is set to the value of the last call.
func (b *BackendConfigPolicyApplyConfiguration) WithStatus(value v1alpha2.PolicyStatus) *BackendConfigPolicyApplyConfiguration {
	b.Status = &value
	return b
}

// GetName retrieves the value of the Name field in the declarative configuration.
func (b *BackendConfigPolicyApplyConfiguration) GetName() *string {
	b.ensureObjectMetaApplyConfigurationExists()
	return b.ObjectMetaApplyConfiguration.Name
}
