// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// ExtGrpcServiceApplyConfiguration represents a declarative configuration of the ExtGrpcService type for use
// with apply.
type ExtGrpcServiceApplyConfiguration struct {
	BackendRef *v1.BackendRef `json:"backendRef,omitempty"`
	Authority  *string        `json:"authority,omitempty"`
}

// ExtGrpcServiceApplyConfiguration constructs a declarative configuration of the ExtGrpcService type for use with
// apply.
func ExtGrpcService() *ExtGrpcServiceApplyConfiguration {
	return &ExtGrpcServiceApplyConfiguration{}
}

// WithBackendRef sets the BackendRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the BackendRef field is set to the value of the last call.
func (b *ExtGrpcServiceApplyConfiguration) WithBackendRef(value v1.BackendRef) *ExtGrpcServiceApplyConfiguration {
	b.BackendRef = &value
	return b
}

// WithAuthority sets the Authority field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Authority field is set to the value of the last call.
func (b *ExtGrpcServiceApplyConfiguration) WithAuthority(value string) *ExtGrpcServiceApplyConfiguration {
	b.Authority = &value
	return b
}
