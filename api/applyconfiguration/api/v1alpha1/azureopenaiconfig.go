// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AzureOpenAIConfigApplyConfiguration represents a declarative configuration of the AzureOpenAIConfig type for use
// with apply.
type AzureOpenAIConfigApplyConfiguration struct {
	AuthToken      *SingleAuthTokenApplyConfiguration `json:"authToken,omitempty"`
	Endpoint       *string                            `json:"endpoint,omitempty"`
	DeploymentName *string                            `json:"deploymentName,omitempty"`
	ApiVersion     *string                            `json:"apiVersion,omitempty"`
}

// AzureOpenAIConfigApplyConfiguration constructs a declarative configuration of the AzureOpenAIConfig type for use with
// apply.
func AzureOpenAIConfig() *AzureOpenAIConfigApplyConfiguration {
	return &AzureOpenAIConfigApplyConfiguration{}
}

// WithAuthToken sets the AuthToken field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AuthToken field is set to the value of the last call.
func (b *AzureOpenAIConfigApplyConfiguration) WithAuthToken(value *SingleAuthTokenApplyConfiguration) *AzureOpenAIConfigApplyConfiguration {
	b.AuthToken = value
	return b
}

// WithEndpoint sets the Endpoint field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Endpoint field is set to the value of the last call.
func (b *AzureOpenAIConfigApplyConfiguration) WithEndpoint(value string) *AzureOpenAIConfigApplyConfiguration {
	b.Endpoint = &value
	return b
}

// WithDeploymentName sets the DeploymentName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeploymentName field is set to the value of the last call.
func (b *AzureOpenAIConfigApplyConfiguration) WithDeploymentName(value string) *AzureOpenAIConfigApplyConfiguration {
	b.DeploymentName = &value
	return b
}

// WithApiVersion sets the ApiVersion field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ApiVersion field is set to the value of the last call.
func (b *AzureOpenAIConfigApplyConfiguration) WithApiVersion(value string) *AzureOpenAIConfigApplyConfiguration {
	b.ApiVersion = &value
	return b
}
