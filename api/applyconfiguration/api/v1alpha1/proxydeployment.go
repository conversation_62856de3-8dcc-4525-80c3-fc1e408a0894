// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// ProxyDeploymentApplyConfiguration represents a declarative configuration of the ProxyDeployment type for use
// with apply.
type ProxyDeploymentApplyConfiguration struct {
	Replicas *uint32 `json:"replicas,omitempty"`
}

// ProxyDeploymentApplyConfiguration constructs a declarative configuration of the ProxyDeployment type for use with
// apply.
func ProxyDeployment() *ProxyDeploymentApplyConfiguration {
	return &ProxyDeploymentApplyConfiguration{}
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *ProxyDeploymentApplyConfiguration) WithReplicas(value uint32) *ProxyDeploymentApplyConfiguration {
	b.Replicas = &value
	return b
}
