// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// HeaderFilterApplyConfiguration represents a declarative configuration of the HeaderFilter type for use
// with apply.
type HeaderFilterApplyConfiguration struct {
	Header *v1.HTTPHeaderMatch `json:"header,omitempty"`
}

// HeaderFilterApplyConfiguration constructs a declarative configuration of the HeaderFilter type for use with
// apply.
func HeaderFilter() *HeaderFilterApplyConfiguration {
	return &HeaderFilterApplyConfiguration{}
}

// WithHeader sets the Header field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Header field is set to the value of the last call.
func (b *HeaderFilterApplyConfiguration) WithHeader(value v1.HTTPHeaderMatch) *HeaderFilterApplyConfiguration {
	b.Header = &value
	return b
}
