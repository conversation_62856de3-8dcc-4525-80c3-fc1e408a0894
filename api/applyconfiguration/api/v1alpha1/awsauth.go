// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"

	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// AwsAuthApplyConfiguration represents a declarative configuration of the AwsAuth type for use
// with apply.
type AwsAuthApplyConfiguration struct {
	Type      *apiv1alpha1.AwsAuthType `json:"type,omitempty"`
	SecretRef *v1.LocalObjectReference `json:"secretRef,omitempty"`
}

// AwsAuthApplyConfiguration constructs a declarative configuration of the AwsAuth type for use with
// apply.
func AwsAuth() *AwsAuthApplyConfiguration {
	return &AwsAuthApplyConfiguration{}
}

// WithType sets the Type field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Type field is set to the value of the last call.
func (b *AwsAuthApplyConfiguration) WithType(value apiv1alpha1.AwsAuthType) *AwsAuthApplyConfiguration {
	b.Type = &value
	return b
}

// WithSecretRef sets the SecretRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecretRef field is set to the value of the last call.
func (b *AwsAuthApplyConfiguration) WithSecretRef(value v1.LocalObjectReference) *AwsAuthApplyConfiguration {
	b.SecretRef = &value
	return b
}
