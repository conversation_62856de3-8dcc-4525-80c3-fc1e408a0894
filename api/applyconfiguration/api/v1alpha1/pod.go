// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
)

// PodApplyConfiguration represents a declarative configuration of the Pod type for use
// with apply.
type PodApplyConfiguration struct {
	ExtraLabels                   map[string]string                       `json:"extraLabels,omitempty"`
	ExtraAnnotations              map[string]string                       `json:"extraAnnotations,omitempty"`
	SecurityContext               *v1.PodSecurityContext                  `json:"securityContext,omitempty"`
	ImagePullSecrets              []v1.LocalObjectReference               `json:"imagePullSecrets,omitempty"`
	NodeSelector                  map[string]string                       `json:"nodeSelector,omitempty"`
	Affinity                      *v1.Affinity                            `json:"affinity,omitempty"`
	Tolerations                   []v1.Toleration                         `json:"tolerations,omitempty"`
	GracefulShutdown              *GracefulShutdownSpecApplyConfiguration `json:"gracefulShutdown,omitempty"`
	TerminationGracePeriodSeconds *int                                    `json:"terminationGracePeriodSeconds,omitempty"`
	ReadinessProbe                *v1.Probe                               `json:"readinessProbe,omitempty"`
	LivenessProbe                 *v1.Probe                               `json:"livenessProbe,omitempty"`
}

// PodApplyConfiguration constructs a declarative configuration of the Pod type for use with
// apply.
func Pod() *PodApplyConfiguration {
	return &PodApplyConfiguration{}
}

// WithExtraLabels puts the entries into the ExtraLabels field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ExtraLabels field,
// overwriting an existing map entries in ExtraLabels field with the same key.
func (b *PodApplyConfiguration) WithExtraLabels(entries map[string]string) *PodApplyConfiguration {
	if b.ExtraLabels == nil && len(entries) > 0 {
		b.ExtraLabels = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ExtraLabels[k] = v
	}
	return b
}

// WithExtraAnnotations puts the entries into the ExtraAnnotations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ExtraAnnotations field,
// overwriting an existing map entries in ExtraAnnotations field with the same key.
func (b *PodApplyConfiguration) WithExtraAnnotations(entries map[string]string) *PodApplyConfiguration {
	if b.ExtraAnnotations == nil && len(entries) > 0 {
		b.ExtraAnnotations = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ExtraAnnotations[k] = v
	}
	return b
}

// WithSecurityContext sets the SecurityContext field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecurityContext field is set to the value of the last call.
func (b *PodApplyConfiguration) WithSecurityContext(value v1.PodSecurityContext) *PodApplyConfiguration {
	b.SecurityContext = &value
	return b
}

// WithImagePullSecrets adds the given value to the ImagePullSecrets field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the ImagePullSecrets field.
func (b *PodApplyConfiguration) WithImagePullSecrets(values ...v1.LocalObjectReference) *PodApplyConfiguration {
	for i := range values {
		b.ImagePullSecrets = append(b.ImagePullSecrets, values[i])
	}
	return b
}

// WithNodeSelector puts the entries into the NodeSelector field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the NodeSelector field,
// overwriting an existing map entries in NodeSelector field with the same key.
func (b *PodApplyConfiguration) WithNodeSelector(entries map[string]string) *PodApplyConfiguration {
	if b.NodeSelector == nil && len(entries) > 0 {
		b.NodeSelector = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.NodeSelector[k] = v
	}
	return b
}

// WithAffinity sets the Affinity field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Affinity field is set to the value of the last call.
func (b *PodApplyConfiguration) WithAffinity(value v1.Affinity) *PodApplyConfiguration {
	b.Affinity = &value
	return b
}

// WithTolerations adds the given value to the Tolerations field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Tolerations field.
func (b *PodApplyConfiguration) WithTolerations(values ...v1.Toleration) *PodApplyConfiguration {
	for i := range values {
		b.Tolerations = append(b.Tolerations, values[i])
	}
	return b
}

// WithGracefulShutdown sets the GracefulShutdown field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GracefulShutdown field is set to the value of the last call.
func (b *PodApplyConfiguration) WithGracefulShutdown(value *GracefulShutdownSpecApplyConfiguration) *PodApplyConfiguration {
	b.GracefulShutdown = value
	return b
}

// WithTerminationGracePeriodSeconds sets the TerminationGracePeriodSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TerminationGracePeriodSeconds field is set to the value of the last call.
func (b *PodApplyConfiguration) WithTerminationGracePeriodSeconds(value int) *PodApplyConfiguration {
	b.TerminationGracePeriodSeconds = &value
	return b
}

// WithReadinessProbe sets the ReadinessProbe field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ReadinessProbe field is set to the value of the last call.
func (b *PodApplyConfiguration) WithReadinessProbe(value v1.Probe) *PodApplyConfiguration {
	b.ReadinessProbe = &value
	return b
}

// WithLivenessProbe sets the LivenessProbe field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LivenessProbe field is set to the value of the last call.
func (b *PodApplyConfiguration) WithLivenessProbe(value v1.Probe) *PodApplyConfiguration {
	b.LivenessProbe = &value
	return b
}
