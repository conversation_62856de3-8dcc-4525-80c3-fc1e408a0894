// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// RateLimitDescriptorEntryApplyConfiguration represents a declarative configuration of the RateLimitDescriptorEntry type for use
// with apply.
type RateLimitDescriptorEntryApplyConfiguration struct {
	Type    *apiv1alpha1.RateLimitDescriptorEntryType          `json:"type,omitempty"`
	Generic *RateLimitDescriptorEntryGenericApplyConfiguration `json:"generic,omitempty"`
	Header  *string                                            `json:"header,omitempty"`
}

// RateLimitDescriptorEntryApplyConfiguration constructs a declarative configuration of the RateLimitDescriptorEntry type for use with
// apply.
func RateLimitDescriptorEntry() *RateLimitDescriptorEntryApplyConfiguration {
	return &RateLimitDescriptorEntryApplyConfiguration{}
}

// WithType sets the Type field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Type field is set to the value of the last call.
func (b *RateLimitDescriptorEntryApplyConfiguration) WithType(value apiv1alpha1.RateLimitDescriptorEntryType) *RateLimitDescriptorEntryApplyConfiguration {
	b.Type = &value
	return b
}

// WithGeneric sets the Generic field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Generic field is set to the value of the last call.
func (b *RateLimitDescriptorEntryApplyConfiguration) WithGeneric(value *RateLimitDescriptorEntryGenericApplyConfiguration) *RateLimitDescriptorEntryApplyConfiguration {
	b.Generic = value
	return b
}

// WithHeader sets the Header field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Header field is set to the value of the last call.
func (b *RateLimitDescriptorEntryApplyConfiguration) WithHeader(value string) *RateLimitDescriptorEntryApplyConfiguration {
	b.Header = &value
	return b
}
