// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// MessageApplyConfiguration represents a declarative configuration of the Message type for use
// with apply.
type MessageApplyConfiguration struct {
	Role    *string `json:"role,omitempty"`
	Content *string `json:"content,omitempty"`
}

// MessageApplyConfiguration constructs a declarative configuration of the Message type for use with
// apply.
func Message() *MessageApplyConfiguration {
	return &MessageApplyConfiguration{}
}

// WithRole sets the Role field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Role field is set to the value of the last call.
func (b *MessageApplyConfiguration) WithRole(value string) *MessageApplyConfiguration {
	b.Role = &value
	return b
}

// WithContent sets the Content field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Content field is set to the value of the last call.
func (b *MessageApplyConfiguration) WithContent(value string) *MessageApplyConfiguration {
	b.Content = &value
	return b
}
