// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	apiv1alpha1 "github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
)

// GatewayExtensionSpecApplyConfiguration represents a declarative configuration of the GatewayExtensionSpec type for use
// with apply.
type GatewayExtensionSpecApplyConfiguration struct {
	Type      *apiv1alpha1.GatewayExtensionType    `json:"type,omitempty"`
	ExtAuth   *ExtAuthProviderApplyConfiguration   `json:"extAuth,omitempty"`
	ExtProc   *ExtProcProviderApplyConfiguration   `json:"extProc,omitempty"`
	RateLimit *RateLimitProviderApplyConfiguration `json:"rateLimit,omitempty"`
}

// GatewayExtensionSpecApplyConfiguration constructs a declarative configuration of the GatewayExtensionSpec type for use with
// apply.
func GatewayExtensionSpec() *GatewayExtensionSpecApplyConfiguration {
	return &GatewayExtensionSpecApplyConfiguration{}
}

// WithType sets the Type field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Type field is set to the value of the last call.
func (b *GatewayExtensionSpecApplyConfiguration) WithType(value apiv1alpha1.GatewayExtensionType) *GatewayExtensionSpecApplyConfiguration {
	b.Type = &value
	return b
}

// WithExtAuth sets the ExtAuth field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtAuth field is set to the value of the last call.
func (b *GatewayExtensionSpecApplyConfiguration) WithExtAuth(value *ExtAuthProviderApplyConfiguration) *GatewayExtensionSpecApplyConfiguration {
	b.ExtAuth = value
	return b
}

// WithExtProc sets the ExtProc field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExtProc field is set to the value of the last call.
func (b *GatewayExtensionSpecApplyConfiguration) WithExtProc(value *ExtProcProviderApplyConfiguration) *GatewayExtensionSpecApplyConfiguration {
	b.ExtProc = value
	return b
}

// WithRateLimit sets the RateLimit field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RateLimit field is set to the value of the last call.
func (b *GatewayExtensionSpecApplyConfiguration) WithRateLimit(value *RateLimitProviderApplyConfiguration) *GatewayExtensionSpecApplyConfiguration {
	b.RateLimit = value
	return b
}
