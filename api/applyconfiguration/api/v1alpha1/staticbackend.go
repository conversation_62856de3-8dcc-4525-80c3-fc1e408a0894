// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// StaticBackendApplyConfiguration represents a declarative configuration of the StaticBackend type for use
// with apply.
type StaticBackendApplyConfiguration struct {
	Hosts []HostApplyConfiguration `json:"hosts,omitempty"`
}

// StaticBackendApplyConfiguration constructs a declarative configuration of the StaticBackend type for use with
// apply.
func StaticBackend() *StaticBackendApplyConfiguration {
	return &StaticBackendApplyConfiguration{}
}

// WithHosts adds the given value to the Hosts field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Hosts field.
func (b *StaticBackendApplyConfiguration) WithHosts(values ...*HostApplyConfiguration) *StaticBackendApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithHosts")
		}
		b.Hosts = append(b.Hosts, *values[i])
	}
	return b
}
