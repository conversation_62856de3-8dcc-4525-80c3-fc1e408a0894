// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AccessLogFilterApplyConfiguration represents a declarative configuration of the AccessLogFilter type for use
// with apply.
type AccessLogFilterApplyConfiguration struct {
	FilterTypeApplyConfiguration `json:",inline"`
	AndFilter                    []FilterTypeApplyConfiguration `json:"andFilter,omitempty"`
	OrFilter                     []FilterTypeApplyConfiguration `json:"orFilter,omitempty"`
}

// AccessLogFilterApplyConfiguration constructs a declarative configuration of the AccessLogFilter type for use with
// apply.
func AccessLogFilter() *AccessLogFilterApplyConfiguration {
	return &AccessLogFilterApplyConfiguration{}
}

// WithAndFilter adds the given value to the AndFilter field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AndFilter field.
func (b *AccessLogFilterApplyConfiguration) WithAndFilter(values ...*FilterTypeApplyConfiguration) *AccessLogFilterApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithAndFilter")
		}
		b.AndFilter = append(b.AndFilter, *values[i])
	}
	return b
}

// WithOrFilter adds the given value to the OrFilter field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the OrFilter field.
func (b *AccessLogFilterApplyConfiguration) WithOrFilter(values ...*FilterTypeApplyConfiguration) *AccessLogFilterApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithOrFilter")
		}
		b.OrFilter = append(b.OrFilter, *values[i])
	}
	return b
}
