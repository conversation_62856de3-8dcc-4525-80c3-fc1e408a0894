// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// AuthHeaderOverrideApplyConfiguration represents a declarative configuration of the AuthHeaderOverride type for use
// with apply.
type AuthHeaderOverrideApplyConfiguration struct {
	Prefix     *string `json:"prefix,omitempty"`
	HeaderName *string `json:"headerName,omitempty"`
}

// AuthHeaderOverrideApplyConfiguration constructs a declarative configuration of the AuthHeaderOverride type for use with
// apply.
func AuthHeaderOverride() *AuthHeaderOverrideApplyConfiguration {
	return &AuthHeaderOverrideApplyConfiguration{}
}

// WithPrefix sets the Prefix field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Prefix field is set to the value of the last call.
func (b *AuthHeaderOverrideApplyConfiguration) WithPrefix(value string) *AuthHeaderOverrideApplyConfiguration {
	b.Prefix = &value
	return b
}

// WithHeaderName sets the HeaderName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the HeaderName field is set to the value of the last call.
func (b *AuthHeaderOverrideApplyConfiguration) WithHeaderName(value string) *AuthHeaderOverrideApplyConfiguration {
	b.HeaderName = &value
	return b
}
