// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "sigs.k8s.io/gateway-api/apis/v1"
)

// GrpcServiceApplyConfiguration represents a declarative configuration of the GrpcService type for use
// with apply.
type GrpcServiceApplyConfiguration struct {
	LogName                         *string        `json:"logName,omitempty"`
	BackendRef                      *v1.BackendRef `json:"backendRef,omitempty"`
	AdditionalRequestHeadersToLog   []string       `json:"additionalRequestHeadersToLog,omitempty"`
	AdditionalResponseHeadersToLog  []string       `json:"additionalResponseHeadersToLog,omitempty"`
	AdditionalResponseTrailersToLog []string       `json:"additionalResponseTrailersToLog,omitempty"`
}

// GrpcServiceApplyConfiguration constructs a declarative configuration of the GrpcService type for use with
// apply.
func GrpcService() *GrpcServiceApplyConfiguration {
	return &GrpcServiceApplyConfiguration{}
}

// WithLogName sets the LogName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LogName field is set to the value of the last call.
func (b *GrpcServiceApplyConfiguration) WithLogName(value string) *GrpcServiceApplyConfiguration {
	b.LogName = &value
	return b
}

// WithBackendRef sets the BackendRef field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the BackendRef field is set to the value of the last call.
func (b *GrpcServiceApplyConfiguration) WithBackendRef(value v1.BackendRef) *GrpcServiceApplyConfiguration {
	b.BackendRef = &value
	return b
}

// WithAdditionalRequestHeadersToLog adds the given value to the AdditionalRequestHeadersToLog field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AdditionalRequestHeadersToLog field.
func (b *GrpcServiceApplyConfiguration) WithAdditionalRequestHeadersToLog(values ...string) *GrpcServiceApplyConfiguration {
	for i := range values {
		b.AdditionalRequestHeadersToLog = append(b.AdditionalRequestHeadersToLog, values[i])
	}
	return b
}

// WithAdditionalResponseHeadersToLog adds the given value to the AdditionalResponseHeadersToLog field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AdditionalResponseHeadersToLog field.
func (b *GrpcServiceApplyConfiguration) WithAdditionalResponseHeadersToLog(values ...string) *GrpcServiceApplyConfiguration {
	for i := range values {
		b.AdditionalResponseHeadersToLog = append(b.AdditionalResponseHeadersToLog, values[i])
	}
	return b
}

// WithAdditionalResponseTrailersToLog adds the given value to the AdditionalResponseTrailersToLog field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the AdditionalResponseTrailersToLog field.
func (b *GrpcServiceApplyConfiguration) WithAdditionalResponseTrailersToLog(values ...string) *GrpcServiceApplyConfiguration {
	for i := range values {
		b.AdditionalResponseTrailersToLog = append(b.AdditionalResponseTrailersToLog, values[i])
	}
	return b
}
