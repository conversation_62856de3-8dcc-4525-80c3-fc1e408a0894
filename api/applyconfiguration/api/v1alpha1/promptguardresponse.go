// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// PromptguardResponseApplyConfiguration represents a declarative configuration of the PromptguardResponse type for use
// with apply.
type PromptguardResponseApplyConfiguration struct {
	Regex   *RegexApplyConfiguration   `json:"regex,omitempty"`
	Webhook *WebhookApplyConfiguration `json:"webhook,omitempty"`
}

// PromptguardResponseApplyConfiguration constructs a declarative configuration of the PromptguardResponse type for use with
// apply.
func PromptguardResponse() *PromptguardResponseApplyConfiguration {
	return &PromptguardResponseApplyConfiguration{}
}

// WithRegex sets the Regex field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Regex field is set to the value of the last call.
func (b *PromptguardResponseApplyConfiguration) WithRegex(value *RegexApplyConfiguration) *PromptguardResponseApplyConfiguration {
	b.Regex = value
	return b
}

// WithWebhook sets the Webhook field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Webhook field is set to the value of the last call.
func (b *PromptguardResponseApplyConfiguration) WithWebhook(value *WebhookApplyConfiguration) *PromptguardResponseApplyConfiguration {
	b.Webhook = value
	return b
}
