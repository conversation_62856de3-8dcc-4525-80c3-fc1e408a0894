// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// DirectResponseSpecApplyConfiguration represents a declarative configuration of the DirectResponseSpec type for use
// with apply.
type DirectResponseSpecApplyConfiguration struct {
	StatusCode *uint32 `json:"status,omitempty"`
	Body       *string `json:"body,omitempty"`
}

// DirectResponseSpecApplyConfiguration constructs a declarative configuration of the DirectResponseSpec type for use with
// apply.
func DirectResponseSpec() *DirectResponseSpecApplyConfiguration {
	return &DirectResponseSpecApplyConfiguration{}
}

// WithStatusCode sets the StatusCode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StatusCode field is set to the value of the last call.
func (b *DirectResponseSpecApplyConfiguration) WithStatusCode(value uint32) *DirectResponseSpecApplyConfiguration {
	b.StatusCode = &value
	return b
}

// WithBody sets the Body field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Body field is set to the value of the last call.
func (b *DirectResponseSpecApplyConfiguration) WithBody(value string) *DirectResponseSpecApplyConfiguration {
	b.Body = &value
	return b
}
