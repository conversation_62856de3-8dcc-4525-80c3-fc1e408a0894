// Code generated by applyconfiguration-gen. DO NOT EDIT.

package internal

import (
	fmt "fmt"
	sync "sync"

	typed "sigs.k8s.io/structured-merge-diff/v4/typed"
)

func Parser() *typed.Parser {
	parserOnce.Do(func() {
		var err error
		parser, err = typed.NewParser(schemaYAML)
		if err != nil {
			panic(fmt.Sprintf("Failed to parse schema: %v", err))
		}
	})
	return parser
}

var parserOnce sync.Once
var parser *typed.Parser
var schemaYAML = typed.YAMLObject(`types:
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIBackend
  map:
    fields:
    - name: llm
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LLMProvider
    - name: multipool
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.MultiPoolConfig
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPolicy
  map:
    fields:
    - name: defaults
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FieldDefault
          elementRelationship: atomic
    - name: promptEnrichment
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPromptEnrichment
    - name: promptGuard
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPromptGuard
    - name: routeType
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPromptEnrichment
  map:
    fields:
    - name: append
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Message
          elementRelationship: atomic
    - name: prepend
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Message
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPromptGuard
  map:
    fields:
    - name: request
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PromptguardRequest
    - name: response
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PromptguardResponse
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AccessLog
  map:
    fields:
    - name: fileSink
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FileSink
    - name: filter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AccessLogFilter
    - name: grpcService
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GrpcService
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AccessLogFilter
  map:
    fields:
    - name: andFilter
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FilterType
          elementRelationship: atomic
    - name: celFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CELFilter
    - name: durationFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DurationFilter
    - name: grpcStatusFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GrpcStatusFilter
    - name: headerFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderFilter
    - name: notHealthCheckFilter
      type:
        scalar: boolean
    - name: orFilter
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FilterType
          elementRelationship: atomic
    - name: responseFlagFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ResponseFlagFilter
    - name: statusCodeFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StatusCodeFilter
    - name: traceableFilter
      type:
        scalar: boolean
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AgentGateway
  map:
    fields:
    - name: enabled
      type:
        scalar: boolean
    - name: logLevel
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AiExtension
  map:
    fields:
    - name: enabled
      type:
        scalar: boolean
    - name: env
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.EnvVar
          elementRelationship: atomic
    - name: image
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Image
    - name: ports
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.ContainerPort
          elementRelationship: atomic
    - name: resources
      type:
        namedType: io.k8s.api.core.v1.ResourceRequirements
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.SecurityContext
    - name: stats
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AiExtensionStats
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AiExtensionStats
  map:
    fields:
    - name: customLabels
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CustomLabel
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AnthropicConfig
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: authToken
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
      default: {}
    - name: model
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AuthHeaderOverride
  map:
    fields:
    - name: headerName
      type:
        scalar: string
    - name: prefix
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsAuth
  map:
    fields:
    - name: secretRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
    - name: type
      type:
        scalar: string
      default: ""
    unions:
    - discriminator: type
      fields:
      - fieldName: secretRef
        discriminatorValue: SecretRef
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsBackend
  map:
    fields:
    - name: accountId
      type:
        scalar: string
      default: ""
    - name: auth
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsAuth
    - name: lambda
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsLambda
    - name: region
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsLambda
  map:
    fields:
    - name: endpointURL
      type:
        scalar: string
    - name: functionName
      type:
        scalar: string
      default: ""
    - name: invocationMode
      type:
        scalar: string
    - name: qualifier
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AzureOpenAIConfig
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
      default: ""
    - name: authToken
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
      default: {}
    - name: deploymentName
      type:
        scalar: string
      default: ""
    - name: endpoint
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Backend
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendSpec
      default: {}
    - name: status
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendConfigPolicy
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendConfigPolicySpec
      default: {}
    - name: status
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendConfigPolicySpec
  map:
    fields:
    - name: commonHttpProtocolOptions
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CommonHttpProtocolOptions
    - name: connectTimeout
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
    - name: http1ProtocolOptions
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Http1ProtocolOptions
    - name: perConnectionBufferLimitBytes
      type:
        scalar: numeric
    - name: sslConfig
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLConfig
    - name: targetRefs
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetReference
          elementRelationship: atomic
    - name: targetSelectors
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetSelector
          elementRelationship: atomic
    - name: tcpKeepalive
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TCPKeepalive
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendSpec
  map:
    fields:
    - name: ai
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIBackend
    - name: aws
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AwsBackend
    - name: dynamicForwardProxy
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DynamicForwardProxyBackend
    - name: static
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StaticBackend
    - name: type
      type:
        scalar: string
      default: ""
    unions:
    - discriminator: type
      fields:
      - fieldName: ai
        discriminatorValue: AI
      - fieldName: aws
        discriminatorValue: Aws
      - fieldName: dynamicForwardProxy
        discriminatorValue: DynamicForwardProxy
      - fieldName: static
        discriminatorValue: Static
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BackendStatus
  map:
    fields:
    - name: conditions
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Condition
          elementRelationship: associative
          keys:
          - type
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BodyTransformation
  map:
    fields:
    - name: parseAs
      type:
        scalar: string
      default: ""
    - name: value
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BufferSettings
  map:
    fields:
    - name: allowPartialMessage
      type:
        scalar: boolean
    - name: maxRequestBytes
      type:
        scalar: numeric
      default: 0
    - name: packAsBytes
      type:
        scalar: boolean
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CELFilter
  map:
    fields:
    - name: match
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CommonHttpProtocolOptions
  map:
    fields:
    - name: headersWithUnderscoresAction
      type:
        scalar: string
    - name: idleTimeout
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
    - name: maxHeadersCount
      type:
        scalar: numeric
    - name: maxRequestsPerConnection
      type:
        scalar: numeric
    - name: maxStreamDuration
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CorsPolicy
  map:
    fields:
    - name: allowCredentials
      type:
        scalar: boolean
    - name: allowHeaders
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: allowMethods
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: allowOrigins
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: exposeHeaders
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: maxAge
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CustomLabel
  map:
    fields:
    - name: keyDelimiter
      type:
        scalar: string
    - name: metadataKey
      type:
        scalar: string
      default: ""
    - name: metadataNamespace
      type:
        scalar: string
    - name: name
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CustomResponse
  map:
    fields:
    - name: message
      type:
        scalar: string
    - name: statusCode
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DirectResponse
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DirectResponseSpec
      default: {}
    - name: status
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DirectResponseStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DirectResponseSpec
  map:
    fields:
    - name: body
      type:
        scalar: string
    - name: status
      type:
        scalar: numeric
      default: 0
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DirectResponseStatus
  map:
    elementType:
      scalar: untyped
      list:
        elementType:
          namedType: __untyped_atomic_
        elementRelationship: atomic
      map:
        elementType:
          namedType: __untyped_deduced_
        elementRelationship: separable
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DurationFilter
  map:
    fields:
    - name: op
      type:
        scalar: string
    - name: value
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DynamicForwardProxyBackend
  map:
    fields:
    - name: enableTls
      type:
        scalar: boolean
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.EnvoyBootstrap
  map:
    fields:
    - name: componentLogLevels
      type:
        map:
          elementType:
            scalar: string
    - name: logLevel
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.EnvoyContainer
  map:
    fields:
    - name: bootstrap
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.EnvoyBootstrap
    - name: image
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Image
    - name: resources
      type:
        namedType: io.k8s.api.core.v1.ResourceRequirements
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.SecurityContext
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtAuthPolicy
  map:
    fields:
    - name: contextExtensions
      type:
        map:
          elementType:
            scalar: string
    - name: enablement
      type:
        scalar: string
    - name: extensionRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
    - name: withRequestBody
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BufferSettings
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtAuthProvider
  map:
    fields:
    - name: grpcService
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtGrpcService
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtGrpcService
  map:
    fields:
    - name: authority
      type:
        scalar: string
    - name: backendRef
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1.BackendRef
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtProcPolicy
  map:
    fields:
    - name: extensionRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
    - name: processingMode
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ProcessingMode
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtProcProvider
  map:
    fields:
    - name: grpcService
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtGrpcService
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FieldDefault
  map:
    fields:
    - name: field
      type:
        scalar: string
      default: ""
    - name: override
      type:
        scalar: boolean
    - name: value
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FileSink
  map:
    fields:
    - name: jsonFormat
      type:
        namedType: __untyped_atomic_
    - name: path
      type:
        scalar: string
      default: ""
    - name: stringFormat
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.FilterType
  map:
    fields:
    - name: celFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CELFilter
    - name: durationFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.DurationFilter
    - name: grpcStatusFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GrpcStatusFilter
    - name: headerFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderFilter
    - name: notHealthCheckFilter
      type:
        scalar: boolean
    - name: responseFlagFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ResponseFlagFilter
    - name: statusCodeFilter
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StatusCodeFilter
    - name: traceableFilter
      type:
        scalar: boolean
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtension
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtensionSpec
      default: {}
    - name: status
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtensionStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtensionSpec
  map:
    fields:
    - name: extAuth
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtAuthProvider
    - name: extProc
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtProcProvider
    - name: rateLimit
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitProvider
    - name: type
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayExtensionStatus
  map:
    fields:
    - name: conditions
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Condition
          elementRelationship: associative
          keys:
          - type
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParameters
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParametersSpec
      default: {}
    - name: status
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParametersStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParametersSpec
  map:
    fields:
    - name: kube
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.KubernetesProxyConfig
    - name: selfManaged
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SelfManagedGateway
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GatewayParametersStatus
  map:
    elementType:
      scalar: untyped
      list:
        elementType:
          namedType: __untyped_atomic_
        elementRelationship: atomic
      map:
        elementType:
          namedType: __untyped_deduced_
        elementRelationship: separable
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GeminiConfig
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
      default: ""
    - name: authToken
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
      default: {}
    - name: model
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GracefulShutdownSpec
  map:
    fields:
    - name: enabled
      type:
        scalar: boolean
    - name: sleepTimeSeconds
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GrpcService
  map:
    fields:
    - name: additionalRequestHeadersToLog
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: additionalResponseHeadersToLog
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: additionalResponseTrailersToLog
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: backendRef
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1.BackendRef
    - name: logName
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GrpcStatusFilter
  map:
    fields:
    - name: exclude
      type:
        scalar: boolean
    - name: statuses
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HTTPListenerPolicy
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HTTPListenerPolicySpec
      default: {}
    - name: status
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HTTPListenerPolicySpec
  map:
    fields:
    - name: accessLog
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AccessLog
          elementRelationship: atomic
    - name: targetRefs
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetReference
          elementRelationship: atomic
    - name: targetSelectors
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetSelector
          elementRelationship: atomic
    - name: upgradeConfig
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.UpgradeConfig
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderFilter
  map:
    fields:
    - name: header
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1.HTTPHeaderMatch
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderTransformation
  map:
    fields:
    - name: name
      type:
        scalar: string
    - name: value
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Host
  map:
    fields:
    - name: host
      type:
        scalar: string
      default: ""
    - name: insecureSkipVerify
      type:
        scalar: boolean
    - name: port
      type:
        scalar: numeric
      default: 0
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Http1ProtocolOptions
  map:
    fields:
    - name: enableTrailers
      type:
        scalar: boolean
    - name: headerFormat
      type:
        scalar: string
    - name: overrideStreamErrorOnInvalidHttpMessage
      type:
        scalar: boolean
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Image
  map:
    fields:
    - name: digest
      type:
        scalar: string
    - name: pullPolicy
      type:
        scalar: string
    - name: registry
      type:
        scalar: string
    - name: repository
      type:
        scalar: string
    - name: tag
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.IstioContainer
  map:
    fields:
    - name: image
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Image
    - name: istioDiscoveryAddress
      type:
        scalar: string
    - name: istioMetaClusterId
      type:
        scalar: string
    - name: istioMetaMeshId
      type:
        scalar: string
    - name: logLevel
      type:
        scalar: string
    - name: resources
      type:
        namedType: io.k8s.api.core.v1.ResourceRequirements
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.SecurityContext
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.IstioIntegration
  map:
    fields:
    - name: customSidecars
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.Container
          elementRelationship: atomic
    - name: istioProxyContainer
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.IstioContainer
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.KubernetesProxyConfig
  map:
    fields:
    - name: agentGateway
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AgentGateway
    - name: aiExtension
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AiExtension
    - name: deployment
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ProxyDeployment
    - name: envoyContainer
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.EnvoyContainer
    - name: floatingUserId
      type:
        scalar: boolean
    - name: istio
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.IstioIntegration
    - name: podTemplate
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Pod
    - name: sdsContainer
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SdsContainer
    - name: service
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Service
    - name: serviceAccount
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ServiceAccount
    - name: stats
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StatsConfig
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LLMProvider
  map:
    fields:
    - name: authHeaderOverride
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AuthHeaderOverride
    - name: hostOverride
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Host
    - name: pathOverride
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PathOverride
    - name: provider
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SupportedLLMProvider
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetReference
  map:
    fields:
    - name: group
      type:
        scalar: string
      default: ""
    - name: kind
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetReferenceWithSectionName
  map:
    fields:
    - name: group
      type:
        scalar: string
      default: ""
    - name: kind
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
    - name: sectionName
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetSelector
  map:
    fields:
    - name: group
      type:
        scalar: string
      default: ""
    - name: kind
      type:
        scalar: string
      default: ""
    - name: matchLabels
      type:
        map:
          elementType:
            scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalRateLimitPolicy
  map:
    fields:
    - name: tokenBucket
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TokenBucket
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Message
  map:
    fields:
    - name: content
      type:
        scalar: string
      default: ""
    - name: role
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Moderation
  map:
    fields:
    - name: openAIModeration
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.OpenAIConfig
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.MultiPoolConfig
  map:
    fields:
    - name: priorities
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Priority
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.OpenAIConfig
  map:
    fields:
    - name: authToken
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
      default: {}
    - name: model
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PathOverride
  map:
    fields:
    - name: fullPath
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Pod
  map:
    fields:
    - name: affinity
      type:
        namedType: io.k8s.api.core.v1.Affinity
    - name: extraAnnotations
      type:
        map:
          elementType:
            scalar: string
    - name: extraLabels
      type:
        map:
          elementType:
            scalar: string
    - name: gracefulShutdown
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GracefulShutdownSpec
    - name: imagePullSecrets
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.LocalObjectReference
          elementRelationship: atomic
    - name: livenessProbe
      type:
        namedType: io.k8s.api.core.v1.Probe
    - name: nodeSelector
      type:
        map:
          elementType:
            scalar: string
    - name: readinessProbe
      type:
        namedType: io.k8s.api.core.v1.Probe
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.PodSecurityContext
    - name: terminationGracePeriodSeconds
      type:
        scalar: numeric
    - name: tolerations
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.Toleration
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Port
  map:
    fields:
    - name: nodePort
      type:
        scalar: numeric
    - name: port
      type:
        scalar: numeric
      default: 0
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Priority
  map:
    fields:
    - name: pool
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LLMProvider
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ProcessingMode
  map:
    fields:
    - name: requestBodyMode
      type:
        scalar: string
    - name: requestHeaderMode
      type:
        scalar: string
    - name: requestTrailerMode
      type:
        scalar: string
    - name: responseBodyMode
      type:
        scalar: string
    - name: responseHeaderMode
      type:
        scalar: string
    - name: responseTrailerMode
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PromptguardRequest
  map:
    fields:
    - name: customResponse
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CustomResponse
    - name: moderation
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Moderation
    - name: regex
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Regex
    - name: webhook
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Webhook
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.PromptguardResponse
  map:
    fields:
    - name: regex
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Regex
    - name: webhook
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Webhook
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ProxyDeployment
  map:
    fields:
    - name: replicas
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimit
  map:
    fields:
    - name: global
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitPolicy
    - name: local
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalRateLimitPolicy
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptor
  map:
    fields:
    - name: entries
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptorEntry
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptorEntry
  map:
    fields:
    - name: generic
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptorEntryGeneric
    - name: header
      type:
        scalar: string
    - name: type
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptorEntryGeneric
  map:
    fields:
    - name: key
      type:
        scalar: string
      default: ""
    - name: value
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitPolicy
  map:
    fields:
    - name: descriptors
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitDescriptor
          elementRelationship: atomic
    - name: extensionRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimitProvider
  map:
    fields:
    - name: domain
      type:
        scalar: string
      default: ""
    - name: failOpen
      type:
        scalar: boolean
    - name: grpcService
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtGrpcService
    - name: timeout
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Regex
  map:
    fields:
    - name: action
      type:
        scalar: string
    - name: builtins
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: matches
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RegexMatch
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RegexMatch
  map:
    fields:
    - name: name
      type:
        scalar: string
    - name: pattern
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ResponseFlagFilter
  map:
    fields:
    - name: flags
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLConfig
  map:
    fields:
    - name: allowRenegotiation
      type:
        scalar: boolean
    - name: alpnProtocols
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: oneWayTLS
      type:
        scalar: boolean
    - name: secretRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
    - name: sni
      type:
        scalar: string
    - name: sslFiles
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLFiles
    - name: sslParameters
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLParameters
    - name: verifySubjectAltName
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLFiles
  map:
    fields:
    - name: rootCA
      type:
        scalar: string
    - name: tlsCertificate
      type:
        scalar: string
    - name: tlsKey
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SSLParameters
  map:
    fields:
    - name: cipherSuites
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: ecdhCurves
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: tlsMaxVersion
      type:
        scalar: string
    - name: tlsMinVersion
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SdsBootstrap
  map:
    fields:
    - name: logLevel
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SdsContainer
  map:
    fields:
    - name: bootstrap
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SdsBootstrap
    - name: image
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Image
    - name: resources
      type:
        namedType: io.k8s.api.core.v1.ResourceRequirements
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.SecurityContext
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SelfManagedGateway
  map:
    elementType:
      scalar: untyped
      list:
        elementType:
          namedType: __untyped_atomic_
        elementRelationship: atomic
      map:
        elementType:
          namedType: __untyped_deduced_
        elementRelationship: separable
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Service
  map:
    fields:
    - name: clusterIP
      type:
        scalar: string
    - name: extraAnnotations
      type:
        map:
          elementType:
            scalar: string
    - name: extraLabels
      type:
        map:
          elementType:
            scalar: string
    - name: ports
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Port
          elementRelationship: atomic
    - name: type
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ServiceAccount
  map:
    fields:
    - name: extraAnnotations
      type:
        map:
          elementType:
            scalar: string
    - name: extraLabels
      type:
        map:
          elementType:
            scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
  map:
    fields:
    - name: inline
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
      default: ""
    - name: secretRef
      type:
        namedType: io.k8s.api.core.v1.LocalObjectReference
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StaticBackend
  map:
    fields:
    - name: hosts
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Host
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StatsConfig
  map:
    fields:
    - name: enableStatsRoute
      type:
        scalar: boolean
    - name: enabled
      type:
        scalar: boolean
    - name: routePrefixRewrite
      type:
        scalar: string
    - name: statsRoutePrefixRewrite
      type:
        scalar: string
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.StatusCodeFilter
  map:
    fields:
    - name: op
      type:
        scalar: string
    - name: value
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SupportedLLMProvider
  map:
    fields:
    - name: anthropic
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AnthropicConfig
    - name: azureopenai
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AzureOpenAIConfig
    - name: gemini
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.GeminiConfig
    - name: openai
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.OpenAIConfig
    - name: vertexai
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.VertexAIConfig
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TCPKeepalive
  map:
    fields:
    - name: keepAliveInterval
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
    - name: keepAliveProbes
      type:
        scalar: numeric
    - name: keepAliveTime
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TokenBucket
  map:
    fields:
    - name: fillInterval
      type:
        scalar: string
      default: ""
    - name: maxTokens
      type:
        scalar: numeric
      default: 0
    - name: tokensPerFill
      type:
        scalar: numeric
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TrafficPolicy
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: metadata
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
      default: {}
    - name: spec
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TrafficPolicySpec
      default: {}
    - name: status
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyStatus
      default: {}
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TrafficPolicySpec
  map:
    fields:
    - name: ai
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.AIPolicy
    - name: cors
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.CorsPolicy
    - name: extAuth
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtAuthPolicy
    - name: extProc
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.ExtProcPolicy
    - name: rateLimit
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.RateLimit
    - name: targetRefs
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetReferenceWithSectionName
          elementRelationship: atomic
    - name: targetSelectors
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.LocalPolicyTargetSelector
          elementRelationship: atomic
    - name: transformation
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TransformationPolicy
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Transform
  map:
    fields:
    - name: add
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderTransformation
          elementRelationship: associative
          keys:
          - name
    - name: body
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.BodyTransformation
    - name: remove
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: set
      type:
        list:
          elementType:
            namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.HeaderTransformation
          elementRelationship: associative
          keys:
          - name
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.TransformationPolicy
  map:
    fields:
    - name: request
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Transform
    - name: response
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Transform
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.UpgradeConfig
  map:
    fields:
    - name: enabledUpgrades
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.VertexAIConfig
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
      default: ""
    - name: authToken
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.SingleAuthToken
      default: {}
    - name: location
      type:
        scalar: string
      default: ""
    - name: model
      type:
        scalar: string
      default: ""
    - name: modelPath
      type:
        scalar: string
    - name: projectId
      type:
        scalar: string
      default: ""
    - name: publisher
      type:
        scalar: string
      default: ""
- name: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Webhook
  map:
    fields:
    - name: forwardHeaders
      type:
        list:
          elementType:
            namedType: io.k8s.sigs.gateway-api.apis.v1.HTTPHeaderMatch
          elementRelationship: atomic
    - name: host
      type:
        namedType: com.github.kgateway-dev.kgateway.v2.api.v1alpha1.Host
      default: {}
- name: io.k8s.api.core.v1.Affinity
  map:
    fields:
    - name: nodeAffinity
      type:
        namedType: io.k8s.api.core.v1.NodeAffinity
    - name: podAffinity
      type:
        namedType: io.k8s.api.core.v1.PodAffinity
    - name: podAntiAffinity
      type:
        namedType: io.k8s.api.core.v1.PodAntiAffinity
- name: io.k8s.api.core.v1.AppArmorProfile
  map:
    fields:
    - name: localhostProfile
      type:
        scalar: string
    - name: type
      type:
        scalar: string
      default: ""
    unions:
    - discriminator: type
      fields:
      - fieldName: localhostProfile
        discriminatorValue: LocalhostProfile
- name: io.k8s.api.core.v1.Capabilities
  map:
    fields:
    - name: add
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: drop
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: io.k8s.api.core.v1.ConfigMapEnvSource
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: optional
      type:
        scalar: boolean
- name: io.k8s.api.core.v1.ConfigMapKeySelector
  map:
    fields:
    - name: key
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
    - name: optional
      type:
        scalar: boolean
    elementRelationship: atomic
- name: io.k8s.api.core.v1.Container
  map:
    fields:
    - name: args
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: command
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: env
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.EnvVar
          elementRelationship: associative
          keys:
          - name
    - name: envFrom
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.EnvFromSource
          elementRelationship: atomic
    - name: image
      type:
        scalar: string
    - name: imagePullPolicy
      type:
        scalar: string
    - name: lifecycle
      type:
        namedType: io.k8s.api.core.v1.Lifecycle
    - name: livenessProbe
      type:
        namedType: io.k8s.api.core.v1.Probe
    - name: name
      type:
        scalar: string
      default: ""
    - name: ports
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.ContainerPort
          elementRelationship: associative
          keys:
          - containerPort
          - protocol
    - name: readinessProbe
      type:
        namedType: io.k8s.api.core.v1.Probe
    - name: resizePolicy
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.ContainerResizePolicy
          elementRelationship: atomic
    - name: resources
      type:
        namedType: io.k8s.api.core.v1.ResourceRequirements
      default: {}
    - name: restartPolicy
      type:
        scalar: string
    - name: securityContext
      type:
        namedType: io.k8s.api.core.v1.SecurityContext
    - name: startupProbe
      type:
        namedType: io.k8s.api.core.v1.Probe
    - name: stdin
      type:
        scalar: boolean
    - name: stdinOnce
      type:
        scalar: boolean
    - name: terminationMessagePath
      type:
        scalar: string
    - name: terminationMessagePolicy
      type:
        scalar: string
    - name: tty
      type:
        scalar: boolean
    - name: volumeDevices
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.VolumeDevice
          elementRelationship: associative
          keys:
          - devicePath
    - name: volumeMounts
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.VolumeMount
          elementRelationship: associative
          keys:
          - mountPath
    - name: workingDir
      type:
        scalar: string
- name: io.k8s.api.core.v1.ContainerPort
  map:
    fields:
    - name: containerPort
      type:
        scalar: numeric
      default: 0
    - name: hostIP
      type:
        scalar: string
    - name: hostPort
      type:
        scalar: numeric
    - name: name
      type:
        scalar: string
    - name: protocol
      type:
        scalar: string
      default: TCP
- name: io.k8s.api.core.v1.ContainerResizePolicy
  map:
    fields:
    - name: resourceName
      type:
        scalar: string
      default: ""
    - name: restartPolicy
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.EnvFromSource
  map:
    fields:
    - name: configMapRef
      type:
        namedType: io.k8s.api.core.v1.ConfigMapEnvSource
    - name: prefix
      type:
        scalar: string
    - name: secretRef
      type:
        namedType: io.k8s.api.core.v1.SecretEnvSource
- name: io.k8s.api.core.v1.EnvVar
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: value
      type:
        scalar: string
    - name: valueFrom
      type:
        namedType: io.k8s.api.core.v1.EnvVarSource
- name: io.k8s.api.core.v1.EnvVarSource
  map:
    fields:
    - name: configMapKeyRef
      type:
        namedType: io.k8s.api.core.v1.ConfigMapKeySelector
    - name: fieldRef
      type:
        namedType: io.k8s.api.core.v1.ObjectFieldSelector
    - name: resourceFieldRef
      type:
        namedType: io.k8s.api.core.v1.ResourceFieldSelector
    - name: secretKeyRef
      type:
        namedType: io.k8s.api.core.v1.SecretKeySelector
- name: io.k8s.api.core.v1.ExecAction
  map:
    fields:
    - name: command
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: io.k8s.api.core.v1.GRPCAction
  map:
    fields:
    - name: port
      type:
        scalar: numeric
      default: 0
    - name: service
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.HTTPGetAction
  map:
    fields:
    - name: host
      type:
        scalar: string
    - name: httpHeaders
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.HTTPHeader
          elementRelationship: atomic
    - name: path
      type:
        scalar: string
    - name: port
      type:
        namedType: io.k8s.apimachinery.pkg.util.intstr.IntOrString
    - name: scheme
      type:
        scalar: string
- name: io.k8s.api.core.v1.HTTPHeader
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: value
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.Lifecycle
  map:
    fields:
    - name: postStart
      type:
        namedType: io.k8s.api.core.v1.LifecycleHandler
    - name: preStop
      type:
        namedType: io.k8s.api.core.v1.LifecycleHandler
- name: io.k8s.api.core.v1.LifecycleHandler
  map:
    fields:
    - name: exec
      type:
        namedType: io.k8s.api.core.v1.ExecAction
    - name: httpGet
      type:
        namedType: io.k8s.api.core.v1.HTTPGetAction
    - name: sleep
      type:
        namedType: io.k8s.api.core.v1.SleepAction
    - name: tcpSocket
      type:
        namedType: io.k8s.api.core.v1.TCPSocketAction
- name: io.k8s.api.core.v1.LocalObjectReference
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    elementRelationship: atomic
- name: io.k8s.api.core.v1.NodeAffinity
  map:
    fields:
    - name: preferredDuringSchedulingIgnoredDuringExecution
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.PreferredSchedulingTerm
          elementRelationship: atomic
    - name: requiredDuringSchedulingIgnoredDuringExecution
      type:
        namedType: io.k8s.api.core.v1.NodeSelector
- name: io.k8s.api.core.v1.NodeSelector
  map:
    fields:
    - name: nodeSelectorTerms
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.NodeSelectorTerm
          elementRelationship: atomic
    elementRelationship: atomic
- name: io.k8s.api.core.v1.NodeSelectorRequirement
  map:
    fields:
    - name: key
      type:
        scalar: string
      default: ""
    - name: operator
      type:
        scalar: string
      default: ""
    - name: values
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: io.k8s.api.core.v1.NodeSelectorTerm
  map:
    fields:
    - name: matchExpressions
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.NodeSelectorRequirement
          elementRelationship: atomic
    - name: matchFields
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.NodeSelectorRequirement
          elementRelationship: atomic
    elementRelationship: atomic
- name: io.k8s.api.core.v1.ObjectFieldSelector
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: fieldPath
      type:
        scalar: string
      default: ""
    elementRelationship: atomic
- name: io.k8s.api.core.v1.PodAffinity
  map:
    fields:
    - name: preferredDuringSchedulingIgnoredDuringExecution
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.WeightedPodAffinityTerm
          elementRelationship: atomic
    - name: requiredDuringSchedulingIgnoredDuringExecution
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.PodAffinityTerm
          elementRelationship: atomic
- name: io.k8s.api.core.v1.PodAffinityTerm
  map:
    fields:
    - name: labelSelector
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector
    - name: matchLabelKeys
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: mismatchLabelKeys
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: namespaceSelector
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector
    - name: namespaces
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
    - name: topologyKey
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.PodAntiAffinity
  map:
    fields:
    - name: preferredDuringSchedulingIgnoredDuringExecution
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.WeightedPodAffinityTerm
          elementRelationship: atomic
    - name: requiredDuringSchedulingIgnoredDuringExecution
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.PodAffinityTerm
          elementRelationship: atomic
- name: io.k8s.api.core.v1.PodSecurityContext
  map:
    fields:
    - name: appArmorProfile
      type:
        namedType: io.k8s.api.core.v1.AppArmorProfile
    - name: fsGroup
      type:
        scalar: numeric
    - name: fsGroupChangePolicy
      type:
        scalar: string
    - name: runAsGroup
      type:
        scalar: numeric
    - name: runAsNonRoot
      type:
        scalar: boolean
    - name: runAsUser
      type:
        scalar: numeric
    - name: seLinuxChangePolicy
      type:
        scalar: string
    - name: seLinuxOptions
      type:
        namedType: io.k8s.api.core.v1.SELinuxOptions
    - name: seccompProfile
      type:
        namedType: io.k8s.api.core.v1.SeccompProfile
    - name: supplementalGroups
      type:
        list:
          elementType:
            scalar: numeric
          elementRelationship: atomic
    - name: supplementalGroupsPolicy
      type:
        scalar: string
    - name: sysctls
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.Sysctl
          elementRelationship: atomic
    - name: windowsOptions
      type:
        namedType: io.k8s.api.core.v1.WindowsSecurityContextOptions
- name: io.k8s.api.core.v1.PreferredSchedulingTerm
  map:
    fields:
    - name: preference
      type:
        namedType: io.k8s.api.core.v1.NodeSelectorTerm
      default: {}
    - name: weight
      type:
        scalar: numeric
      default: 0
- name: io.k8s.api.core.v1.Probe
  map:
    fields:
    - name: exec
      type:
        namedType: io.k8s.api.core.v1.ExecAction
    - name: failureThreshold
      type:
        scalar: numeric
    - name: grpc
      type:
        namedType: io.k8s.api.core.v1.GRPCAction
    - name: httpGet
      type:
        namedType: io.k8s.api.core.v1.HTTPGetAction
    - name: initialDelaySeconds
      type:
        scalar: numeric
    - name: periodSeconds
      type:
        scalar: numeric
    - name: successThreshold
      type:
        scalar: numeric
    - name: tcpSocket
      type:
        namedType: io.k8s.api.core.v1.TCPSocketAction
    - name: terminationGracePeriodSeconds
      type:
        scalar: numeric
    - name: timeoutSeconds
      type:
        scalar: numeric
- name: io.k8s.api.core.v1.ResourceClaim
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: request
      type:
        scalar: string
- name: io.k8s.api.core.v1.ResourceFieldSelector
  map:
    fields:
    - name: containerName
      type:
        scalar: string
    - name: divisor
      type:
        namedType: io.k8s.apimachinery.pkg.api.resource.Quantity
    - name: resource
      type:
        scalar: string
      default: ""
    elementRelationship: atomic
- name: io.k8s.api.core.v1.ResourceRequirements
  map:
    fields:
    - name: claims
      type:
        list:
          elementType:
            namedType: io.k8s.api.core.v1.ResourceClaim
          elementRelationship: associative
          keys:
          - name
    - name: limits
      type:
        map:
          elementType:
            namedType: io.k8s.apimachinery.pkg.api.resource.Quantity
    - name: requests
      type:
        map:
          elementType:
            namedType: io.k8s.apimachinery.pkg.api.resource.Quantity
- name: io.k8s.api.core.v1.SELinuxOptions
  map:
    fields:
    - name: level
      type:
        scalar: string
    - name: role
      type:
        scalar: string
    - name: type
      type:
        scalar: string
    - name: user
      type:
        scalar: string
- name: io.k8s.api.core.v1.SeccompProfile
  map:
    fields:
    - name: localhostProfile
      type:
        scalar: string
    - name: type
      type:
        scalar: string
      default: ""
    unions:
    - discriminator: type
      fields:
      - fieldName: localhostProfile
        discriminatorValue: LocalhostProfile
- name: io.k8s.api.core.v1.SecretEnvSource
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: optional
      type:
        scalar: boolean
- name: io.k8s.api.core.v1.SecretKeySelector
  map:
    fields:
    - name: key
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
    - name: optional
      type:
        scalar: boolean
    elementRelationship: atomic
- name: io.k8s.api.core.v1.SecurityContext
  map:
    fields:
    - name: allowPrivilegeEscalation
      type:
        scalar: boolean
    - name: appArmorProfile
      type:
        namedType: io.k8s.api.core.v1.AppArmorProfile
    - name: capabilities
      type:
        namedType: io.k8s.api.core.v1.Capabilities
    - name: privileged
      type:
        scalar: boolean
    - name: procMount
      type:
        scalar: string
    - name: readOnlyRootFilesystem
      type:
        scalar: boolean
    - name: runAsGroup
      type:
        scalar: numeric
    - name: runAsNonRoot
      type:
        scalar: boolean
    - name: runAsUser
      type:
        scalar: numeric
    - name: seLinuxOptions
      type:
        namedType: io.k8s.api.core.v1.SELinuxOptions
    - name: seccompProfile
      type:
        namedType: io.k8s.api.core.v1.SeccompProfile
    - name: windowsOptions
      type:
        namedType: io.k8s.api.core.v1.WindowsSecurityContextOptions
- name: io.k8s.api.core.v1.SleepAction
  map:
    fields:
    - name: seconds
      type:
        scalar: numeric
      default: 0
- name: io.k8s.api.core.v1.Sysctl
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: value
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.TCPSocketAction
  map:
    fields:
    - name: host
      type:
        scalar: string
    - name: port
      type:
        namedType: io.k8s.apimachinery.pkg.util.intstr.IntOrString
- name: io.k8s.api.core.v1.Toleration
  map:
    fields:
    - name: effect
      type:
        scalar: string
    - name: key
      type:
        scalar: string
    - name: operator
      type:
        scalar: string
    - name: tolerationSeconds
      type:
        scalar: numeric
    - name: value
      type:
        scalar: string
- name: io.k8s.api.core.v1.VolumeDevice
  map:
    fields:
    - name: devicePath
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
- name: io.k8s.api.core.v1.VolumeMount
  map:
    fields:
    - name: mountPath
      type:
        scalar: string
      default: ""
    - name: mountPropagation
      type:
        scalar: string
    - name: name
      type:
        scalar: string
      default: ""
    - name: readOnly
      type:
        scalar: boolean
    - name: recursiveReadOnly
      type:
        scalar: string
    - name: subPath
      type:
        scalar: string
    - name: subPathExpr
      type:
        scalar: string
- name: io.k8s.api.core.v1.WeightedPodAffinityTerm
  map:
    fields:
    - name: podAffinityTerm
      type:
        namedType: io.k8s.api.core.v1.PodAffinityTerm
      default: {}
    - name: weight
      type:
        scalar: numeric
      default: 0
- name: io.k8s.api.core.v1.WindowsSecurityContextOptions
  map:
    fields:
    - name: gmsaCredentialSpec
      type:
        scalar: string
    - name: gmsaCredentialSpecName
      type:
        scalar: string
    - name: hostProcess
      type:
        scalar: boolean
    - name: runAsUserName
      type:
        scalar: string
- name: io.k8s.apimachinery.pkg.api.resource.Quantity
  scalar: untyped
- name: io.k8s.apimachinery.pkg.apis.meta.v1.Condition
  map:
    fields:
    - name: lastTransitionTime
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Time
    - name: message
      type:
        scalar: string
      default: ""
    - name: observedGeneration
      type:
        scalar: numeric
    - name: reason
      type:
        scalar: string
      default: ""
    - name: status
      type:
        scalar: string
      default: ""
    - name: type
      type:
        scalar: string
      default: ""
- name: io.k8s.apimachinery.pkg.apis.meta.v1.Duration
  scalar: string
- name: io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1
  map:
    elementType:
      scalar: untyped
      list:
        elementType:
          namedType: __untyped_atomic_
        elementRelationship: atomic
      map:
        elementType:
          namedType: __untyped_deduced_
        elementRelationship: separable
- name: io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector
  map:
    fields:
    - name: matchExpressions
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelectorRequirement
          elementRelationship: atomic
    - name: matchLabels
      type:
        map:
          elementType:
            scalar: string
    elementRelationship: atomic
- name: io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelectorRequirement
  map:
    fields:
    - name: key
      type:
        scalar: string
      default: ""
    - name: operator
      type:
        scalar: string
      default: ""
    - name: values
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: atomic
- name: io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
    - name: fieldsType
      type:
        scalar: string
    - name: fieldsV1
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1
    - name: manager
      type:
        scalar: string
    - name: operation
      type:
        scalar: string
    - name: subresource
      type:
        scalar: string
    - name: time
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Time
- name: io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta
  map:
    fields:
    - name: annotations
      type:
        map:
          elementType:
            scalar: string
    - name: creationTimestamp
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Time
    - name: deletionGracePeriodSeconds
      type:
        scalar: numeric
    - name: deletionTimestamp
      type:
        namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Time
    - name: finalizers
      type:
        list:
          elementType:
            scalar: string
          elementRelationship: associative
    - name: generateName
      type:
        scalar: string
    - name: generation
      type:
        scalar: numeric
    - name: labels
      type:
        map:
          elementType:
            scalar: string
    - name: managedFields
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry
          elementRelationship: atomic
    - name: name
      type:
        scalar: string
    - name: namespace
      type:
        scalar: string
    - name: ownerReferences
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference
          elementRelationship: associative
          keys:
          - uid
    - name: resourceVersion
      type:
        scalar: string
    - name: selfLink
      type:
        scalar: string
    - name: uid
      type:
        scalar: string
- name: io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference
  map:
    fields:
    - name: apiVersion
      type:
        scalar: string
      default: ""
    - name: blockOwnerDeletion
      type:
        scalar: boolean
    - name: controller
      type:
        scalar: boolean
    - name: kind
      type:
        scalar: string
      default: ""
    - name: name
      type:
        scalar: string
      default: ""
    - name: uid
      type:
        scalar: string
      default: ""
    elementRelationship: atomic
- name: io.k8s.apimachinery.pkg.apis.meta.v1.Time
  scalar: untyped
- name: io.k8s.apimachinery.pkg.runtime.RawExtension
  map:
    elementType:
      scalar: untyped
      list:
        elementType:
          namedType: __untyped_atomic_
        elementRelationship: atomic
      map:
        elementType:
          namedType: __untyped_deduced_
        elementRelationship: separable
- name: io.k8s.apimachinery.pkg.util.intstr.IntOrString
  scalar: untyped
- name: io.k8s.sigs.gateway-api.apis.v1.BackendRef
  map:
    fields:
    - name: group
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: name
      type:
        scalar: string
      default: ""
    - name: namespace
      type:
        scalar: string
    - name: port
      type:
        scalar: numeric
    - name: weight
      type:
        scalar: numeric
- name: io.k8s.sigs.gateway-api.apis.v1.HTTPHeaderMatch
  map:
    fields:
    - name: name
      type:
        scalar: string
      default: ""
    - name: type
      type:
        scalar: string
    - name: value
      type:
        scalar: string
      default: ""
- name: io.k8s.sigs.gateway-api.apis.v1.ParentReference
  map:
    fields:
    - name: group
      type:
        scalar: string
    - name: kind
      type:
        scalar: string
    - name: name
      type:
        scalar: string
      default: ""
    - name: namespace
      type:
        scalar: string
    - name: port
      type:
        scalar: numeric
    - name: sectionName
      type:
        scalar: string
- name: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyAncestorStatus
  map:
    fields:
    - name: ancestorRef
      type:
        namedType: io.k8s.sigs.gateway-api.apis.v1.ParentReference
      default: {}
    - name: conditions
      type:
        list:
          elementType:
            namedType: io.k8s.apimachinery.pkg.apis.meta.v1.Condition
          elementRelationship: associative
          keys:
          - type
    - name: controllerName
      type:
        scalar: string
      default: ""
- name: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyStatus
  map:
    fields:
    - name: ancestors
      type:
        list:
          elementType:
            namedType: io.k8s.sigs.gateway-api.apis.v1alpha2.PolicyAncestorStatus
          elementRelationship: atomic
- name: __untyped_atomic_
  scalar: untyped
  list:
    elementType:
      namedType: __untyped_atomic_
    elementRelationship: atomic
  map:
    elementType:
      namedType: __untyped_atomic_
    elementRelationship: atomic
- name: __untyped_deduced_
  scalar: untyped
  list:
    elementType:
      namedType: __untyped_atomic_
    elementRelationship: atomic
  map:
    elementType:
      namedType: __untyped_deduced_
    elementRelationship: separable
`)
