---
apiVersion: v1
kind: Namespace
metadata:
  name: nginx
---
apiVersion: v1
kind: Service
metadata:
  name: nginx
  namespace: nginx
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
      name: http
    - protocol: TCP
      port: 8443
      targetPort: https-web-svc
      name: https
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-conf
  namespace: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes  1;
    events {
      worker_connections  10240;
    }
    http {
      server {
          listen              80;
          listen              443 ssl;
          server_name         localhost;
          ssl_certificate     /etc/nginx/localhost.crt;
          ssl_certificate_key /etc/nginx/localhost.key;

          location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
        }
      }
    }
  # localhost cert and key generated with following command from https://letsencrypt.org/docs/certificates-for-localhost/
  # openssl req -x509 -out localhost.crt -keyout localhost.key  -newkey rsa:2048 -nodes -sha256 -subj '/CN=localhost' -extensions EXT -config <(printf "[dn]\nCN=localhost\n[req]\ndistinguished_name = dn\n[EXT]\nsubjectAltName=DNS:localhost\nkeyUsage=digitalSignature\nextendedKeyUsage=serverAuth")
  localhost.crt: |
    -----BEGIN CERTIFICATE-----
    MIIDDzCCAfegAwIBAgIUMrxrG4aI7TShlLeuu4tmIsTIO1gwDQYJKoZIhvcNAQEL
    BQAwFDESMBAGA1UEAwwJbG9jYWxob3N0MB4XDTI0MDcwMjIxNDYzNVoXDTI0MDgw
    MTIxNDYzNVowFDESMBAGA1UEAwwJbG9jYWxob3N0MIIBIjANBgkqhkiG9w0BAQEF
    AAOCAQ8AMIIBCgKCAQEAw6lxj3IX6kBNKWF0LEQiONJN81vbKNDEVpE+w5zwaA1K
    zAMSfKxkhdQMPtM+MS64CPkDUZFxdYbUgKygl23uWcuIPWHnD7aqICm+ujMLUMzw
    RFXablUCZiO9sFfZegkdLwvecmtnvNjVL5s8jk3HjV8Jetu/tE17HMvP4cMdfs/r
    zdYRxoI2tWyYDWUW1XfD6eTxDykWwfLMdJ6UX0ksZSlQ098OheMMA6E+cxH0JMoe
    +PLyD4nuAYW8c6tOFTXJjqHUaJzSRlYFg3OG0WRWKcjP9ufeLsPWjWza5M6WSGEj
    hiPP2bSxMCfkY3DFSO3K71MrYf5xsP4L70YmD2oUowIDAQABo1kwVzAUBgNVHREE
    DTALgglsb2NhbGhvc3QwCwYDVR0PBAQDAgeAMBMGA1UdJQQMMAoGCCsGAQUFBwMB
    MB0GA1UdDgQWBBSC36i8yBxdER9rU3KJvR/Dtop9XTANBgkqhkiG9w0BAQsFAAOC
    AQEAXG1LQfCVJZ3R2rXHZirUHkSgXCPMglUv9dN25XMvGiOwVGX8g9QUv+WuMeoS
    VK98rLnej7EOLZLb+02lXKqAT8G6eDqXlZONfFCTnS6BWc0+5o2fvnniuJhzxGq0
    qolf2q4P4JNJk7TRlulaLdIxSOMyJukRne4kRcbkz3SaVE+eGAm6IURSEE1x1AXU
    BaCZpm5MWgiJtOJulM7/9Nw8SpTir3nKNTcI3Q0M2XGvhWylN9N17AkANrBrNBme
    LDyhBvUlZrbnOxfblBxzB8jocGxCLDLLtNNlfuEPquy8J263LVIh+Totibxhg4l/
    MaeHQu7bsnjxU6pWF3x/QsZYzQ==
    -----END CERTIFICATE-----
  localhost.key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  namespace: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  terminationGracePeriodSeconds: 0
  containers:
  - name: nginx
    image: nginx:stable
    ports:
      - containerPort: 80
        name: http-web-svc
      - containerPort: 443
        name: https-web-svc
    volumeMounts:
    - name: nginx-conf
      mountPath: /etc/nginx/
      readOnly: true
  volumes:
  - name: nginx-conf
    configMap:
      name: nginx-conf
      items:
      - key: nginx.conf
        path: nginx.conf
      - key: localhost.crt
        path: localhost.crt
      - key: localhost.key
        path: localhost.key
