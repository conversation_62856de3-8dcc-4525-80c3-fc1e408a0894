---
apiVersion: v1
kind: Namespace
metadata:
  name: tcp-echo
---
apiVersion: v1
kind: Service
metadata:
  name: tcp-echo
  namespace: tcp-echo
spec:
  selector:
    app.kubernetes.io/name: tcp-echo
  ports:
    - protocol: TCP
      port: 1025
---
apiVersion: v1
kind: Pod
metadata:
  name: tcp-echo
  namespace: tcp-echo
  labels:
    app.kubernetes.io/name: tcp-echo
spec:
  terminationGracePeriodSeconds: 0
  containers:
  - name: tcp-echo
    image: soloio/tcp-echo:latest
    ports:
      - containerPort: 1025
    resources:
      requests:
        cpu: "100m"
      limits:
        cpu: "200m"

