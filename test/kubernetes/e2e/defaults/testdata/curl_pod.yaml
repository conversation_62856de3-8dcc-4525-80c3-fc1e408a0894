---
apiVersion: v1
kind: Namespace
metadata:
  name: curl
---
apiVersion: v1
kind: Pod
metadata:
  name: curl
  namespace: curl
  labels:
    app: curl
    version: v1
    app.kubernetes.io/name: curl
spec:
  terminationGracePeriodSeconds: 0
  containers:
    - name: curl
      image: curlimages/curl:7.83.1
      imagePullPolicy: IfNotPresent
      command:
        - "tail"
        - "-f"
        - "/dev/null"
      resources:
        requests:
          cpu: "100m"
        limits:
          cpu: "200m"