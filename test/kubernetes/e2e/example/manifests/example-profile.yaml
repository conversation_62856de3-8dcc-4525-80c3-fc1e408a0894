global:
  image:
    pullPolicy: IfNotPresent

settings:
  create: true

gateway:
  # This is an example profile, so we disable as many components as possible
  enabled: true

kubeGateway:
  # This is an example profile, so we disable as many components as possible
  enabled: false

gloo:
  # We set the profile log level to debug, so that a test can override it
  logLevel: debug

# This is an example profile, so we disable as many components as possible
discovery:
  enabled: false