apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: route-rl-policy
spec:
  rateLimit:
    local:
      tokenBucket:
        maxTokens: 1
        tokensPerFill: 1
        fillInterval: 30s
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: svc-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /path1
    - backendRefs:
        - name: simple-svc
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /path2
      filters:
          - type: ExtensionRef
            extensionRef:
              group: gateway.kgateway.dev
              kind: TrafficPolicy
              name: route-rl-policy