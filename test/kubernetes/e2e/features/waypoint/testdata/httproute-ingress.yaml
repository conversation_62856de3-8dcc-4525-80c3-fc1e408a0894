kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: gw
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: Same
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: svc-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: svc-a
          port: 8080
---