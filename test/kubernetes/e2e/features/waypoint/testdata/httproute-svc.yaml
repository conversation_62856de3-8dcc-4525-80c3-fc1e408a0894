---
# HTT<PERSON>oute parented to the Service only affects one chain
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: hr-traversed-waypoint-header
spec:
  parentRefs:
  - name: svc-a
    group: ""
    kind: Service
  rules:
  - backendRefs:
    - name: svc-a
      port: 8080
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "traversed-waypoint"
          value: "true"
