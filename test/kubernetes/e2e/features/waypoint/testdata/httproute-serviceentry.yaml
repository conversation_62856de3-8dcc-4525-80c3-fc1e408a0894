---
# HTT<PERSON>oute parented to the Service via a hostname only affects one chain
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: hr-traversed-waypoint-header
spec:
  parentRefs:
  - name: se-a
    group: "networking.istio.io"
    kind: ServiceEntry
  rules:
  - backendRefs:
    - name: se-a
      group: "networking.istio.io"
      kind: ServiceEntry
      port: 8080
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "traversed-waypoint"
          value: "true"
