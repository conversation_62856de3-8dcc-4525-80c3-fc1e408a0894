---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: lambda-route
  namespace: lambda-test
spec:
  parentRefs:
    - name: lambda-gateway
  hostnames:
    - "www.example.com"
  rules:
    - matches:
      - path:
          type: Exact
          value: /lambda
      backendRefs:
        - name: lambda-backend
          kind: Backend
          group: gateway.kgateway.dev
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  name: lambda-backend
  namespace: lambda-test
spec:
  type: AWS
  aws:
    accountId: "************"
    auth:
      type: Secret
      secretRef:
        name: aws-creds
    lambda:
      functionName: hello-function
      invocationMode: Async
      endpointURL: "http://**********:31566"
