---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: backend-filter-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "www.example.com"
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /with-extproc
      backendRefs:
        - name: backend
          port: 3000
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: backend-test
    - matches:
        - path:
            type: PathPrefix
            value: /without-extproc
      backendRefs:
        - name: backend
          port: 3000
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: backend-test
spec:
  extProc:
    extensionRef:
      name: ext-proc-extension
