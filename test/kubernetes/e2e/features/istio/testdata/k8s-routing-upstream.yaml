kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: gw
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: All
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin-route
  namespace: httpbin
spec:
  parentRefs:
    - name: gw
      namespace: default
  hostnames:
    - "httpbin"
  rules:
    - backendRefs:
        - name: httpbin-upstream
          namespace: httpbin
          port: 8000
          kind: Upstream
          group: gloo.solo.io
---
apiVersion: gloo.solo.io/v1
kind: Upstream
metadata:
  name: httpbin-upstream
  namespace: httpbin
spec:
  disableIstioAutoMtls: true
  kube:
    selector:
      app: httpbin
    serviceName: httpbin
    serviceNamespace: httpbin
    servicePort: 8000
