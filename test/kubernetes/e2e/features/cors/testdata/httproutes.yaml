apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: svc-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /path1
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: svc-route-2
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /path2