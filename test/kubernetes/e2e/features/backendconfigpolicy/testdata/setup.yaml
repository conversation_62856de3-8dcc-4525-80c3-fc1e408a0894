kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: gw
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: Same
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: example-svc
          port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
---
kind: BackendConfigPolicy
apiVersion: gateway.kgateway.dev/v1alpha1
metadata:
  name: example-policy
spec:
  targetRefs:
    - name: example-svc
      group: ""
      kind: Service
  connectTimeout: 5s
  perConnectionBufferLimitBytes: 1024
  tcpKeepalive:
    keepAliveProbes: 3
    keepAliveTime: 30s
    keepAliveInterval: 5s
  commonHttpProtocolOptions:
    idleTimeout: 10s
    maxHeadersCount: 15
    maxStreamDuration: 30s
    maxRequestsPerConnection: 100
    headersWithUnderscoresAction: RejectRequest
  http1ProtocolOptions:
    enableTrailers: true
    overrideStreamErrorOnInvalidHttpMessage: true
    headerFormat: PreserveCaseHeaderKeyFormat
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  containers:
    - name: nginx
      image: nginx:stable
      ports:
        - containerPort: 80
          name: http-web-svc
          