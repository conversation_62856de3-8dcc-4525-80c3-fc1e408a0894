---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin
  namespace: httpbin
  labels:
    app: httpbin
spec:
  hostnames:
  - "www.example.com"
  parentRefs:
  - name: gw
    namespace: default
  rules:
  - filters:
    - type: ExtensionRef
      extensionRef:
        name: test-1
        group: gateway.kgateway.dev
        kind: DirectResponse
    - type: ExtensionRef
      extensionRef:
        name: test-2
        group: gateway.kgateway.dev
        kind: DirectResponse
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: DirectResponse
metadata:
  name: test-1
  namespace: httpbin
spec:
  status: 301
  body: "Test 1 body"
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: DirectResponse
metadata:
  name: test-2
  namespace: httpbin
spec:
  status: 302
  body: "Test 2 body"
