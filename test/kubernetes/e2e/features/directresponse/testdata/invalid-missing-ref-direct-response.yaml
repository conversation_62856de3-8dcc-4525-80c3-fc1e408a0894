---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin
  namespace: httpbin
  labels:
    app: httpbin
spec:
  hostnames:
  - "www.example.com"
  parentRefs:
  - name: gw
    namespace: default
  rules:
  - matches:
    - path:
        type: Exact
        value: /non-existent
    filters:
    - type: ExtensionRef
      extensionRef:
        name: non-existent-ref
        group: gateway.kgateway.dev
        kind: DirectResponse
