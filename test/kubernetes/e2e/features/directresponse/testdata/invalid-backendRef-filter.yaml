---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin
  namespace: httpbin
  labels:
    app: httpbin
spec:
  hostnames:
  - "www.example.com"
  parentRefs:
  - name: gw
    namespace: default
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: httpbin
      port: 8000
      filters:
      - type: ExtensionRef
        extensionRef:
          name: robots-txt
          group: gateway.kgateway.dev
          kind: DirectResponse
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: DirectResponse
metadata:
  name: robots-txt
  namespace: httpbin
spec:
  status: 200
  body: "User-agent: *\nDisallow: /custom\n"
