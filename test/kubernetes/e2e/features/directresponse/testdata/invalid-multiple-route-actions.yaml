---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httpbin
  namespace: httpbin
  labels:
    app: httpbin
spec:
  hostnames:
  - "www.example.com"
  parentRefs:
  - name: gw
    namespace: default
  rules:
  - filters:
    - type: RequestRedirect
      requestRedirect:
        statusCode: 301
        port: 80
    - type: ExtensionRef
      extensionRef:
        name: test-1
        group: gateway.kgateway.dev
        kind: DirectResponse
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: DirectResponse
metadata:
  name: test-1
  namespace: httpbin
spec:
  status: 403
