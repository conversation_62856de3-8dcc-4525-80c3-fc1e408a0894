kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: gw
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: Same
---
apiVersion: gloo.solo.io/v1
kind: Upstream
metadata:
  name: example-upstream
spec:
  kube:
    serviceName: example-svc
    serviceNamespace: default
    servicePort: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
    - name: gw
  hostnames:
    - "example.com"
  rules:
    - backendRefs:
        - name: example-upstream
          port: 80
          kind: Upstream
          group: gloo.solo.io
