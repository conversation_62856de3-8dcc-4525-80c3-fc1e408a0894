
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  namespace: kgateway-test
  name: route-example-disableall
spec:
  parentRefs:
    - name: super-gateway
      namespace: kgateway-test
  hostnames:
    - "disableall.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  namespace: kgateway-test
  name: insecure-route-policy2
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: route-example-disableall
  extAuth:
    enablement: DisableAll 
---
## On a route that is not part of the gw gateway opt in
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  namespace: kgateway-test
  name: secure-route-policy2
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: route-example-disableall
  extAuth:
    extensionRef: 
      name: basic-extauth2
