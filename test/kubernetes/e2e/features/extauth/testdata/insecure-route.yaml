
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  namespace: kgateway-test
  name: route-example-insecure
spec:
  parentRefs:
    - name: super-gateway
      namespace: kgateway-test
  hostnames:
    - "insecureroute.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  namespace: kgateway-test
  name: insecure-route-policy
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: route-example-insecure
  extAuth:
    enablement: DisableAll 
