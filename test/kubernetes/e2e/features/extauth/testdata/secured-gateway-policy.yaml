apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  namespace: kgateway-test
  name: route-example-default
spec:
  parentRefs:
    - name: super-gateway
      namespace: kgateway-test
  hostnames:
    - "securegateways.com"
  rules:
    - backendRefs:
        - name: simple-svc
          port: 8080
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  namespace: kgateway-test
  name: gw-policy
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: super-gateway
  extAuth:
    extensionRef: 
      name: basic-extauth
