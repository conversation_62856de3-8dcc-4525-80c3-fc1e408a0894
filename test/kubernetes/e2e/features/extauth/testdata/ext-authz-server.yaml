

apiVersion: v1
kind: Service
metadata:
  namespace: kgateway-test
  name: ext-authz
  labels:
    app: ext-authz
spec:
  ports:
  # - name: http
  #   port: 8000
  #   targetPort: 8000
  - port: 4444
    targetPort: 9000
    protocol: TCP
    appProtocol: kubernetes.io/h2c
  selector:
    app: ext-authz
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: kgateway-test
  name: ext-authz
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ext-authz
  template:
    metadata:
      labels:
        app: ext-authz
        app.kubernetes.io/name: ext-authz
    spec:
      containers:
      - image: gcr.io/istio-testing/ext-authz:1.25-dev # use istio's extauthz for ease of use
        name: ext-authz
        ports:
        # - containerPort: 8000
        - containerPort: 9000
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  namespace: kgateway-test
  name: basic-extauth
spec:
  type: ExtAuth
  extAuth:
    grpcService:
      backendRef:
        name: ext-authz
        port: 4444
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  namespace: kgateway-test
  name: basic-extauth2
spec:
  type: ExtAuth
  extAuth:
    grpcService:
      backendRef:
        name: ext-authz
        port: 4444