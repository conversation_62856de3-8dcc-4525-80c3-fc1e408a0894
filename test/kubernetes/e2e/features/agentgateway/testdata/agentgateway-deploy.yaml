kind: GatewayParameters
apiVersion: gateway.kgateway.dev/v1alpha1
metadata:
  name: kgateway
spec:
  kube:
    agentGateway:
      enabled: true
---
kind: GatewayClass
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: agentgateway
spec:
  controllerName: kgateway.dev/kgateway
  parametersRef:
    group: gateway.kgateway.dev
    kind: GatewayParameters
    name: kgateway
    namespace: default
---
kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: agent-gateway
spec:
  gatewayClassName: agentgateway
  listeners:
    - protocol: kgateway.dev/mcp
      port: 8080
      name: mcp
      allowedRoutes:
        namespaces:
          from: All
    - protocol: kgateway.dev/a2a
      port: 9090
      name: a2a
      allowedRoutes:
        namespaces:
          from: All