apiVersion: v1
kind: Service
metadata:
  name: headless-example-svc
spec:
  clusterIP: None
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  containers:
    - name: nginx
      image: nginx:stable
      ports:
        - containerPort: 80
          name: http-web-svc
---
apiVersion: v1
kind: Namespace
metadata:
  name: curl
---
apiVersion: v1
kind: Pod
metadata:
  name: curl
  namespace: curl
  labels:
    app: curl
    version: v1
spec:
  containers:
    - name: curl
      image: curlimages/curl:7.83.1
      imagePullPolicy: IfNotPresent
      command:
        - "tail"
        - "-f"
        - "/dev/null"
      resources:
        requests:
          cpu: "100m"
        limits:
          cpu: "200m"