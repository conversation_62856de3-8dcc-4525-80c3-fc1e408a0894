apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gw
  namespace: default
spec:
  gatewayClassName: kgateway
  listeners:
  - allowedRoutes:
      namespaces:
        from: Same
    name: http
    port: 80
    protocol: HTTP
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: headless-httproute
  namespace: default
spec:
  hostnames:
  - headless.example.com
  parentRefs:
  - name: gw
  rules:
  - backendRefs:
    - name: headless-example-svc
      port: 8080
---
