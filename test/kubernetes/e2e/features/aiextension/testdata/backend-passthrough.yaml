apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: openai-override
  namespace: ai-test
spec:
  type: AI
  ai:
    llm:
      hostOverride:
        host: test-ai-provider.ai-test.svc.cluster.local
        port: 443
        insecureSkipVerify: true
      pathOverride:
        fullPath: "/api/v1/chat/completions"
      provider:
        openai:
          authToken:
            kind: "Passthrough"
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: openai
  namespace: ai-test
spec:
  type: AI
  ai:
    llm:
      hostOverride:
        host: test-ai-provider.ai-test.svc.cluster.local
        port: 443
        insecureSkipVerify: true
      provider:
        openai:
          authToken:
            kind: "Passthrough"
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: azure-openai
  namespace: ai-test
spec:
  type: AI
  ai:
    llm:
      hostOverride:
        host: test-ai-provider.ai-test.svc.cluster.local
        port: 443
        insecureSkipVerify: true
      provider:
        azureopenai:
          endpoint: ai-gateway.openai.azure.com
          deploymentName: gpt-4o-mini
          apiVersion: 2024-02-15-preview
          authToken:
            kind: "Passthrough"
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: gemini
  namespace: ai-test
spec:
  type: AI
  ai:
    llm:
      hostOverride:
        host: test-ai-provider.ai-test.svc.cluster.local
        port: 443
        insecureSkipVerify: true
      provider:
        gemini:
          model: "gemini-1.5-flash-001"
          apiVersion: "v1beta"
          authToken:
            kind: "Passthrough"
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: vertex-ai
  namespace: ai-test
spec:
  type: AI
  ai:
    llm:
      hostOverride:
        host: test-ai-provider.ai-test.svc.cluster.local
        port: 443
        insecureSkipVerify: true
      provider:
        vertexai:
          model: gemini-1.5-flash-001
          apiVersion: v1
          location: us-central1
          projectId: kgateway-project
          publisher: GOOGLE
          authToken:
            kind: "Passthrough"
