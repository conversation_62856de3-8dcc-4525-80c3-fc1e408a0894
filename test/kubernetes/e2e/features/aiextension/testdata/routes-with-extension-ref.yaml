apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: openai-override
  namespace: ai-test
spec:
  parentRefs:
    - name: ai-gateway
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /openai-override
      backendRefs:
        - name: openai-override
          group: gateway.kgateway.dev
          kind: Backend
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: openai-policy
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: openai
  namespace: ai-test
spec:
  parentRefs:
    - name: ai-gateway
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /openai
      backendRefs:
        - name: openai
          group: gateway.kgateway.dev
          kind: Backend
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: openai-policy
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: azure-openai
  namespace: ai-test
spec:
  parentRefs:
    - name: ai-gateway
      namespace: ai-test
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /azure
      backendRefs:
        - name: azure-openai
          namespace: ai-test
          group: gateway.kgateway.dev
          kind: Backend
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: openai-azure-policy
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: gemini
  namespace: ai-test
spec:
  parentRefs:
    - name: ai-gateway
      namespace: ai-test
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /gemini
      backendRefs:
        - name: gemini
          namespace: ai-test
          group: gateway.kgateway.dev
          kind: Backend
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: gemini-policy
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: vertex-ai
  namespace: ai-test
spec:
  parentRefs:
    - name: ai-gateway
      namespace: ai-test
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /vertex-ai
      backendRefs:
        - name: vertex-ai
          namespace: ai-test
          group: gateway.kgateway.dev
          kind: Backend
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: vertex-ai-policy
