# public cert of self-signed cert loaded into nginx, see nginx.yaml
# separate file so it can be deleted independently
apiVersion: v1
data:
  ca.crt: |
    -----BEGIN CERTIFICATE-----
    MIIDFTCCAf2gAwIBAgIUNrmDuzMzk7Fj5/qq6FdxOyhmYUIwDQYJKoZIhvcNAQEL
    BQAwFjEUMBIGA1UEAwwLZXhhbXBsZS5jb20wHhcNMjUwNjA1MTkyNjQ2WhcNMjUw
    NzA1MTkyNjQ2WjAWMRQwEgYDVQQDDAtleGFtcGxlLmNvbTCCASIwDQYJKoZIhvcN
    AQEBBQADggEPADCCAQoCggEBAKQE/tTuc2YkRdvtQeavYXWn7vMaZlN7CVdHz77L
    TbkQqgY9jW8z+fytLNezn+rRz2+Egyn+EZkZY/r5KfYKHcGbrYZym9P3roJUNkcv
    fx541TCmzVWb5NTBpyWfVRWmmvHIPRN6vzpUAy8IBV6Vi6drgYEfVLq80sTKI3xW
    JGq/tuIMRf4Vxsney0BTiH2R9EuxEd4yRVmZuXHnmRAd1Lvzj8AUPnRwGp3iDExb
    lLrf9atIubVK5wbeVayuqhZlA/vJI2ds2DwWk9GbXjTCsNdhNFxzScufuSslRCwc
    VEZywk0z/1d0XajyUwpazr5ZPM/vvfA5px6HlqbidShPZ18CAwEAAaNbMFkwFgYD
    VR0RBA8wDYILZXhhbXBsZS5jb20wCwYDVR0PBAQDAgeAMBMGA1UdJQQMMAoGCCsG
    AQUFBwMBMB0GA1UdDgQWBBQaC/3wAQewXS9TbWN+lejEulC5fDANBgkqhkiG9w0B
    AQsFAAOCAQEAOyQ7VNO04sjvzfyqXjh0qPXYx7Hyu06yMndr3B0XxG/9AZ396icS
    B3LZgZ6R7LsAHD4Fw6QsTWxCW0S6mVBRX8972DvtNZuOIB0zb1A5qORJ0DajzwAh
    l4wTra0oG6sFI1FSwl/I5poRiLMVUaoIzdW4HObhKIGe5F/J/iXtdAxFxLKtStQx
    M9sP76Nw3FgzsXmMd04of7hGYSwrpANvyd49JO/mIzVfy4MRtbn5m4XKtahtT8vv
    NGZZF13q6tdnAUptWWAvPShEeNj4xSw6Vam0JIbGcpH2Ch9ltOfDEDByELgdu7a/
    /vAjhVKjL51vkbtmRxRSYbJZmyp4gNUKcQ==
    -----END CERTIFICATE-----

kind: ConfigMap
metadata:
  name: ca
