apiVersion: v1
kind: Service
metadata:
  name: nginx
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
      name: http
    - protocol: TCP
      port: 8443
      targetPort: https-web-svc
      name: https
---
# nginx cert and key generated via:
# openssl req -x509 -out ex.crt -keyout ex.key \
#   -newkey rsa:2048 -nodes -sha256 \
#   -subj '/CN=example.com' -extensions EXT -config <( \
#    printf "[dn]\nCN=example.com\n[req]\ndistinguished_name = dn\n[EXT]\nsubjectAltName=DNS:example.com\nkeyUsage=digitalSignature\nextendedKeyUsage=serverAuth")
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-conf
data:
  nginx.conf: |
    user nginx;
    worker_processes  1;
    events {
      worker_connections  10240;
    }
    http {
      server {
          listen              80;
          listen              443 ssl;
          server_name         example.com;
          ssl_certificate     /etc/nginx/example.crt;
          ssl_certificate_key /etc/nginx/example.key;

          location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
        }
      }
    }
  example.crt: |
    -----BEGIN CERTIFICATE-----
    MIIDFTCCAf2gAwIBAgIUNrmDuzMzk7Fj5/qq6FdxOyhmYUIwDQYJKoZIhvcNAQEL
    BQAwFjEUMBIGA1UEAwwLZXhhbXBsZS5jb20wHhcNMjUwNjA1MTkyNjQ2WhcNMjUw
    NzA1MTkyNjQ2WjAWMRQwEgYDVQQDDAtleGFtcGxlLmNvbTCCASIwDQYJKoZIhvcN
    AQEBBQADggEPADCCAQoCggEBAKQE/tTuc2YkRdvtQeavYXWn7vMaZlN7CVdHz77L
    TbkQqgY9jW8z+fytLNezn+rRz2+Egyn+EZkZY/r5KfYKHcGbrYZym9P3roJUNkcv
    fx541TCmzVWb5NTBpyWfVRWmmvHIPRN6vzpUAy8IBV6Vi6drgYEfVLq80sTKI3xW
    JGq/tuIMRf4Vxsney0BTiH2R9EuxEd4yRVmZuXHnmRAd1Lvzj8AUPnRwGp3iDExb
    lLrf9atIubVK5wbeVayuqhZlA/vJI2ds2DwWk9GbXjTCsNdhNFxzScufuSslRCwc
    VEZywk0z/1d0XajyUwpazr5ZPM/vvfA5px6HlqbidShPZ18CAwEAAaNbMFkwFgYD
    VR0RBA8wDYILZXhhbXBsZS5jb20wCwYDVR0PBAQDAgeAMBMGA1UdJQQMMAoGCCsG
    AQUFBwMBMB0GA1UdDgQWBBQaC/3wAQewXS9TbWN+lejEulC5fDANBgkqhkiG9w0B
    AQsFAAOCAQEAOyQ7VNO04sjvzfyqXjh0qPXYx7Hyu06yMndr3B0XxG/9AZ396icS
    B3LZgZ6R7LsAHD4Fw6QsTWxCW0S6mVBRX8972DvtNZuOIB0zb1A5qORJ0DajzwAh
    l4wTra0oG6sFI1FSwl/I5poRiLMVUaoIzdW4HObhKIGe5F/J/iXtdAxFxLKtStQx
    M9sP76Nw3FgzsXmMd04of7hGYSwrpANvyd49JO/mIzVfy4MRtbn5m4XKtahtT8vv
    NGZZF13q6tdnAUptWWAvPShEeNj4xSw6Vam0JIbGcpH2Ch9ltOfDEDByELgdu7a/
    /vAjhVKjL51vkbtmRxRSYbJZmyp4gNUKcQ==
    -----END CERTIFICATE-----

  example.key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  terminationGracePeriodSeconds: 0
  containers:
  - name: nginx
    image: nginx:stable
    ports:
    - containerPort: 80
      name: http-web-svc
    - containerPort: 443
      name: https-web-svc
    volumeMounts:
    - name: nginx-conf
      mountPath: /etc/nginx/
      readOnly: true
  volumes:
  - name: nginx-conf
    configMap:
      name: nginx-conf
      items:
      - key: nginx.conf
        path: nginx.conf
      - key: example.crt
        path: example.crt
      - key: example.key
        path: example.key
