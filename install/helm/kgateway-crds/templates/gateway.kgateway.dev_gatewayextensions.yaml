---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  labels:
    app: kgateway
    app.kubernetes.io/name: kgateway
  name: gatewayextensions.gateway.kgateway.dev
spec:
  group: gateway.kgateway.dev
  names:
    categories:
    - kgateway
    kind: GatewayExtension
    listKind: GatewayExtensionList
    plural: gatewayextensions
    singular: gatewayextension
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Which extension type?
      jsonPath: .spec.type
      name: Type
      type: string
    - description: The age of the gatewayextension.
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              extAuth:
                properties:
                  grpcService:
                    properties:
                      authority:
                        type: string
                      backendRef:
                        properties:
                          group:
                            default: ""
                            maxLength: 253
                            pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                            type: string
                          kind:
                            default: Service
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                            type: string
                          name:
                            maxLength: 253
                            minLength: 1
                            type: string
                          namespace:
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                            type: string
                          port:
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                          weight:
                            default: 1
                            format: int32
                            maximum: 1000000
                            minimum: 0
                            type: integer
                        required:
                        - name
                        type: object
                        x-kubernetes-validations:
                        - message: Must have port for Service reference
                          rule: '(size(self.group) == 0 && self.kind == ''Service'')
                            ? has(self.port) : true'
                    required:
                    - backendRef
                    type: object
                required:
                - grpcService
                type: object
              extProc:
                properties:
                  grpcService:
                    properties:
                      authority:
                        type: string
                      backendRef:
                        properties:
                          group:
                            default: ""
                            maxLength: 253
                            pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                            type: string
                          kind:
                            default: Service
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                            type: string
                          name:
                            maxLength: 253
                            minLength: 1
                            type: string
                          namespace:
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                            type: string
                          port:
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                          weight:
                            default: 1
                            format: int32
                            maximum: 1000000
                            minimum: 0
                            type: integer
                        required:
                        - name
                        type: object
                        x-kubernetes-validations:
                        - message: Must have port for Service reference
                          rule: '(size(self.group) == 0 && self.kind == ''Service'')
                            ? has(self.port) : true'
                    required:
                    - backendRef
                    type: object
                required:
                - grpcService
                type: object
              rateLimit:
                properties:
                  domain:
                    type: string
                  failOpen:
                    default: false
                    type: boolean
                  grpcService:
                    properties:
                      authority:
                        type: string
                      backendRef:
                        properties:
                          group:
                            default: ""
                            maxLength: 253
                            pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                            type: string
                          kind:
                            default: Service
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                            type: string
                          name:
                            maxLength: 253
                            minLength: 1
                            type: string
                          namespace:
                            maxLength: 63
                            minLength: 1
                            pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                            type: string
                          port:
                            format: int32
                            maximum: 65535
                            minimum: 1
                            type: integer
                          weight:
                            default: 1
                            format: int32
                            maximum: 1000000
                            minimum: 0
                            type: integer
                        required:
                        - name
                        type: object
                        x-kubernetes-validations:
                        - message: Must have port for Service reference
                          rule: '(size(self.group) == 0 && self.kind == ''Service'')
                            ? has(self.port) : true'
                    required:
                    - backendRef
                    type: object
                  timeout:
                    default: 20ms
                    pattern: ^([0-9]{1,5}(h|m|s|ms)){1,4}$
                    type: string
                required:
                - domain
                - grpcService
                type: object
              type:
                enum:
                - ExtAuth
                - ExtProc
                - RateLimit
                - Extended
                type: string
            required:
            - type
            type: object
            x-kubernetes-validations:
            - message: ExtAuth must be set when type is ExtAuth
              rule: self.type != 'ExtAuth' || has(self.extAuth)
            - message: ExtProc must be set when type is ExtProc
              rule: self.type != 'ExtProc' || has(self.extProc)
            - message: RateLimit must be set when type is RateLimit
              rule: self.type != 'RateLimit' || has(self.rateLimit)
            - message: ExtAuth must not be set when type is not ExtAuth
              rule: self.type == 'ExtAuth' || !has(self.extAuth)
            - message: ExtProc must not be set when type is not ExtProc
              rule: self.type == 'ExtProc' || !has(self.extProc)
            - message: RateLimit must not be set when type is not RateLimit
              rule: self.type == 'RateLimit' || !has(self.rateLimit)
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
