---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  labels:
    app: kgateway
    app.kubernetes.io/name: kgateway
    gateway.networking.k8s.io/policy: Direct
  name: httplistenerpolicies.gateway.kgateway.dev
spec:
  group: gateway.kgateway.dev
  names:
    categories:
    - kgateway
    kind: HTTPListenerPolicy
    listKind: HTTPListenerPolicyList
    plural: httplistenerpolicies
    singular: httplistenerpolicy
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              accessLog:
                items:
                  properties:
                    fileSink:
                      properties:
                        jsonFormat:
                          type: object
                          x-kubernetes-preserve-unknown-fields: true
                        path:
                          type: string
                        stringFormat:
                          type: string
                      required:
                      - path
                      type: object
                      x-kubernetes-validations:
                      - message: only one of 'StringFormat' or 'JsonFormat' may be
                          set
                        rule: (has(self.stringFormat) && !has(self.jsonFormat)) ||
                          (!has(self.stringFormat) && has(self.jsonFormat))
                    filter:
                      allOf:
                      - maxProperties: 1
                        minProperties: 1
                      - maxProperties: 1
                        minProperties: 1
                      properties:
                        andFilter:
                          items:
                            maxProperties: 1
                            minProperties: 1
                            properties:
                              celFilter:
                                properties:
                                  match:
                                    type: string
                                required:
                                - match
                                type: object
                              durationFilter:
                                properties:
                                  op:
                                    enum:
                                    - EQ
                                    - GE
                                    - LE
                                    type: string
                                  value:
                                    format: int32
                                    maximum: 4294967295
                                    minimum: 0
                                    type: integer
                                required:
                                - op
                                type: object
                              grpcStatusFilter:
                                properties:
                                  exclude:
                                    type: boolean
                                  statuses:
                                    items:
                                      enum:
                                      - OK
                                      - CANCELED
                                      - UNKNOWN
                                      - INVALID_ARGUMENT
                                      - DEADLINE_EXCEEDED
                                      - NOT_FOUND
                                      - ALREADY_EXISTS
                                      - PERMISSION_DENIED
                                      - RESOURCE_EXHAUSTED
                                      - FAILED_PRECONDITION
                                      - ABORTED
                                      - OUT_OF_RANGE
                                      - UNIMPLEMENTED
                                      - INTERNAL
                                      - UNAVAILABLE
                                      - DATA_LOSS
                                      - UNAUTHENTICATED
                                      type: string
                                    minItems: 1
                                    type: array
                                type: object
                              headerFilter:
                                properties:
                                  header:
                                    properties:
                                      name:
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      type:
                                        default: Exact
                                        enum:
                                        - Exact
                                        - RegularExpression
                                        type: string
                                      value:
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                required:
                                - header
                                type: object
                              notHealthCheckFilter:
                                type: boolean
                              responseFlagFilter:
                                properties:
                                  flags:
                                    items:
                                      type: string
                                    minItems: 1
                                    type: array
                                required:
                                - flags
                                type: object
                              statusCodeFilter:
                                properties:
                                  op:
                                    enum:
                                    - EQ
                                    - GE
                                    - LE
                                    type: string
                                  value:
                                    format: int32
                                    maximum: 4294967295
                                    minimum: 0
                                    type: integer
                                required:
                                - op
                                type: object
                              traceableFilter:
                                type: boolean
                            type: object
                          minItems: 2
                          type: array
                        celFilter:
                          properties:
                            match:
                              type: string
                          required:
                          - match
                          type: object
                        durationFilter:
                          properties:
                            op:
                              enum:
                              - EQ
                              - GE
                              - LE
                              type: string
                            value:
                              format: int32
                              maximum: 4294967295
                              minimum: 0
                              type: integer
                          required:
                          - op
                          type: object
                        grpcStatusFilter:
                          properties:
                            exclude:
                              type: boolean
                            statuses:
                              items:
                                enum:
                                - OK
                                - CANCELED
                                - UNKNOWN
                                - INVALID_ARGUMENT
                                - DEADLINE_EXCEEDED
                                - NOT_FOUND
                                - ALREADY_EXISTS
                                - PERMISSION_DENIED
                                - RESOURCE_EXHAUSTED
                                - FAILED_PRECONDITION
                                - ABORTED
                                - OUT_OF_RANGE
                                - UNIMPLEMENTED
                                - INTERNAL
                                - UNAVAILABLE
                                - DATA_LOSS
                                - UNAUTHENTICATED
                                type: string
                              minItems: 1
                              type: array
                          type: object
                        headerFilter:
                          properties:
                            header:
                              properties:
                                name:
                                  maxLength: 256
                                  minLength: 1
                                  pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                  type: string
                                type:
                                  default: Exact
                                  enum:
                                  - Exact
                                  - RegularExpression
                                  type: string
                                value:
                                  maxLength: 4096
                                  minLength: 1
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                          required:
                          - header
                          type: object
                        notHealthCheckFilter:
                          type: boolean
                        orFilter:
                          items:
                            maxProperties: 1
                            minProperties: 1
                            properties:
                              celFilter:
                                properties:
                                  match:
                                    type: string
                                required:
                                - match
                                type: object
                              durationFilter:
                                properties:
                                  op:
                                    enum:
                                    - EQ
                                    - GE
                                    - LE
                                    type: string
                                  value:
                                    format: int32
                                    maximum: 4294967295
                                    minimum: 0
                                    type: integer
                                required:
                                - op
                                type: object
                              grpcStatusFilter:
                                properties:
                                  exclude:
                                    type: boolean
                                  statuses:
                                    items:
                                      enum:
                                      - OK
                                      - CANCELED
                                      - UNKNOWN
                                      - INVALID_ARGUMENT
                                      - DEADLINE_EXCEEDED
                                      - NOT_FOUND
                                      - ALREADY_EXISTS
                                      - PERMISSION_DENIED
                                      - RESOURCE_EXHAUSTED
                                      - FAILED_PRECONDITION
                                      - ABORTED
                                      - OUT_OF_RANGE
                                      - UNIMPLEMENTED
                                      - INTERNAL
                                      - UNAVAILABLE
                                      - DATA_LOSS
                                      - UNAUTHENTICATED
                                      type: string
                                    minItems: 1
                                    type: array
                                type: object
                              headerFilter:
                                properties:
                                  header:
                                    properties:
                                      name:
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      type:
                                        default: Exact
                                        enum:
                                        - Exact
                                        - RegularExpression
                                        type: string
                                      value:
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                required:
                                - header
                                type: object
                              notHealthCheckFilter:
                                type: boolean
                              responseFlagFilter:
                                properties:
                                  flags:
                                    items:
                                      type: string
                                    minItems: 1
                                    type: array
                                required:
                                - flags
                                type: object
                              statusCodeFilter:
                                properties:
                                  op:
                                    enum:
                                    - EQ
                                    - GE
                                    - LE
                                    type: string
                                  value:
                                    format: int32
                                    maximum: 4294967295
                                    minimum: 0
                                    type: integer
                                required:
                                - op
                                type: object
                              traceableFilter:
                                type: boolean
                            type: object
                          minItems: 2
                          type: array
                        responseFlagFilter:
                          properties:
                            flags:
                              items:
                                type: string
                              minItems: 1
                              type: array
                          required:
                          - flags
                          type: object
                        statusCodeFilter:
                          properties:
                            op:
                              enum:
                              - EQ
                              - GE
                              - LE
                              type: string
                            value:
                              format: int32
                              maximum: 4294967295
                              minimum: 0
                              type: integer
                          required:
                          - op
                          type: object
                        traceableFilter:
                          type: boolean
                      type: object
                    grpcService:
                      properties:
                        additionalRequestHeadersToLog:
                          items:
                            type: string
                          type: array
                        additionalResponseHeadersToLog:
                          items:
                            type: string
                          type: array
                        additionalResponseTrailersToLog:
                          items:
                            type: string
                          type: array
                        backendRef:
                          properties:
                            group:
                              default: ""
                              maxLength: 253
                              pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                              type: string
                            kind:
                              default: Service
                              maxLength: 63
                              minLength: 1
                              pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                              type: string
                            name:
                              maxLength: 253
                              minLength: 1
                              type: string
                            namespace:
                              maxLength: 63
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                              type: string
                            port:
                              format: int32
                              maximum: 65535
                              minimum: 1
                              type: integer
                            weight:
                              default: 1
                              format: int32
                              maximum: 1000000
                              minimum: 0
                              type: integer
                          required:
                          - name
                          type: object
                          x-kubernetes-validations:
                          - message: Must have port for Service reference
                            rule: '(size(self.group) == 0 && self.kind == ''Service'')
                              ? has(self.port) : true'
                        logName:
                          type: string
                      required:
                      - backendRef
                      - logName
                      type: object
                  type: object
                type: array
              targetRefs:
                items:
                  properties:
                    group:
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      maxLength: 253
                      minLength: 1
                      type: string
                  required:
                  - group
                  - kind
                  - name
                  type: object
                maxItems: 16
                minItems: 1
                type: array
              targetSelectors:
                items:
                  properties:
                    group:
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    matchLabels:
                      additionalProperties:
                        type: string
                      type: object
                  required:
                  - group
                  - kind
                  - matchLabels
                  type: object
                type: array
              upgradeConfig:
                properties:
                  enabledUpgrades:
                    items:
                      type: string
                    minItems: 1
                    type: array
                type: object
            type: object
          status:
            properties:
              ancestors:
                items:
                  properties:
                    ancestorRef:
                      properties:
                        group:
                          default: gateway.networking.k8s.io
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          default: Gateway
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                        port:
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        sectionName:
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                      required:
                      - name
                      type: object
                    conditions:
                      items:
                        properties:
                          lastTransitionTime:
                            format: date-time
                            type: string
                          message:
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      minItems: 1
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*\/[A-Za-z0-9\/\-._~%!$&'()*+,;=:]+$
                      type: string
                  required:
                  - ancestorRef
                  - controllerName
                  type: object
                maxItems: 16
                type: array
            required:
            - ancestors
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
