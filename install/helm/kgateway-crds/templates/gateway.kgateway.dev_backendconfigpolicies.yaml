---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  labels:
    app: kgateway
    app.kubernetes.io/name: kgateway
  name: backendconfigpolicies.gateway.kgateway.dev
spec:
  group: gateway.kgateway.dev
  names:
    categories:
    - kgateway
    kind: BackendConfigPolicy
    listKind: BackendConfigPolicyList
    plural: backendconfigpolicies
    singular: backendconfigpolicy
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              commonHttpProtocolOptions:
                properties:
                  headersWithUnderscoresAction:
                    enum:
                    - Allow
                    - RejectRequest
                    - DropHeader
                    type: string
                  idleTimeout:
                    type: string
                    x-kubernetes-validations:
                    - message: idleTimeout must be a valid duration string
                      rule: duration(self) >= duration('0s')
                  maxHeadersCount:
                    type: integer
                  maxRequestsPerConnection:
                    type: integer
                  maxStreamDuration:
                    type: string
                    x-kubernetes-validations:
                    - message: maxStreamDuration must be a valid duration string
                      rule: duration(self) >= duration('0s')
                type: object
                x-kubernetes-validations:
                - message: idleTimeout must be a valid duration string (e.g. "1s",
                    "500ms")
                  rule: (!has(self.idleTimeout) || (has(self.idleTimeout) && self.idleTimeout.matches('^([0-9]{1,5}(h|m|s|ms)){1,4}$')))
                - message: maxStreamDuration must be a valid duration string (e.g.
                    "1s", "500ms")
                  rule: (!has(self.maxStreamDuration) || (has(self.maxStreamDuration)
                    && self.maxStreamDuration.matches('^([0-9]{1,5}(h|m|s|ms)){1,4}$')))
              connectTimeout:
                type: string
                x-kubernetes-validations:
                - message: connectTimeout must be a valid duration string
                  rule: duration(self) >= duration('0s')
              http1ProtocolOptions:
                properties:
                  enableTrailers:
                    type: boolean
                  headerFormat:
                    enum:
                    - ProperCaseHeaderKeyFormat
                    - PreserveCaseHeaderKeyFormat
                    type: string
                  overrideStreamErrorOnInvalidHttpMessage:
                    type: boolean
                type: object
              perConnectionBufferLimitBytes:
                type: integer
              sslConfig:
                properties:
                  allowRenegotiation:
                    type: boolean
                  alpnProtocols:
                    items:
                      type: string
                    type: array
                  oneWayTLS:
                    type: boolean
                  secretRef:
                    properties:
                      name:
                        default: ""
                        type: string
                    type: object
                    x-kubernetes-map-type: atomic
                  sni:
                    type: string
                  sslFiles:
                    properties:
                      rootCA:
                        type: string
                      tlsCertificate:
                        type: string
                      tlsKey:
                        type: string
                    type: object
                    x-kubernetes-validations:
                    - message: At least one of tlsCertificate, tlsKey, or rootCA must
                        be set in SSLFiles
                      rule: has(self.tlsCertificate) || has(self.tlsKey) || has(self.rootCA)
                  sslParameters:
                    properties:
                      cipherSuites:
                        items:
                          type: string
                        type: array
                      ecdhCurves:
                        items:
                          type: string
                        type: array
                      tlsMaxVersion:
                        enum:
                        - AUTO
                        - "1.0"
                        - "1.1"
                        - "1.2"
                        - "1.3"
                        type: string
                      tlsMinVersion:
                        enum:
                        - AUTO
                        - "1.0"
                        - "1.1"
                        - "1.2"
                        - "1.3"
                        type: string
                    type: object
                  verifySubjectAltName:
                    items:
                      type: string
                    type: array
                type: object
                x-kubernetes-validations:
                - message: Exactly one of secretRef or sslFiles must be set in SSLConfig
                  rule: has(self.secretRef) != has(self.sslFiles)
              targetRefs:
                items:
                  properties:
                    group:
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      maxLength: 253
                      minLength: 1
                      type: string
                  required:
                  - group
                  - kind
                  - name
                  type: object
                maxItems: 16
                minItems: 1
                type: array
              targetSelectors:
                items:
                  properties:
                    group:
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    matchLabels:
                      additionalProperties:
                        type: string
                      type: object
                  required:
                  - group
                  - kind
                  - matchLabels
                  type: object
                type: array
              tcpKeepalive:
                properties:
                  keepAliveInterval:
                    type: string
                    x-kubernetes-validations:
                    - message: keepAliveInterval must be a valid duration string
                      rule: duration(self) >= duration('0s')
                    - message: keepAliveInterval must be at least 1 second
                      rule: duration(self) >= duration('1s')
                  keepAliveProbes:
                    type: integer
                  keepAliveTime:
                    type: string
                    x-kubernetes-validations:
                    - message: keepAliveTime must be a valid duration string
                      rule: duration(self) >= duration('0s')
                    - message: keepAliveTime must be at least 1 second
                      rule: duration(self) >= duration('1s')
                type: object
            type: object
          status:
            properties:
              ancestors:
                items:
                  properties:
                    ancestorRef:
                      properties:
                        group:
                          default: gateway.networking.k8s.io
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          default: Gateway
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                        port:
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        sectionName:
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                      required:
                      - name
                      type: object
                    conditions:
                      items:
                        properties:
                          lastTransitionTime:
                            format: date-time
                            type: string
                          message:
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      minItems: 1
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*\/[A-Za-z0-9\/\-._~%!$&'()*+,;=:]+$
                      type: string
                  required:
                  - ancestorRef
                  - controllerName
                  type: object
                maxItems: 16
                type: array
            required:
            - ancestors
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
