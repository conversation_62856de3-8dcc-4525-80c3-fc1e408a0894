apiVersion: v1
kind: Service
metadata:
  name: {{ include "kgateway.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kgateway.labels" . | nindent 4 }}
spec:
  type: {{ .Values.controller.service.type }}
  ports:
  - name: grpc-xds
    protocol: TCP
    port: {{ .Values.controller.service.ports.grpc }}
    targetPort: {{ .Values.controller.service.ports.grpc }}
  selector:
    {{- include "kgateway.selectorLabels" . | nindent 4 }}
