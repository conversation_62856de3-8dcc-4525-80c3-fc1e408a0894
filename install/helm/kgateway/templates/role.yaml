---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kgateway-{{ .Release.Namespace }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  - serviceaccounts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - watch
- apiGroups:
  - ""
  resources:
  - endpoints
  - namespaces
  - nodes
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.kgateway.dev
  resources:
  - backendconfigpolicies
  - backends
  - directresponses
  - gatewayextensions
  - gatewayparameters
  - httplistenerpolicies
  - trafficpolicies
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.kgateway.dev
  resources:
  - backendconfigpolicies/status
  - backends/status
  - directresponses/status
  - gatewayextensions/status
  - gatewayparameters/status
  - httplistenerpolicies/status
  - trafficpolicies/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - backendtlspolicies
  - gateways
  - grpcroutes
  - httproutes
  - referencegrants
  - tcproutes
  - tlsroutes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - backendtlspolicies/status
  - gatewayclasses/status
  - gateways/status
  - grpcroutes/status
  - httproutes/status
  - tcproutes/status
  - tlsroutes/status
  verbs:
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses
  verbs:
  - create
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.x-k8s.io
  resources:
  - xlistenersets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.x-k8s.io
  resources:
  - xlistenersets/status
  verbs:
  - patch
  - update
- apiGroups:
  - networking.istio.io
  resources:
  - destinationrules
  - serviceentries
  - workloadentries
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - security.istio.io
  resources:
  - authorizationpolicies
  verbs:
  - get
  - list
  - watch
