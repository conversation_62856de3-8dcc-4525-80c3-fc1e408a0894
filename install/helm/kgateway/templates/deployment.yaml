apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kgateway.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kgateway.labels" . | nindent 4 }}
  {{- with .Values.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.controller.replicaCount }}
  selector:
    matchLabels:
      {{- include "kgateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "kgateway.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "kgateway.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.controller.image.registry | default .Values.image.registry }}/{{ .Values.controller.image.repository }}:{{ .Values.controller.image.tag | default .Values.image.tag | default .Chart.Version }}"
          imagePullPolicy: {{ .Values.controller.image.pullPolicy | default .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.controller.service.ports.grpc }}
              name: grpc-xds
              protocol: TCP
            - containerPort: {{ .Values.controller.service.ports.health }}
              name: health
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readyz
              port: {{ .Values.controller.service.ports.health }}
            initialDelaySeconds: 3
            periodSeconds: 10
          env:
            - name: GOMEMLIMIT
              valueFrom:
                resourceFieldRef:
                  divisor: "1"
                  resource: limits.memory
            - name: GOMAXPROCS
              valueFrom:
                resourceFieldRef:
                  divisor: "1"
                  resource: limits.cpu
            - name: KGW_LOG_LEVEL
              value: {{ .Values.controller.logLevel | quote }}
            - name: KGW_XDS_SERVICE_NAME
              value: {{ include "kgateway.fullname" . }}
            - name: KGW_XDS_SERVICE_PORT
              value: {{ .Values.controller.service.ports.grpc | quote }}
            {{- if .Values.inferenceExtension.enabled }}
            - name: KGW_ENABLE_INFER_EXT
              value: "true"
            {{- end }}
            {{- if .Values.inferenceExtension.autoProvision }}
            - name: KGW_INFER_EXT_AUTO_PROVISION
              value: "true"
            {{- end }}
            - name: KGW_DEFAULT_IMAGE_REGISTRY
              value: {{ .Values.image.registry }}
            - name: KGW_DEFAULT_IMAGE_TAG
              value: {{ .Values.image.tag | default .Chart.Version }}
            - name: KGW_DEFAULT_IMAGE_PULL_POLICY
              value: {{ .Values.image.pullPolicy | default "IfNotPresent" }}
            - name: KGW_DISCOVERY_NAMESPACE_SELECTORS
              value: {{ .Values.discoveryNamespaceSelectors | toJson | quote }}
            {{- if .Values.controller.extraEnv }}
            {{- range $key, $value := .Values.controller.extraEnv }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
            {{- if .Values.agentGateway.enabled }}
            - name: KGW_ENABLE_AGENT_GATEWAY
              value: "true"
            {{- end }}
            # TODO: Remove this once the cleanup is done. Required as the gloo-system
            # namespace is the default namespace and conformance will fail as a result.
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
