{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "kgateway.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kgateway.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kgateway.name" . }}-role-{{ .Release.Namespace }}
subjects:
- kind: ServiceAccount
  name: {{ include "kgateway.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: kgateway-{{ .Release.Namespace }}
  apiGroup: rbac.authorization.k8s.io
