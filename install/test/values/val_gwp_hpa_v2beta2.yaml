gatewayProxies:
  gatewayProxy:
    horizontalPodAutoscaler:
      apiVersion: autoscaling/v2beta2
      minReplicas: 1
      maxReplicas: 2
      metrics:
        - type: Resource
          resource:
            name: cpu
            target:
              type: Utilization
              averageUtilization: 50
      behavior:
        scaleDown:
          policies:
            - type: Pods
              value: 4
              periodSeconds: 60
            - type: Percent
              value: 10
              periodSeconds: 60
