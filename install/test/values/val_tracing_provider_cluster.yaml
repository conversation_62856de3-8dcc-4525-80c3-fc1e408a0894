gatewayProxies:
  gatewayProxy:
    tracing:
      provider:
        zipkinConfig:
          collector_cluster: zipkin
          collector_endpoint: /api/v2/spans
      cluster:
      - name: zipkin
        connect_timeout: 1s
        type: STRICT_DNS
        respect_dns_ttl: true
        lb_policy: ROUND_ROBIN
        load_assignment:
          cluster_name: zipkin
          endpoints:
          - lb_endpoints:
            - endpoint:
                address:
                  socket_address:
                    address: zipkin
                    port_value: 1234
