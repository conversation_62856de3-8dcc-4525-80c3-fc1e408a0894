gatewayProxies:
  gatewayProxy:
    gatewaySettings:
      useProxyProto: true
      customHttpGateway:
        virtualServices:
        - name: one
          namespace: one
      customHttpsGateway:
        virtualServices:
        - name: one
          namespace: one
  testName:
    gatewaySettings:
      useProxyProto: true
      customHttpGateway:
        virtualServices:
        - name: one
          namespace: one
      customHttpsGateway:
        virtualServices:
        - name: one
          namespace: one
    kind:
      deployment:
        replicas: 1
    podTemplate:
      disableNetBind: false
      floatingUserId: false
      httpPort: 8081
      httpsPort: 8444
      image:
        pullPolicy: Always
        repository: gloo-envoy-wrapper
        tag: dev
      probes: false
      runAsUser: 10101
      runUnprivileged: false
      tolerations: null
    readConfig: false
    service:
      httpPort: 80
      httpsPort: 443
      type: LoadBalancer
    stats:
      enabled: true
    configMap:
      data: null
