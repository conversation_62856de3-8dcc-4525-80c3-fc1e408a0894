gatewayProxies:
  gatewayProxy:
    envoyBootstrapExtensions:
    - name: envoy.wasm_service
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.wasm.v3.WasmService
        config:
          name: "my_plugin"
          vm_config:
            runtime: "envoy.wasm.runtime.v8"
            code:
              local:
                filename: "/etc/envoy_filter_http_wasm_example.wasm"
        singleton: true