layered_runtime:
  layers:
    - name: static_layer
      static_layer:
        envoy.reloadable_features.strict_1xx_and_204_response_headers: false
        envoy.reloadable_features.disable_tls_inspector_injection: false
        overload:
          global_downstream_max_connections: 250000
        upstream:
          healthy_panic_threshold:
            value: 50
    - name: admin_layer
      admin_layer: {}
node:
  cluster: gateway
  id: "{{.PodName}}.{{.PodNamespace}}"
  metadata:
    # role's value is the key for the in-memory xds cache (projects/gloo/pkg/xds/envoy.go)
    role: "gloo-system~gateway-proxy"
static_resources:
  listeners:
    - name: prometheus_listener
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 8081
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                codec_type: AUTO
                stat_prefix: prometheus
                route_config:
                  name: prometheus_route
                  virtual_hosts:
                    - name: prometheus_host
                      domains:
                        - "*"
                      routes:
                        - match:
                            path: "/ready"
                            headers:
                              - name: ":method"
                                exact_match: GET
                          route:
                            cluster: admin_port_cluster
                        - match:
                            prefix: "/metrics"
                            headers:
                              - name: ":method"
                                exact_match: GET
                          route:
                            prefix_rewrite: /stats/prometheus?usedonly
                            cluster: admin_port_cluster
                http_filters:
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
  clusters:
    - name: gloo.gloo-system.svc.cluster.local:9977
      alt_stat_name: xds_cluster
      connect_timeout: 5.000s
      load_assignment:
        cluster_name: gloo.gloo-system.svc.cluster.local:9977
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: gloo.gloo-system.svc.cluster.local
                      port_value: 9977
      http2_protocol_options: {}
      upstream_connection_options:
        tcp_keepalive:
          keepalive_time: 60
      type: STRICT_DNS
      respect_dns_ttl: true
    - name: rest_xds_cluster
      alt_stat_name: rest_xds_cluster
      connect_timeout: 5.000s
      load_assignment:
        cluster_name: rest_xds_cluster
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: gloo.gloo-system.svc.cluster.local
                      port_value: 9976
      upstream_connection_options:
        tcp_keepalive:
          keepalive_time: 60
      type: STRICT_DNS
      respect_dns_ttl: true
    - name: wasm-cache
      connect_timeout: 5.000s
      load_assignment:
        cluster_name: wasm-cache
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: gloo.gloo-system.svc.cluster.local
                      port_value: 9979
      upstream_connection_options:
        tcp_keepalive:
          keepalive_time: 60
      type: STRICT_DNS
      respect_dns_ttl: true
    - name: admin_port_cluster
      connect_timeout: 5.000s
      type: STATIC
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: admin_port_cluster
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 19000

dynamic_resources:
  ads_config:
    transport_api_version: V3
    api_type: GRPC
    rate_limit_settings: {}
    grpc_services:
      - envoy_grpc: {cluster_name: gloo.gloo-system.svc.cluster.local:9977}
  cds_config:
    resource_api_version: V3
    ads: {}
  lds_config:
    resource_api_version: V3
    ads: {}
admin:
  access_log_path: /dev/null
  address:
    socket_address:
      address: 127.0.0.1
      port_value: 19000
