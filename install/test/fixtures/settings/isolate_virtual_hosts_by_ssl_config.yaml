apiVersion: gloo.solo.io/v1
kind: Settings
metadata:
  labels:
    app: gloo
    gloo: settings
  name: default
  namespace: {{ . }}
spec:
  discovery:
    fdsMode: WHITELIST
  gateway:
    readGatewaysFromAllNamespaces: false
    enableGatewayController: true
    isolateVirtualHostsBySslConfig: true
    validation:
      fullEnvoyValidation: false
      alwaysAccept: true
      allowWarnings: true
      warnMissingTlsSecret: true
      serverEnabled: true
      disableTransformationValidation: false
      warnRouteShortCircuiting: false
      proxyValidationServerAddr: gloo:9988
      validationServerGrpcMaxSizeBytes: 104857600
  gloo:
    regexMaxProgramSize: 1024
    enableRestEds: false
    xdsBindAddr: 0.0.0.0:9977
    restXdsBindAddr: 0.0.0.0:9976
    proxyDebugBindAddr: 0.0.0.0:9966
    disableKubernetesDestinations: false
    disableProxyGarbageCollection: false
    invalidConfigPolicy:
      invalidRouteResponseBody: Gloo Gateway has invalid configuration. Administrators should run `glooctl check` to find and fix config errors.
      invalidRouteResponseCode: 404
      replaceInvalidRoutes: false
    istioOptions:
     appendXForwardedHost: true
     enableAutoMtls: false
     enableIntegration: false
  kubernetesArtifactSource: {}
  kubernetesConfigSource: {}
  kubernetesSecretSource: {}
  refreshRate: 60s
  discoveryNamespace: {{ . }}
