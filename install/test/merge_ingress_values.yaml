discovery:
  fdsMode: WHITELIST
gateway:
  enabled: false
ingress:
  deployment:
    runAsUser: 10101
    image:
      # This is where we override the ingress image
      registry: docker.io/ilackarms
      repository: ingress
      tag: test-ilackarms
      pullPolicy: Always
    replicas: 1
    stats: false
  enabled: false
k8s:
  clusterName: cluster.local
namespace:
  create: true
global:
  glooRbac:
    create: true
settings:
  create: true
  integrations:
    knative:
      enabled: true
      version: 0.8.0
      proxy:
        httpPort: 80
        httpsPort: 443
        image:
          pullPolicy: Always
          registry: quay.io/solo-io
          repository: gloo-envoy-wrapper
          tag: 0.18.9
        replicas: 1
        stats: true