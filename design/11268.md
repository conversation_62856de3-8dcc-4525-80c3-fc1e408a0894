# EP-11268: Policy Application and Merging

* Issue: [11268](https://github.com/kgateway-dev/kgateway/issues/11268)

## Background

This document describes how multiple policies targeting the same resource are prioritized, merged (when the policy plugin supports merging), and applied.


## Motivation

It is possible that multiple policies, either belonging to the same API Kind or different, target the same resource. Such policies may either augment each other, or result in a conflict that needs to be gracefully handled. Thus, it is important to define how policies are prioritized, merged, and applied.


## Goals

- Define priority for policies belonging to the same GroupKind.
- Define policy application semantics.

## Non-Goals

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

## Implementation Details

### Policy Priority

Targeting policies of the same API Kind are sorted by their creation timestamp (oldest to newest) such that an older policy has higher priority than a newer one in the context of policy application or merging. A lower priority policy must not override a higher priority policy, but may augment it by configuring additional fields on the policy's output.

The hierarchy of a targeted resource also determines the priority of policies targeting the resource. Generally, more specific policies have higher priority than less specific ones, with route delegation being an exception where parent policies are prioritized over child policies by default.

For `Gateways`, a policy targeting a `Listener` is higher priority than a policy targeting the whole `Gateway`. `PListener-t0 > ... PListener-tn > PGateway-t0 >  ... PGateway-tn`, where PListener is a policy targeting a `Listener` using `sectionName`, PGateway is a policy targeting the `Gateway`, and `t` is the creation timestamp.

For `HTTPRoutes`, `ExtensionRef` based policies are higher priority than `TargetRefs` based policies, and `TargetRefs` based policies targeting a specific `HTTPRouteRule` are higher priority than those targeting the whole `HTTPRoute`.
`PExtensionRef > PTargetRef-with-sectionName-t0 > ... PTargetRef-with-sectionName-tn > PTargetRef-without-sectionName-t0 > ... PTargetRef-without-sectionName-tn`, where PExtensionRef is a policy attached using `ExtensionRef`, PTargetRef-with-sectionName is a policy targeted a specific `HTTPRouteRule`, PTargetRef-without-sectionName is a policy targeting the `HTTPRoute`, and `t` is the creation timestamp.

Policies targeting an HTTPRoute in a delegation chain are prioritized based on whether the policy targets an ancestor route in the chain or the route itself. For more details, refer to [policy inheritance and merging for route delegation](/design/10943.md#trafficpolicy-inheritance-and-merging)

### Policy Application

Policies for a given GroupKind are implemented by a specific plugin. When applying policies attached to a resource (Gateway, HTTPRoute, etc.), all attached policies are applied as follows:
1. All policies (sorted by creation timestamp) belonging to the same GroupKind processed together.
1. If the plugin supports merging, it must implement the `PolicyPlugin.MergePolicies` method, in which case all policies for the same GroupKind are merged into a single policy before the plugin's Apply*() method is invoked to apply the merged policy.
1. Otherwise, policies for the same GroupKind are processed in priority order (oldest to newest) by invoking the plugin's Apply*() method. It is the plugin's responsibility to ensure it does not override a higher priority policy when applying a lower priority policy.
1. The above steps are repeated for policies belonging to different GroupKinds.

#### Plugin Ordering

Built-in policies, implemented for the `VirtualBuiltInGK` GroupKind, are always applied last so that they can override other policies. Built-in policies correspond to policies that are a part of the upstream Gateway API spec, such as `HTTPRouteFilterURLRewrite`, `HTTPRouteTimeouts`, `HTTPRouteRetry`, etc., and are considered higher in priority than other forms of policy attachments (`ExtensionRef` or `TargetRefs`).

Plugin ordering is enforced by iterating `AttachedPolicies` on the targeted resource by invoking `ApplyOrderedGroupKinds()` that returns the list of GroupKinds in the order they should be processed. Currently, the ordering is limited to allow built-in policies to be applied last.


### Policy Merging

Currently, only the `TrafficPolicy` plugin supports policy merging. The merging ensures that lower priority policies can set top-level policy fields that are not set by higher priority policies.

Consider [this example](/internal/kgateway/translator/gateway/testutils/inputs/traffic-policy/merge.yaml) were the `example-route` HTTPRoute has 3 TrafficPolicies attached to it:
1. `extensionref-policy` which is referenced in the `example-route` HTTPRoute and has the highest priority.
1. `policy-with-section-name` which uses `targetRefs` with `sectionName` to attach to a rule within the HTTPRoute and has the next highest priority.
1. `policy-without-section-name` which uses `targetRefs` without `sectionName` to attach to the HTTPRoute and has the lowest priority.

The merging of these policies in priority order results in the following:
1. `cors` policy is derived from the `extensionref-policy` and is ignored in `policy-with-section-name` since it is lower in priority.
1. `transformation` policy is derived from the `policy-with-section-name` and is ignored in `policy-without-section-name` since it is lower in priority.
1. `rateLimit` policy is derived from the `policy-without-section-name`.

### Test Plan

[Translator unit tests](/translator/gateway/gateway_translator_test.go) are suitable to test policy priority and merging and have reasonable coverage for `TrafficPolicies` with different attachment forms.

## Alternatives

N/a

## Open Questions

Plugins that do not implement policy merging must ensure that lower priority policies do not override higher priority policies. This is a work in progress.