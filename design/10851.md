<!--
**Note:** When your Enhancement Proposal (EP) is complete, all of these comment blocks should be removed.

This template is inspired by the Kubernetes Enhancement Proposal (KEP) template: https://github.com/kubernetes/enhancements/blob/master/keps/sig-architecture/0000-kep-process/README.md


Just because a EP is merged does not mean it is complete or approved. Any EP
marked as `provisional` is a working document and subject to change. You can
denote sections that are under active debate as follows:

```
<<[UNRESOLVED optional short context or usernames ]>>
- Is listener level really the right attatchment level
- Should all auth be bundled
<<[/UNRESOLVED]>>
```

When editing EPS, aim for tightly-scoped, single-topic PRs to keep discussions
focused. If you disagree with what is already in a document, open a new PR
with suggested changes.

One EP corresponds to one "feature" or "enhancement" for its whole lifecycle. Once a feature has become
"implemented", major changes should get new EPs.
-->
# EP-[10851]: Extension Ordering

Status: Partially accepted


* Issue: [#10851](https://github.com/kgateway-dev/kgateway/issues/10851)


## Background 
Gateways will often provide extension mechanisms which provide a great deal of flexibility. 
These class of extensions often involve loading custom code or reaching out to an external service. 

In general debugging / robustness is helped by having strict ordering of extensions but in my opinion there are certain classes of externalized solutions that should be able to be slotted into the well known order of operations.

In kgateway's case this is especially true for the envoy filters like [extproc](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/ext_proc_filter), [extauth](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/ext_authz_filter) and [lua](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/lua_filter.html) filters.

While trying to add extauth and extproc alot of discussion occured which made breaking this out for posterity make some amount of sense.
Please see the[ ExtAuth PR for more context](https://github.com/kgateway-dev/kgateway/pull/10819)


<!-- 
provide a brief overview of the feature/enhancement, including relevant background information, origin, and sponsors. 
Highlight the primary purpose and how it fits within the broader ecosystem.

Include Motivation, concise overview of goals, challenges, and trade-offs.

-->

## Motivation

<!--
This section is for explicitly listing the motivation, goals, and non-goals of
this EP. Describe why the change is important and the benefits to users. The
motivation section can optionally provide links to [experience reports] to
demonstrate the interest in a EP within the wider Kubernetes community.

[experience reports]: https://github.com/golang/go/wiki/ExperienceReports
-->

### Goals
* A way to specify where in the general order to inject certain filters which we cant be opinionated on
* A way to make multiple implementations of the same extension determinable. ie having 2 instances of extproc added to the filter chain
* Unblock all extensions that rely on flexible ordering
* Not require a stage to be set and fall back on kgateway maintainers opinionated location for the extension. e.g. ExtAuth being placed during the authz stage.



### Non-Goals 
* Support for all policies, the more policies that have this functionality the more unintended side effects or bugs may be surfaced.

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

## Implementation Details



### Design Considerations

The API is designed with the following key considerations:
1. **Coarse grained control**: Users shouldnt have to worry about all the possible filters in their envoy implementation to know where to inject their extension.
2. **Minimal configuration**: Setting filter stage should be a really advanced use case and so 9 times out of ten should be empty and that experience should be optimized for 
3. **Encapsulate extension points**: Perhaps a controversial stance but to minimize places one needs to look to understand what is essentially an injected external source we propose to comingle the datasource and the filter stage info to represent a singular entry in the filter chain. Since most uses of extension ordering are non-standard use cases we propose commingling the endpoint/datasource information with filter stage. By doing this we can keep implementation specific guard rails right on the Extension

### API Changes
Add a new Policy called GatewayExtension. 
Note that this is the current view of what the api may look like but Placement should not be implemented until another pr updates and accepts this EP.
```yaml
// GatewayExtensionSpec contains staging information per design/10851.md
// +kubebuilder:validation:XValidation:message="ExtAuth must be set when type is ExtAuth",rule="self.type != 'ExtAuth' || has(self.extAuth)"
// +kubebuilder:validation:XValidation:message="ExtProc must be set when type is ExtProc",rule="self.type != 'ExtProc' || has(self.extProc)"
// +kubebuilder:validation:XValidation:message="ExtAuth must not be set when type is not ExtAuth",rule="self.type == 'ExtAuth' || !has(self.extAuth)"
// +kubebuilder:validation:XValidation:message="ExtProc must not be set when type is not ExtProc",rule="self.type == 'ExtProc' || !has(self.extProc)"
type GatewayExtensionSpec struct {
  // Type indicates the type of the GatewayPolicy to be used.
  // +unionDiscriminator
  // +kubebuilder:validation:Enum=ExtAuth;ExtProc
  // +kubebuilder:validation:Required
  Type GatewayExtensionType `json:"type"`

  // Placement configuration for where this extension should be placed in the filter chain.
  // If not specified, the extension will be placed based on the type of the extension.
  // For example Exauth will be place in the in AuthZStage by default.
  // +optional
  Placement Placement `json:"placement"`

  // ExtAuth configuration for ExtAuth extension type.
  // +optional
  ExtAuth *ExtAuth `json:"extAuth,omitempty"`

  // ExtProc configuration for ExtProc extension type.
  // +optional
  ExtProc *ExtProc `json:"extProc,omitempty"`
}

// Placement defines the configuration for where this should be placed in the filter chain.
type Placement struct {
  // Name of the filter stage where the  should be placed.
  // +kubebuilder:validation:Required
  Name FilterStageName `json:"name"`

  // Priority determines the relative order of s within the same stage.
  // Lower priorities are processed first.
  // In general ordering within a stage is considered not important.
  // +optional
  // +kubebuilder:validation:Minimum=-10
  // +kubebuilder:validation:Maximum=10
  Priority *int32 `json:"priority,omitempty"`
}
// ExtAuth defines the configuration for an ExtAuth source .
type ExtAuth struct {
  // BackendRef references the backend service that will handle the authentication.
  // +kubebuilder:validation:Required
  BackendRef *gwv1.BackendRef `json:"backendRef"`
}
```


Which would look like

```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  name: basic-extauth
spec:
  type: ExtAuth
  extauth:
  - backendRef:
    - name: custom-auth.svc
      port: 50051
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  name: basic-extauth-2
spec:
  type: ExtAuth
  extauth:
  - backendRef:
    - name: custom-auth.svc
      port: 50051
  placement:
    stage: RateLimit # note we need to think more on the stages we expose
    priority: 1
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  name: basic-ext-proc
spec:
  type: ExtProc
  extproc:
  - backendRef:
    - name: custom-extproc.svc
      port: 50051
  placement:
    stage: Router
    priority: -1
```

This would be a oneof with the types of extensions and then a general placement stanza that details where to slot the filter in.


### Reporting
Providers should have a status based on whether they have a valid references in the provider specific sections

## Alternatives
This proposal attempts to set the basic way to configure extensions which have no well defined location.

## Open Questions
Should filter staging information be comingled with the datasources for external providers?
Should this truly be externalprovider or is there a cleaner name.

