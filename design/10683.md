<!--
**Note:** When your Enhancement Proposal (EP) is complete, all of these comment blocks should be removed.

This template is inspired by the Kubernetes Enhancement Proposal (KEP) template: https://github.com/kubernetes/enhancements/blob/master/keps/sig-architecture/0000-kep-process/README.md


Just because a EP is merged does not mean it is complete or approved. Any EP
marked as `provisional` is a working document and subject to change. You can
denote sections that are under active debate as follows:

```
<<[UNRESOLVED optional short context or usernames ]>>
- Is listener level really the right attatchment level
- Should all auth be bundled
<<[/UNRESOLVED]>>
```

When editing EPS, aim for tightly-scoped, single-topic PRs to keep discussions
focused. If you disagree with what is already in a document, open a new PR
with suggested changes.

One EP corresponds to one "feature" or "enhancement" for its whole lifecycle. Once a feature has become
"implemented", major changes should get new EPs.
-->
# EP-[10683]: BYO Extauth



* Issue: [#10683](https://github.com/kgateway-dev/kgateway/issues/10683)


## Background
External authorization is a key [security](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/security/ext_authz_filter) and extensibility mechanism in Envoy and kgateway should support configuration for it. In this document we will focus purely on the http level extauth as it has more granular control and information than the network level filter.
The previous project (gloo) had 2 main configuration paths the first which was a bring your own [customauth](https://docs.solo.io/gloo-edge/latest/guides/security/auth/custom_auth/) and the other was a more integrated variant that was purely enterprise.

While we know that applying route configuration and pointing at an Extauth server is table stakes such as is seen in [Istio](https://istio.io/latest/docs/tasks/security/authorization/authz-custom/) it makes sense that we also keep in mind whether we want to support extensible setups which could have more or less tight control plane control of the external servers. In particular a question we will try to solve here is how we want to integrate with the deployer and how to handle the opt in or out aspect.

Before reading any further it is also nessecary to see the current state of Gateway APIs [gep-1494 ](https://github.com/kubernetes-sigs/gateway-api/issues/1494).
At the time of writing there is explicitly not a defined implementation but it does provide some good guidelines on methodology and levels of configuration as seen in the [goals section](https://github.com/kubernetes-sigs/gateway-api/blob/2b0b9ceff08f1ac7899efa25657480d3196b1185/geps/gep-1494/index.md?plain=1#L14-L24).

<!--
provide a brief overview of the feature/enhancement, including relevant background information, origin, and sponsors.
Highlight the primary purpose and how it fits within the broader ecosystem.

Include Motivation, concise overview of goals, challenges, and trade-offs.

-->

## Motivation

<!--
This section is for explicitly listing the motivation, goals, and non-goals of
this EP. Describe why the change is important and the benefits to users. The
motivation section can optionally provide links to [experience reports] to
demonstrate the interest in a EP within the wider Kubernetes community.

[experience reports]: https://github.com/golang/go/wiki/ExperienceReports
-->

### Goals
* Listener level configuration to point at the desired authorization server
* Listener level configuration for as many of the options as are non-controversial in [listener level envoy configuration](https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/filters/http/ext_authz/v3/ext_authz.proto#envoy-v3-api-msg-extensions-filters-http-ext-authz-v3-extauthz)
* Set sensible defaults at the listener level for external authorization
* Set a baseline for how we may want to configure Auth* type policy

<!--

List the specific goals of the EP. What is it trying to achieve? How will we
know that this has succeeded?

Include specific, actionable outcomes. Ensure that the goals focus on the scope of
the proposed feature.
-->


### Non-Goals
* Automation of external authenication server's configured authentication information
* Creation of production ready kgateway default external authentication server
* Enforce that kgateway controls the life cycle of the external authorization server(s)
* Route level configuration for all options in [Envoy CheckSettings ](https://github.com/envoyproxy/envoy/blob/1a8c2f8e2c6e6cc92cd8e113c73b715c403cb3fd/api/envoy/extensions/filters/http/ext_authz/v3/ext_authz.proto#L478)

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

## Implementation Details

#### Sensible listener settings
It seems that pretty for most users the defaults set in envoy work fairly well with a few exceptions.
Given common workflows the following exceptions seem reasonable.
* MetadataContextNamespaces should have jwt set by default to make the configuration that needs jwt infromation cleaner
* http/grpc services should not need full configuration and should have sensible defaults such as for their timeouts
* There is no reason to expose the api version as the the auto option defaults to v3 which has been standard for years. If desired this can be added later if legacy implementations require it

### Design Considerations

The API is designed with the following key considerations:
1. **Top down enforcement**: Authentication details shouldnt have to vary wildly between routes in most kgateway deployments. In general if there are seperate concerns then those concerns should be split into seperate Listeners which provides better isolation and performance. This means that the in this design we want to keep the scope of route level options to a slim profile.
2. **Extensiblity**: While kgateway should not be perscriptive around how the external server should derive its information we should consider that for ease of delivery future api may want to allow for extension hooks or recognize that projects may want to register new CRs that will may want to reference the authconfig. Additionally we should be ready for a future extension to allow for multiple exclusive extauth servers on a single gateway.
3. **Security by default**: While security can be an opt in if authorization is desired in an environment it likely is better to have all routes have to opt out explicitly. This mirrors the current thought in the upstream [gep](https://github.com/kubernetes-sigs/gateway-api/blob/2b0b9ceff08f1ac7899efa25657480d3196b1185/geps/gep-1494/index.md?plain=1#L24).

### API Changes
Requires gateway extension policy [implementation](https://github.com/kgateway-dev/kgateway/issues/10851)
```yaml

// ExtAuthEnablement determines the enabled state of the ExtAuth filter.
// +kubebuilder:validation:Enum=DisableAll
type ExtAuthEnablement string

const (
  // ExtAuthDisableAll disables the ExtAuth filter for this route.
  ExtAuthDisableAll ExtAuthEnablement = "DisableAll"

)

// ExtAuthPolicy configures external authentication for a route.
// This policy will determine the ext auth server to use and how to  talk to it.
// Note that most of these fields are passed along as is to Envoy.
// For more details on particular fields please see the Envoy ExtAuth documentation.
// https://raw.githubusercontent.com/envoyproxy/envoy/f910f4abea24904aff04ec33a00147184ea7cffa/api/envoy/extensions/filters/http/ext_authz/v3/ext_authz.proto
type ExtAuthPolicy struct {
  // Extension references the GatewayExtension that should be used for authentication.
  // +optional
  Extension *gwv1.LocalObjectReference `json:"extension,omitempty"`

  // Enablement determines the enabled state of the ExtAuth filter.
  // When set to "DisableAll", the filter is disabled for this route.
  // When empty, the filter is enabled unless another policy explicitly disables it.
  // +optional
  Enablement ExtAuthEnabled `json:"enablement,omitempty"`

  // WithRequestBody allows the request body to be buffered and sent to the authorization service.
  // Warning buffering has implications for streaming and therefore performance.
  // +optional
  WithRequestBody *BufferSettings `json:"withRequestBody,omitempty"`


  // MetadataContextNamespaces specifies metadata namespaces to pass to the authorization service.
  // Default to allowing jwt info if processing for jwt is configured.
  // +optional
  // +listType=set
  // +kubebuilder:default={"jwt"}
  MetadataContextNamespaces []string `json:"metadataContextNamespaces,omitempty"`
}
```

### API Examples
Some fields of which proboably arent needed in a first pass for extauth and are noted in the examples as such.
```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  name: basic-extauth
spec:
  type: Extauth
  extauth:
    backendRef:
      - name: custom-auth.svc
        port: 50051
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: GatewayExtension
metadata:
  name: basic-extauth-2
spec:
  type: Extauth
  extauth:
  - backendRef:
    - name: custom-auth.svc
      port: 50051
---
## Blanket apply on anything on the gateway
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: gw-policy
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: gw
  extauth:
    extension:
      name: basic-extauth
---
## Opt out of the top level gateway configuration
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: myroute-noauth
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: app-team-route
  extauth:
    - disallowedHeaders:
      - x-authenticated
    metadataContextNamespaces: ["jwt", "custom"]
    enablement: DisableAll  # note we propose just having this option to begin with so if its present you can enable health check routes easily
---
## On a route that is not part of the gw gateway opt in
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: myroute-on-a-different-gw
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: app-team-route
  extauth:
    extension:
      name: basic-extauth-2
```



### Plugin
For this initial pass we should implement GatewayExtension and the basic reference on TrafficPolicy. We may want to not implement route level disablement in the first pass.
Given the concerns for extensibility that we have some type of generalized GatewayExtension type for external destinations such as extauth and tracing.


### Deployer
Will not change in this first pass.

### Reporting
For now this will be purely based on envoy metrics for the extauth server.
### Test Plan
* Add a simple e2e test with an instance of a simple extauth compliant server that requires a particular header.
* Have 2 routes, one with auth one without
* Request with correct header and without on protected route
* Request with correct header and without on unprotected route
* Change the ext auth server reference to a non-existant destination
* Rerun the above requests
<!--
    Define the testing strategy for the feature.
    Include unit, integration, and end-to-end (e2e) tests.
    Specify any additional frameworks or tools required for testing.
-->

## Alternatives
This proposal attempts to set the ground work for auth policy APIs and how they will be broken out.
Several auth implementations provide for both authn and authz so while we could split them out like [how Istio does](https://istio.io/latest/docs/tasks/security/authentication/jwt-route/) there are an increasing number of filters such as [ApiKeys](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/api_key_auth_filter) which comingle the concepts.
Another alternative is a more monolithic approach like how [authconfig for the previous project ](https://docs.solo.io/gloo-edge/main/reference/api/github.com/solo-io/gloo/projects/gloo/api/v1/enterprise/options/extauth/v1/extauth-internal.proto.sk/#config)(gloo) contained all info for every auth* type of policy that may be handled by the extauth server.

An example of an alternative API could be setting it on the listenerOption
```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: ListenerOption
metadata:
  name: extauthserver
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: gw
  options:
    extauth:
      - name: mycustom
        disallowedHeaders:
          - x-authenticated
        emitFilterStateStats: true
        backendRef:
          - name: custom-auth.svc
            port: 50051
```

<!--
Highlight potential challenges or trade-offs.
-->

## Open Questions
Should there be support for multiple extauth servers per listener
* Would require more route augmentation such as setting metadata to toggle extauths
* Would enable cases where extauth is both a custom server and an integrator such as WAF
* Would enable monolithic deploys to have multiple external auth servers
* Any desired use case can be solved in logic in a single external server
Should we actually start at the Route level
* Makes granular settings easier to add later
* Keeps all logic in one place
* Makes new route adoption worse
* Less performant on the dataplane
<!--
Include any unresolved questions or areas requiring feedback.
-->
