# EP-11303: Define Naming Convention for New Metrics

* Issue: [#11303](https://github.com/kgateway-dev/kgateway/issues/11303)

## Background

Having a consistent naming convention can make it easy to manage OTel pipelines and to share/simplify processors between them. The goal of this ticket is to come up with the convention itself.

## Motivation

A consistent naming convention is crucial for the effective management and integration of OTel pipelines. It simplifies the sharing and processing of data across different subsystems.

### Goals

- Establish a clear and consistent naming convention for metrics.

### Non-Goals

- This proposal does not cover the implementation of the metrics themselves, only the naming convention.
- These conventions apply to metrics generated by kgateway directly, and does not cover metrics generated by a proxy.

## Implementation Details

We will use the naming convention of starting metric names with the core namespace of `kgateway_`. The core namespace should be followed by the subsytem name.

Use [Prometheus' Metric and label naming guide](https://prometheus.io/docs/practices/naming/) for general naming guidance.

Metrics which currently exist and do not conform the naming convention will be left in place until the next major version. A parallel metric will be created with a conforming name in an earlier release.

It is expected that new metrics will be implemented by a framework which will enforce these naming conventions.

### Test Plan
Not Applicable. 

## Alternatives
- Continue with the current ad-hoc naming practices.

## Open Questions

