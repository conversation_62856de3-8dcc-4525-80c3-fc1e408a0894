# EP-10943: Route Delegation

* Issue: [#10943](https://github.com/kgateway-dev/kgateway/issues/10943)

## Background

Route delegation is a feature that allows HTTPRoutes to delegate routing configuration to other HTTPRoutes. Benefits include:
- Improving maintanability by splitting up large routing configurations into multiple smaller objects.
- Being able to enforce a set of policies at the top level, but delegating custom routing decisions and policies to different teams.
- Allowing teams to make changes to their routing configuration without accessing or impacting routing owned by other teams.
- Reducing duplication by being able to reuse the same routing configuration in multiple places.

## Motivation

### Goals

The purpose of this EP is to define the current kgateway route delegation feature, including the API and implementation, at the time of this writing. This includes topics such as:
- [Path matching](#path-matching)
- [Delegating by wildcard name](#delegating-by-wildcard-name)
- [Delegating by label selector](#delegating-by-label-selector)
- [Multiple parents or multiple children](#multiple-parents-or-multiple-children)
- [Multiple levels of delegation](#multiple-levels-of-delegation)
- [Restricting allowed parents using parentRefs](#restricting-allowed-parents-using-parentrefs)
- [Header, query parameter, and method matching](#header-query-parameter-and-method-matching)
- [Matcher inheritance](#matcher-inheritance)
- [Delegation of native HTTPRouteRule fields](#delegation-of-native-httprouterule-fields)
- [TrafficPolicy inheritance and merging](#trafficpolicy-inheritance-and-merging)

### Non-Goals

The EP will not address or propose new features / enhancements that are not already implemented at the time of this writing, such as:
- Supporting delegation for route types other than HTTPRoutes (e.g. TCPRoute, TLSRoutes)
- Supporting policy merging for non-TrafficPolicy plugins

## API and examples

Route delegation is configured through the `backendRefs` field in HTTPRoute rules, where a backend reference can point to other HTTPRoutes.

### Path matching

In the simplest case, a parent route can delegate configuration for a specific path prefix to a child by reference. If the child route's namespace is not specified, it defaults to the parent route's namespace.

The parent and child matchers must adhere to the following rules (unless [matcher inheritance](#matcher-inheritance) is used): 
- The parent path matcher must be of type `PathPrefix`.
- The child's path matcher value must contain the parent's path matcher value as a prefix.

In the following example, the route `parent` delegates to the route `child-route` in namespace `team1` for paths starting with `/team1`:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: child-route
      namespace: team1
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route
  namespace: team1
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1/anything
    backendRefs:
    - name: team1-svc
      port: 8080
```

### Delegating by wildcard name

The child route name can also be a wildcard. In this case, the parent will delegate `/team1` to all routes in the `team1` namespace (i.e. both `child-route1` and `child-route2`):

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team1
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route1
  namespace: team1
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1/foo
    backendRefs:
    - name: svc1
      port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route2
  namespace: team1
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1/bar
    backendRefs:
    - name: svc2
      port: 8080
```

### Delegating by label selector

Rather than using an HTTPRoute as a backendRef, a parent can also delegate to child routes with a label selector by adding the custom group/kind `delegation.kgateway.dev/label` as a backendRef:
```yaml
- group: delegation.kgateway.dev
  kind: label
  name: <label value>
  namespace: ""|<namespace>|"all" # optional
```

This will delegate to child HTTPRoutes in the given `namespace` that have a label with name=`delegation.kgateway.dev/label` and value=`<label value>`.

The `namespace` is optional, and is used to restrict the namespaces in which to search for matching child routes. If not specified, it defaults to the parent route's namespace. To delegate to matching children in _all_ namespaces, a special value `all` can be used as the namespace. In case there already exists an actual namespace `all`, to avoid conflicts the special wildcard value can be overridden by setting the environment variable `DELEGATION_WILDCARD_NAMESPACE` to a different value in the control plane pod.

The example below shows a parent route that delegates to child routes using various label selectors:
- `/infra` delegates to children with label `delegation.kgateway.dev/label: infra-routes` in the `infra` namespace.
- `/a` delegates to children with label `delegation.kgateway.dev/label: a-routes` in the `a` namespace.
- `/all` delegates to children with label `delegation.kgateway.dev/label: all-routes` in any namespace.

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /infra
    backendRefs:
    - group: delegation.kgateway.dev
      kind: label
      name: infra-routes
      # no namespace; defaults to parent route's namespace (infra)
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - group: delegation.kgateway.dev
      kind: label
      name: a-routes
      namespace: a
  - matches:
    - path:
        type: PathPrefix
        value: /all
    backendRefs:
    - group: delegation.kgateway.dev
      kind: label
      name: all-routes
      namespace: all # wildcard namespace
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: infra-child
  namespace: infra
  labels:
    delegation.kgateway.dev/label: infra-routes
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /infra/1
    backendRefs:
    - name: example-svc
      port: 80
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-a
  namespace: a
  labels:
    delegation.kgateway.dev/label: a-routes
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/1
    backendRefs:
    - name: svc-a
      port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-b
  namespace: b
  labels:
    delegation.kgateway.dev/label: all-routes
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /all/b
    backendRefs:
    - name: svc-b
      port: 8080
```

### Multiple parents or multiple children

A parent route can delegate different paths to different children or namespaces. Likewise, a given child route can have multiple parents. Here, there are two parent routes (hostname `foo.com` and hostname `bar.com`) which delegate to child routes in the `team1` and `team2` namespaces. Both `parent-foo` and `parent-bar` have children `child-route1` and `child-route2`:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-foo
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "foo.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team1
  - matches:
    - path:
        type: PathPrefix
        value: /team2
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team2
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-bar
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "bar.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team1
  - matches:
    - path:
        type: PathPrefix
        value: /team2
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team2
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route1
  namespace: team1
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1/foo
    backendRefs:
    - name: svc1
      port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route2
  namespace: team2
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team2/bar
    backendRefs:
    - name: svc2
      port: 8080
```

### Multiple levels of delegation

Child routes can delegate further to other child routes, creating a hierarchy of routes. In the following example, `parent` delegates to `route-a` which delegates to `route-a-b`:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-a
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/b
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: route-a-b
      namespace: a-b
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-a-b
  namespace: a-b
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/b/1
    backendRefs:
    - name: svc-a-b
      port: 8080
```

### Restricting allowed parents using parentRefs

Children may restrict which parent routes are allowed to delegate to it, by specifying `parentRefs`. If a child doesn't specify `parentRefs`, then any parent is allowed to delegate to it.

Note that `parentRefs` on the child alone will not enable delegation; the parent still needs to enable delegation to the child, via the `backendRefs` field.

In the following example, although both `parent-foo` and `parent-bar` delegate to `child-route`, `child-route` has restricted the allowed parents to only `parent-foo` via the `parentRefs` field:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-foo
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "foo.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team1
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-bar
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "bar.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: team1
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route
  namespace: team1
spec:
  parentRefs:
  - name: parent-foo
    namespace: infra
    group: gateway.networking.k8s.io
    kind: HTTPRoute
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /team1/anything
    backendRefs:
    - name: team1-svc
      port: 8080
```

### Header, query parameter, and method matching

If a parent route specifies header, query parameter, or method matchers, the child route must adhere to the following rules in order to be a valid delegatee of the parent (unless [matcher inheritance](#matcher-inheritance) is used):
- The child header matchers must be a superset of the parent header matchers.
- The child query param matchers must be a superset of the parent query param matchers.
- If the parent method matcher is set, the child's method matcher value must be equal to the parent method matcher value.

In the following example:
- The parent matcher contains header `header1=val1` and query parameter `query1=val1`.
- Since `route1` specifies a matcher containing a superset of these values, it is a valid delegatee of `parent`.
- Since `route2` does not contain `header1=val1` or `query1=val1`, it is not a valid delegatee of `parent` and is dropped from the output.

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 80
  - matches:
    - path:
        type: PathPrefix
        value: /a
      headers:
      - type: Exact
        name: header1
        value: val1
      queryParams:
      - type: Exact
        name: query1
        value: val1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route1
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: Exact
        value: /a/1
      headers:
      - type: Exact
        name: header1
        value: val1
      - type: Exact
        name: headerX
        value: valX
      queryParams:
      - type: Exact
        name: query1
        value: val1
      - type: Exact
        name: queryX
        value: valX
    backendRefs:
    - name: svc1
      port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route2
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: Exact
        value: /a/2
      headers:
      - type: Exact
        name: headerX
        value: valX
      queryParams:
      - type: Exact
        name: queryX
        value: valX
    backendRefs:
    - name: svc2
      port: 8080
```

### Matcher inheritance

When a child route has the annotation `delegation.kgateway.dev/inherit-parent-matcher: true`, it does not need to contain a superset of its parent's matchers. Instead, the parent and child route matchers will be merged as follows:
- The resulting path consists of parent path + child path.
- The resulting headers consist of the combined headers from parent and child, with parent header taking precedence on any name conflicts.
- The resulting query parameters consist of the combined query parameters from parent and child, with parent query params taking precedence on any name conflicts.
- The child inherits the parent's method if specified; otherwise the child retains its own method.

In the following example, the parent and child route matchers are merged, and the resulting route matcher contains:
- path: `/a/foo`
- method: `GET`
- headers: `header1=val1`, `headerA=valA`
- queryParams: `query1=val1`, `queryA=valA`

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
      method: GET
      headers:
      - type: Exact
        name: header1
        value: val1
      queryParams:
      - type: Exact
        name: query1
        value: val1
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child
  namespace: a
  annotations:
    delegation.kgateway.dev/inherit-parent-matcher: "true"
spec:
  rules:
  - matches:
    - path:
        type: Exact
        value: /foo
      method: PUT
      headers:
      - type: Exact
        name: headerA
        value: valA
      queryParams:
      - type: Exact
        name: queryA
        value: valA
    backendRefs:
    - name: svc-a
      port: 8080
```

### Delegation of native HTTPRouteRule fields

Delegation of native HTTPRouteRule fields such as `timeouts` and `retry` is supported. Any value specified on a child route will override the value specified on the parent. If no value is specified on a child route, it will inherit the value from its parent.

In the following example:
- `route-a1` does not specify `timeouts` or `retry`, so it will inherit those values from `parent`
- `route-a2` specifies both `timeouts` and `retry`, so those will override the values from `parent`

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
    timeouts:
      request: 5s
    retry:
      attempts: 3
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-a1
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/1
    backendRefs:
    - name: svc-a
      port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: route-a2
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/2
    backendRefs:
    - name: svc-a
      port: 8080
    timeouts:
      request: 10s
    retry:
      attempts: 5
      codes:
        - 503
      backoff: 1s
```

### TrafficPolicy inheritance and merging

Child routes can inherit TrafficPolicies that are attached to their ancestors. If a TrafficPolicy is attached to both an ancestor and a child, the TrafficPolicies will be merged. To merge policies, we need to consider both the hierarchy of routes (i.e. parent vs. child), and the ordering of attachment points (i.e. targetRef, extensionRef) within each level of the hierarchy:
- By default, policies attached to a parent take precedence over policies attached to a child. However, if the parent route sets the annotation `delegation.kgateway.dev/inherited-policy-priority: PreferChild`, then it allows the child policy to override fields on the parent policy.
- Within a route, policy attachment has the following priority, from highest to lowest:
    - Policy attached to HTTPRouteRule via `extensionRef`
    - Policy attached to HTTPRouteRule via `targetRef`
    - Policy attached to HTTPRoute via `targetRef`

Merging is currently done as a shallow merge of the top-level fields (e.g. `transformation`, `extProc`, `rateLimit`) of the TrafficPolicy. For example, if multiple TrafficPolicies specify a `transformation`, the resulting policy only consists of the transformation with highest priority; we do not merge the transformations together.

In the following example, there are 2 parents (`parent-foo` and `parent-bar`) which both delegate to child `child-route`. A TrafficPolicy with both `transformation` and `rateLimit` is attached to both parents, and a TrafficPolicy with a different `transformation` is attached to the child. The parent `parent-bar` allows child overrides, whereas `parent-foo` does not. The result is that:
- Route `/a/foo` for domain `foo.com` will inherit both the transformation and rateLimit policy from the parent.
- Route `/a/foo` for domain `bar.com` will inherit the rateLimit from the parent, but use the transformation specified by the child's policy.

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-foo
  namespace: infra
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "foo.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: parent-bar
  namespace: infra
  annotations:
    delegation.kgateway.dev/inherited-policy-priority: PreferChild
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "bar.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: "*"
      namespace: a
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: child-route
  namespace: a
spec:
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a/foo
    backendRefs:
    - name: a-svc
      port: 8080
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: parent-policy
  namespace: infra
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: parent-foo
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: parent-bar
  transformation:
    request:
      set:
        - name: x-foo-req
          value: abc
  rateLimit:
    local:
      tokenBucket:
        maxTokens: 1
        tokensPerFill: 2
        fillInterval: 3s
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: child-policy
  namespace: a
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: child-route
  transformation:
    response:
      set:
        - name: a-bar-resp
          value: def
```

## Delegation rules

There are several rules that must be followed when using delegation. Any route that violates one of these rules will be removed from the translated output.
- Child HTTPRoutes may not set the `spec.hostnames` field, as the hostnames are inherited from the parent.
- Cycles are not allowed (i.e. a route cannot appear in a delegation chain multiple times).
- If child sets the `parentRefs` field, the parentRefs must include the parent, as described in [Restricting allowed parents using parentRefs](#restricting-allowed-parents-using-parentrefs) above.
- By default (without [matcher inheritance](#matcher-inheritance)), the [path matching](#path-matching) and [header, query parameter, and method matching](#header-query-parameter-and-method-matching) rules must be adhered to.

## Implementation Details

### Reconciling route tree

When the kgateway controller [starts](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/controller/start.go#L92), as part of initializing the KRT collections, it converts any HTTPRoutes into HttpRouteIRs/HttpRouteRuleIRs. In [transformHttpRoute](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/krtcollections/policy.go#L740), the list of all policies targeting that route/rule are fetched and stored on the IR.

The building of the route tree is done during the first phase of kgateway translation, which converts user-facing configuration (Gateways, HTTPRoutes, TrafficPolicies) into a GatewayIR (intermediate representation). The entrypoint to this translation is the [Translate](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/gateway/gateway_translator.go#L30) function in `gateway_translator.go`.

First, [GetRoutesForGateway](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/query/httproute.go#L312) creates a list of RouteInfos for each listener. Each RouteInfo contains the route tree consisting of a route and all its descendant routes. To build the route tree, [getDelegatedChildren](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/query/httproute.go#L194) is recursively called to get the delegated HTTPRoute backends for each route. This is where we check for cycles, and filter out child routes if they disallow attaching to the parent (via parentRefs).

These RouteInfos are then used to build the HttpRouteRuleMatchIRs which go on the eventual GatewayIR. This is initiated from [TranslateGatewayHTTPRouteRules](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/httproute/gateway_http_route_translator.go#L25). The delegated routes of a RouteInfo are recursively visited to create a flattened list of HttpRouteRuleMatchIRs. While building this list, any delegating parent HttpRouteRuleMatchIRs (along with their attached policies) are attached to the child, to be used later when applying policies to the final Envoy routes.

For each child route that is visited, [filterDelegatedChildren](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/httproute/delegation_helpers.go#L34) is called. This has most of the logic to check the validity of parent/child relationships and to do any necesssary matcher merging, as described in [Delegation rules](#delegation-rules) above.

### Applying policies to routes

During the second phase of translation, the IRs are converted to xDS configuration. The entrypoint to this translation is the [Translate](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/irtranslator/gateway.go#L31) function in `irtranslator/gateway.go`. This is when each of the attached policies, including those inherited through delegation, are collected and applied to the route to form the final envoy configuration. See [runRoutePlugins](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/irtranslator/route.go#L207) in `irtranslator/route.go` for the route translation.

If the plugin implements `MergePolicies`, then policies will first be merged and `ApplyForRoute` will be called once with the merged policy. Currently only the TrafficPolicy plugin implements `MergePolicies`. For other plugins that don't support merging, `ApplyForRoute` will be called once per policy, in order from lowest to highest priority, as detailed in [TrafficPolicy inheritance and merging](#trafficpolicy-inheritance-and-merging) above.

## Test Plan

We currently have tests that cover a broad range of route delegation scenarios, primarily in the following suites:
- [Translation unit tests](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/internal/kgateway/translator/gateway/gateway_translator_test.go#L398), which assert that given some input (Gateways, HTTPRoutes, Services) we get the correct translated output (i.e. Envoy config)
- [End-to-end tests](https://github.com/kgateway-dev/kgateway/blob/ec3f9909932254b374049c5284762f958c634f80/test/kubernetes/e2e/features/route_delegation/suite.go), which contain various route delegation configurations and assert the expected statuses and curl responses on the routes.

## Alternatives

N/A

## Open Questions

N/A