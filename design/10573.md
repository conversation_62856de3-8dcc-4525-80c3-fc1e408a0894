# EP-10573: AWS Lambda Integration

* Issue: [#10573](https://github.com/kgateway-dev/kgateway/issues/10573)

## Background

The Gateway API has become a standard for routing traffic within Kubernetes clusters, primarily supporting native resources like `HTTPRoute` and `TCPRoute` to direct traffic to Kubernetes Services. However, organizations adopting serverless architectures increasingly rely on AWS Lambda as a compute backend, which the Gateway API does not natively support.

Kgateway fills this gap through its custom `Backend` API, designed as an extension point to support a broad range of destinations, including static hosts and AWS Lambda functions. This enhancement introduces native AWS Lambda integration into kgateway's routing capabilities, enabling users to maintain a consistent Gateway API experience while seamlessly integrating serverless backends.

Furthermore, this proposal moves away from the previous approach that relied on a custom [envoy-gloo](https://github.com/solo-io/envoy-gloo) lambda filter and the UX for the synthetic `Parameter` GVK. Instead, it leverages Envoy's native [AWS Lambda filter](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/aws_lambda_filter#configuration-as-an-upstream-http-filter) and encodes the lambda function ARN in the `Backend` API. While this approach requires users to define N Backends for each lambda function, it provides a more flexible and maintainable solution that can be extended to support other AWS services in the future.

## Motivation

Organizations adopting serverless architectures face unique challenges when integrating Gateway API implementations. While the HTTPRoute API is excellent for routing to Kubernetes services, it lacks built-in support for the nuances of AWS Lambda—including managing function versions, controlling invocation modes, and handling cross-account access.

This enhancement aims to deliver a unified and open API experience that supports both traditional Kubernetes services and serverless functions through the Backend API. Key motivations include:

- **Simplicity**: The Backend API provides a clear and intuitive way to define Lambda function backends, making it easy for users to integrate serverless workloads in a consistent manner.
- **Composability**: Developers can iterate on function versions and aliases without having to reconfigure underlying connection details.
- **Flexibility**: The explicit invocation type configuration allows users to control whether Lambda functions are invoked synchronously or asynchronously.
- **Security**: Security teams gain fine-grained control over AWS credential management through IRSA or static credentials.
- **Maintainability**: By leveraging Envoy's native Lambda support and clear API boundaries, the solution becomes more maintainable and scalable.

In summary, this enhancement extends the capabilities of kgateway to support AWS Lambda through a simple yet powerful Backend API, helping organizations adopt serverless technologies without compromising on the core benefits of the Gateway API.

### Goals

- Enable users to route traffic to AWS Lambda backends
- Design a foundational AWS service API that can be extended beyond Lambda
- Support for static credentials and IRSA (IAM Roles for Service Accounts) authentication
- Support for Lambda function qualifiers and versioning
- Support for Lambda function invocation mode (sync/async)
- Provide an extensible API design that other AWS services can build upon

### Non-Goals

- Support for other serverless platforms (e.g., Azure Functions, Google Cloud Functions)
- Support for role chaining to enable cross-account invocation
- Support for request or response transformations (this will be implemented by vendors)
- Support for other authentication methods beyond IRSA and static credentials
- Support for other AWS services (these can be added later using the foundational API)

## Implementation Details

This proposal is focused on extending the Backend API to support routing to AWS Lambda functions. Additionally, it proposes moving away from the custom envoy-gloo lambda filter and the synthetic `Parameter` GVK approach. The following sub-sections detail the API changes, example configurations, and dive into the lower-level details like the [plugin](#plugin) implementation.

### API Changes

### Design Considerations

The API is designed with the following key considerations:

1. **Service Extensibility**: While this proposal focuses on Lambda integration, the `Backend` AWS API is designed to be extensible to support other AWS services (e.g., S3, EventBridge, RDS) in the future. The clear separation between connection-level configuration and service-specific routing enables this flexibility.
2. **Request/Response Transformation**: The previous Gloo project had support for request/response transformations, such as wrapping requests or unwrapping responses from AWS API Gateway. While these transformations are not part of the core OSS API, this design delegates the responsibility of implementing these transformations to the vendors as they see fit.
3. **API Design**:
   - Using specific types over generic types (e.g. corev1.LocalObjectReference) for better type safety
   - Making configuration options explicit and well-documented
   - Providing clear validation rules via kubebuilder markers
   - Using enums instead of booleans for better clarity
4. **Authentication**:
   - Separating authentication configuration from service configuration
   - Supporting both IRSA (recommended) and static credentials
   - Designing for future authentication methods
   - Following security best practices for credential management
5. **Validation and Documentation**:
   - Using kubebuilder markers for validation
   - Providing clear documentation in godoc comments
   - Including examples in documentation
   - Making defaults and behaviors explicit

This approach ensures that while the core OSS functionality remains focused and maintainable, the architecture can support advanced features like API Gateway compatibility that many enterprise customers require.

### API Changes

The backend_policy_types.go file will be updated to add the new AWS Lambda-specific configuration:

```go
const (
	// BackendTypeAws is the type of backend that references an AWS service.
	BackendTypeAws = "aws"
	// BackendTypeStatic is the type of backend that references a static list of hosts.
	BackendTypeStatic = "static"
)

// BackendSpec defines the desired state of a Backend.
// +kubebuilder:validation:XValidation:message="Type field must match the backend configuration: aws requires Aws field, static requires Static field",rule="(self.type == 'aws' && has(self.aws) && !has(self.static)) || (self.type == 'static' && has(self.static) && !has(self.aws))"
type BackendSpec struct {
	// Type controls the type of backend.
	// +unionDiscriminator
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Enum=aws;static
	Type string `json:"type"`

	// AWS backend configuration. Allows for referencing AWS services.
	// +optional
	Aws *AWSBackendConfig `json:"aws,omitempty"`

	// Static backend configuration. Allows for referencing a list of hosts.
	// +optional
	Static *StaticBackendConfig `json:"static,omitempty"`
}

// AWSBackendConfig defines AWS-specific backend configuration.
type AWSBackendConfig struct {
	// Region is the AWS region.
	// +kubebuilder:validation:Required
	Region string `json:"region"`

	// AccountId is the AWS account ID.
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Pattern=^[0-9]{12}$
	AccountId string `json:"accountId"`

	// Auth specifies the authentication method to use.
	// +kubebuilder:validation:Required
	Auth AWSAuthConfig `json:"auth"`

	// Lambda-specific configuration.
	// +optional
	Lambda *AWSLambdaConfig `json:"lambda,omitempty"`
}

const (
	// AWSInvocationModeSync is the invocation mode for synchronous Lambda function invocations.
	AWSInvocationModeSync = "Sync"
	// AWSInvocationModeAsync is the invocation mode for asynchronous Lambda function invocations.
	AWSInvocationModeAsync = "Async"
)

// AWSLambdaConfig defines Lambda-specific configuration.
type AWSLambdaConfig struct {
	// FunctionName is the name or ARN of the Lambda function.
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:MaxLength=140
	FunctionName string `json:"functionName"`

	// Qualifier is the function version or alias.
	// +optional
	// +kubebuilder:validation:MaxLength=128
	Qualifier string `json:"qualifier,omitempty"`

	// InvocationType controls whether the function is invoked synchronously or asynchronously.
	// +optional
	// +kubebuilder:validation:Enum=Sync;Async
	InvocationType string `json:"invocationType,omitempty"`

	// EndpointURL is the URL to use for the Lambda service endpoint. Defaults
	// to the AWS Lambda endpoint when not provided. This is useful
	// for testing with a local lambda function.
	// +optional
	EndpointURL string `json:"endpointURL,omitempty"`
}

// AWSAuthConfig defines the authentication configuration for AWS services.
type AWSAuthConfig struct {
	// Type of authentication to use.
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Enum=irsa;secret
	Type string `json:"type"`

	// IRSA contains configuration for IAM Roles for Service Accounts.
	// Required if type is "irsa".
	// +optional
	IRSA *AWSAuthIRSAConfig `json:"irsa,omitempty"`

	// Secret contains static AWS credentials.
	// Required if type is "secret". Not recommended for production use.
	// +optional
	Secret *AWSAuthSecretConfig `json:"secret,omitempty"`
}

// AWSAuthIRSAConfig defines the configuration for IAM Roles for Service Accounts.
type AWSAuthIRSAConfig struct {
	// RoleARN is the ARN of the IAM role to assume.
	// +kubebuilder:validation:Required
	// +kubebuilder:validation:Pattern=^arn:aws:iam::\d{12}:role/[a-zA-Z0-9+=,.@_-]+$
	RoleARN string `json:"roleArn"`
}

// AWSAuthSecretConfig contains static AWS credentials.
type AWSAuthSecretConfig struct {
	// Secret specifies a secret containing AWS credentials.
	// +kubebuilder:validation:Required
	Secret AWSSecretReference `json:"secret"`
}

// AWSSecretReference specifies a secret containing AWS credentials.
type AWSSecretReference struct {
	// Name is the name of the secret.
	// +kubebuilder:validation:Required
	Name string `json:"name"`

	// Namespace is the namespace of the secret.
	// Defaults to the namespace of the Backend resource if not provided.
	// +optional
	Namespace string `json:"namespace,omitempty"`
}
```

### Configuration

Let's go through an example of how to configure an AWS Lambda function using the Backend API.

#### Basic Lambda Configuration

```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  name: my-lambda-backend
  namespace: kgateway-system
spec:
  type: aws
  aws:
    accountId: "************"
    region: us-west-2
    auth:
      type: irsa
      irsa:
        roleARN: arn:aws:iam::************:role/my-lambda-role
    lambda:
      functionName: my-lambda-function
      qualifier: prod
      invocationType: Sync
```

#### Using Static Credentials (Development Only)

```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  name: my-lambda-backend
  namespace: kgateway-system
spec:
  type: aws
  aws:
    accountId: "************"
    region: us-west-2
    auth:
      type: secret
      secret:
        name: aws-creds
        namespace: kgateway-system
    lambda:
      functionName: my-lambda-function
      invocationType: Async
```

#### LocalStack Testing Configuration

```yaml
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  name: my-lambda-backend
  namespace: kgateway-system
spec:
  type: aws
  aws:
    accountId: "************"
    region: us-east-1
    auth:
      type: secret
      secret:
        name: localstack-creds
        namespace: kgateway-system
    lambda:
      functionName: my-test-function
      endpointURL: "http://172.18.0.2:31566"
```

#### HTTPRoute Integration

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
  namespace: kgateway-system
spec:
  parentRefs:
  - name: http
    namespace: kgateway-system
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /lambda
    backendRefs:
    - name: my-lambda-backend
      group: gateway.kgateway.dev
      kind: Backend
```

### Plugin

The AWS Lambda plugin will be implemented in the `internal/kgateway/extensions2/plugins/upstream` directory. At a high-level, the following changes will be made:

1. Configure the lambda ARN in the [upstream HTTP filter](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/aws_lambda_filter#configuration-as-an-upstream-http-filter)
2. Configure a "dummy" lambda ARN in the [listener filter](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/aws_lambda_filter#configuration-as-a-listener-filter)

The listener filter is responsible for configuring how to authenticate the request to the lambda function. The upstream filter is responsible for configuring each lambda function, how to invoke it, etc. Note, we provide a "dummy" lambda ARN in the listener filter to satisfy the validation logic and to ensure that translator is idempotent (i.e. we don't override the lambda ARN for each route policy) by providing a default value.

This approach was prototyped in the [feat/aws-lambda](https://github.com/timflannagan/kgateway/tree/feat/aws-lambda) branch.

### Authentication Methods

The implementation prioritizes IRSA (IAM Roles for Service Accounts) as the primary and recommended authentication method:

1. **IRSA (Primary Implementation)**:
   - Uses IAM Roles for Service Accounts
   - AWS-recommended approach for Kubernetes workloads
   - Follows security best practices
   - Provides fine-grained access control
   - Automatic credential rotation
   - No need to store sensitive credentials in Kubernetes secrets
2. **Static Credentials** (Development/Testing Only):
   - Credentials stored in Kubernetes secrets
   - Not recommended for production use
   - Suitable only for development and testing
3. **Future Authentication Methods** (Optional):
   - AWS STS token exchange
   - Role chaining for cross-account access

## Testing

### LocalStack Integration

Testing AWS Lambda integration uses [LocalStack](https://github.com/localstack/localstack), a local AWS emulator that provides a fully functional local AWS cloud stack. This will allows us to test the integration without having to deploy to AWS account.

#### Test Environment Setup

1. **LocalStack Deployment**:
   ```bash
   # Deploy LocalStack to test cluster
   helm repo add localstack-repo https://helm.localstack.cloud
   helm install localstack localstack-repo/localstack
   ```
2. **Test Lambda Functions**:
   - Deploy sample Lambda functions to LocalStack
   - Functions include basic math operations and echo services
   - Provides predictable test cases
3. **Configuration Testing**:
   - Test Backend configuration with LocalStack endpoints
   - Verify credential handling
   - Test function invocation modes

See the [aws-lambda-testing](https://github.com/timflannagan/aws-lambda-testing) repository for a complete example of how this could be integrated into the project.

### Testing Strategy

The testing approach needs to account for limitations with LocalStack:

1. **Basic Routing Tests**
   - Use LocalStack for basic request routing validation
   - Test function invocation patterns
   - Test sync vs async invocation modes
2. **Authentication Testing**
   - LocalStack's IAM functionality is a Pro feature
   - IAM/IRSA tests will pass regardless of role validity
   - Manual testing required for authentication scenarios
3. **Edge Cases**
   - Manual testing required with real AWS credentials
   - Deploy an IAM user, role, and policy with Web Identity Token authentication
   - Deploy cert-manager and the EKS pod identity webhook on a kind cluster with a fake OIDC provider setup
   - Configure a custom GatewayParameters resource that injects the `eks.amazonaws.com/role-arn` annotation to the deployers' ServiceAccount
   - Setup basic routing and validate that invocation works with real credentials
   - Validate that resource-based, cross-account invocation works
4. **Service Mesh Integration**
   - Test Lambda integration with Istio auto-mTLS enabled
   - Validate correct error propagation when Lambda returns errors
   - Ensure gateway status accurately reflects Lambda execution status
   - Verify no encryption conflicts between gateway and service mesh

Due to LocalStack Pro limitations around IAM/IRSA validation, the following approach will be taken:

- Document the testing gap clearly
- Provide manual test procedures for maintainers and contributors
- Delegate comprehensive authentication and transformation testing to vendors

Running a test with real AWS credentials is not currently in scope for this proposal as it requires a production AWS account. In the future, we may add integration tests with real AWS credentials in CI.

## Alternatives

### Alternative 1: Adopt existing synthetic `Parameter` GVK

The previous approach for this feature relied on the synthetic `Parameter` GVK. While this approach was a good starting point, it had significant limitations:

1. **Limited Function Configuration**: The synthetic Parameter GVK could only express the function name as a single field. This prevented users from expressing additional function-level configurations like: function version/qualifier, invocation type (sync/async), etc.
2. **Lack of Type Safety**: Using a generic `Parameter` type meant losing the same Kubernetes type safety benefits and making validation more complex and/or brittle.
3. **Poor API Discoverability**: Similar to an annotation or label-based APIs, synthetic GVKs are difficult to discover through standard Kubernetes tooling (e.g., `kubectl explain`). Users must context switch to external documentation to understand available configuration options, rather than being able to explore the API structure directly through kubectl.

This proposal moves away from this approach in favor of a more flexible and maintainable solution that can be extended to support other AWS services in the future.

### Alternative 2: TrafficPolicy API

This proposal enables users to configure function-level parameters via the TrafficPolicy API. The Backend API is now focused on service-level and connection-level configuration and provides additional composability guarantees. Users that wish to configure function-level parameters can do so via the TrafficPolicy API. This proposal has the downside that requires users to configure multiple APIs to get the same functionality. Additionally, there's an open question around whether function-level parameters fall under the policy umbrella (i.e. fit within the scope of the TrafficPolicy API).

### Alternative 3: (Implementation Detail) Use the envoy-gloo lambda filter

The previous approach for this feature relied on the envoy-gloo lambda filter. While this filter has been battle tested in production environments, it is not a native Envoy filter and requires a custom build of Envoy. This proposal moves away from this approach in favor of a more flexible and maintainable solution that can be extended to support other AWS services in the future.

## Open Questions

- Is there a use case for multiple lambda functions within the same Backend?
- When IRSA is enabled, does the Backend need to sign AWS requests?
