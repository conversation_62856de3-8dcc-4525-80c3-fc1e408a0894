package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
	"k8s.io/cli-runtime/pkg/genericclioptions"
	"k8s.io/cli-runtime/pkg/resource"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/tools/clientcmd"
	cmdutil "k8s.io/kubectl/pkg/cmd/util"

	"github.com/kgateway-dev/kgateway/hack/utils/applier/pkg/applier"
)

var (
	fileNameFlags *genericclioptions.FileNameFlags

	dryRun        bool
	startIndex    int
	endIndex      int
	numIterations int
	force         bool

	delete bool

	async   bool
	workers int

	qps   float32
	burst int
)

// applyCmd represents the apply command
var applyCmd = &cobra.Command{
	Use: "apply",
	RunE: func(cmd *cobra.Command, args []string) error {
		fmt.Println("apply called")

		if burst < int(qps) {
			burst = int(qps)
		}

		configFlags.WithDiscoveryBurst(burst).WithDiscoveryQPS(qps)

		userSpecifiedContext, err := cmd.Flags().GetString("context")
		if err != nil {
			return err
		}

		restConfig, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: configFlags.ToRawKubeConfigLoader().ConfigAccess().GetDefaultFilename()},
			&clientcmd.ConfigOverrides{
				CurrentContext: userSpecifiedContext,
			}).ClientConfig()
		if err != nil {
			return err
		}

		restConfig.QPS = qps
		restConfig.Burst = burst

		factory := cmdutil.NewFactory(matchVersionKubeConfigFlags)
		validationDirective, err := cmdutil.GetValidationDirective(cmd)
		if err != nil {
			return err
		}

		dynamicClient, err := dynamic.NewForConfig(restConfig)
		if err != nil {
			return err
		}
		fieldValidationVerifier := resource.NewQueryParamVerifier(dynamicClient, factory.OpenAPIGetter(), resource.QueryParamFieldValidation)

		validator, err := factory.Validator(validationDirective, fieldValidationVerifier)
		if err != nil {
			return err
		}
		filenameOptions := fileNameFlags.ToOptions()

		if numIterations > 0 {
			endIndex = startIndex + numIterations
		}

		a := applier.Applier{
			Start:  startIndex,
			End:    endIndex,
			DryRun: dryRun,
			Force:  force,
			Delete: delete,

			Async:   async,
			Workers: workers,
		}

		return a.Apply(dynamicClient, factory, filenameOptions, validator)
	},
}

func init() {
	rootCmd.AddCommand(applyCmd)

	filenames := []string{}
	recursive := false
	kustomize := ""
	fileNameFlags = &genericclioptions.FileNameFlags{Usage: "", Filenames: &filenames, Kustomize: &kustomize, Recursive: &recursive}

	fileNameFlags.AddFlags(applyCmd.PersistentFlags())
	cmdutil.AddValidateFlags(applyCmd)

	applyCmd.Flags().IntVar(&startIndex, "start", 0, "Start index for the loop")
	applyCmd.Flags().IntVar(&numIterations, "iterations", 0, "If set, end index will be set to start+this")
	applyCmd.Flags().IntVar(&endIndex, "end", 3000, "End index for the loop. (If start is 0, this is the number times to apply the manifest)")
	applyCmd.Flags().BoolVarP(&dryRun, "dry-run", "d", false, "Dry run - print yamls to stdout")
	applyCmd.Flags().BoolVar(&force, "force", false, "Force apply - delete and recreate objects")
	applyCmd.Flags().Float32Var(&qps, "qps", 50, "QPS")
	applyCmd.Flags().IntVar(&burst, "burst", 75, "Burst")

	applyCmd.Flags().BoolVar(&async, "async", false, "Run in async mode. Use this if not hitting your QPS.")
	applyCmd.Flags().IntVar(&workers, "workers", 10, "Number of workers to use when using async mode. each worker submits requests in parallel.")

	applyCmd.Flags().BoolVar(&delete, "delete", false, "Delete resources instead of applying them (useful for cleanup)")
}
