package applier

import (
	"fmt"
	"reflect"
	"text/template"
)

func funcMap() template.FuncMap {
	return template.FuncMap{
		"add":      add,
		"subtract": subtract,
	}
}

func convert(a reflect.Value) (int64, error) {

	switch a.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return a.Int(), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return int64(a.Uint()), nil
	default:
		return 0, fmt.Errorf("unsupported type: %T", a)
	}

}

func add(a, b reflect.Value) (interface{}, error) {
	a64, err := convert(a)
	if err != nil {
		return nil, err
	}
	b64, err := convert(b)
	if err != nil {
		return nil, err
	}
	return a64 + b64, nil
}

func subtract(a, b reflect.Value) (interface{}, error) {
	a64, err := convert(a)
	if err != nil {
		return nil, err
	}
	b64, err := convert(b)
	if err != nil {
		return nil, err
	}
	return a64 - b64, nil
}
