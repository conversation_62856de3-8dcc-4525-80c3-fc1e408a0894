package applier

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"sync"
	"text/template"
	"time"

	"github.com/cheggaaa/pb/v3"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/serializer/json"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/cli-runtime/pkg/resource"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes/scheme"
	cmdutil "k8s.io/kubectl/pkg/cmd/util"
	"k8s.io/kubectl/pkg/validation"
)

type Applier struct {
	DryRun bool
	Start  int
	End    int

	Force  bool
	Delete bool

	Async   bool
	Workers int

	getLock sync.Mutex
}

func (a *Applier) Apply(dynamicClient dynamic.Interface, factory cmdutil.Factory, fo resource.FilenameOptions, validator validation.Schema) error {
	ctx := context.Background()
	templateObjects, err := a.getobjects(ctx, factory, fo, validator)

	if err != nil {
		return err
	}

	if a.DryRun {

		for i := a.Start; i < a.End; i++ {
			for _, obj := range templateObjects {
				u := obj.Get(i)
				s := json.NewYAMLSerializer(json.DefaultMetaFactory, scheme.Scheme, scheme.Scheme)
				s.Encode(u, os.Stdout)
				fmt.Fprintln(os.Stdout, "---")
			}
		}

	} else {

		errs := []error{}
		iterations := (a.End - a.Start)
		expectedNumObjs := iterations * len(templateObjects)
		fmt.Println("We have", iterations, "iterations and", len(templateObjects), "objects to apply in each iteration. For a total of", expectedNumObjs, "objects to apply.")
		fmt.Println("objects start: ", time.Now().Format(time.RFC3339))
		defer func() { fmt.Println("objects done: ", time.Now().Format(time.RFC3339)) }()

		bar := pb.StartNew(expectedNumObjs)
		bar.SetTemplate(pb.Full)
		defer bar.Finish()

		if !a.Async {
			firstError := false
			for i := a.Start; i < a.End; i++ {
				for _, obj := range templateObjects {
					err = a.applyOne(ctx, i, obj, dynamicClient)
					if err != nil {
						if !firstError {
							fmt.Printf("First error encountered at index: %d %v\n", i, err)
							firstError = true
						}
						errs = append(errs, err)
					}
					bar.Increment()
				}
			}
		} else {
			var firstError error
			errIndex := 0
			for result := range a.runner(ctx, templateObjects, dynamicClient) {
				if result.err != nil {
					if firstError == nil || errIndex > result.index {
						firstError = result.err
						errIndex = result.index
						fmt.Printf("Potential first error encountered at index: %d %v\n", errIndex, firstError)
					}
					errs = append(errs, result.err)
				}
				bar.Increment()
			}
			if firstError != nil {
				fmt.Printf("First error encountered at index: %d %v\n", errIndex, firstError)
			}
		}
		if len(errs) == 1 {
			return errs[0]
		}
		if len(errs) > 1 {
			return utilerrors.NewAggregate(errs)
		}
	}

	return nil
}

func (a *Applier) getobjects(ctx context.Context, factory cmdutil.Factory, fo resource.FilenameOptions, validator validation.Schema) ([]*TemplateInfo, error) {
	builder := factory.NewBuilder()

	if a.Workers == 0 {
		a.Workers = 10
	}

	namespace, enforceNamespace, err := factory.ToRawKubeConfigLoader().Namespace()
	if err != nil {
		return nil, err
	}
	// read the yaml or yaml array from the template file
	r := builder.
		Unstructured().
		Schema(validator).
		ContinueOnError().
		NamespaceParam(namespace).DefaultNamespace().
		FilenameParam(enforceNamespace, &fo).
		Flatten().
		Do()
	objects, err := r.Infos()
	if err != nil {
		return nil, err
	}

	var templateObjects []*TemplateInfo
	for _, info := range objects {
		templateObjects = append(templateObjects, NewTemplateInfo(info))
	}
	return templateObjects, nil
}

func (a *Applier) applyOne(ctx context.Context, i int, obj *TemplateInfo, dynamicClient dynamic.Interface) error {

	a.getLock.Lock()
	objToCreate := obj.Get(i).DeepCopy()
	a.getLock.Unlock()
	var err error
	if a.Delete {
		err = dynamicClient.Resource(obj.Mapping.Resource).Namespace(objToCreate.GetNamespace()).Delete(ctx, objToCreate.GetName(), metav1.DeleteOptions{})
	} else {
		_, err = dynamicClient.Resource(obj.Mapping.Resource).Namespace(objToCreate.GetNamespace()).Apply(ctx, objToCreate.GetName(), objToCreate, metav1.ApplyOptions{FieldManager: "solo-io/applier", Force: a.Force})
	}
	return err
}

type result struct {
	err   error
	index int
}

func (a *Applier) runner(ctx context.Context, templateObjects []*TemplateInfo, dynamicClient dynamic.Interface) <-chan result {
	resultc := make(chan result, 100)
	var wg sync.WaitGroup

	type queueItem struct {
		index int
	}
	queue := make(chan queueItem, 100)
	go func() {
		// put all objects in the queue
		defer close(queue)
		for i := a.Start; i < a.End; i++ {
			i := i
			queue <- queueItem{
				index: i,
			}
		}
	}()

	// spawn workers to process the queue
	for i := 0; i < a.Workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for item := range queue {
				// create objects in order in case there are dependencies
				for _, obj := range templateObjects {
					err := a.applyOne(ctx, item.index, obj, dynamicClient)
					resultc <- result{index: item.index, err: err}
				}
			}
		}()
	}

	go func() {
		wg.Wait()
		// we all workers are done, close the result channel
		close(resultc)
	}()
	return resultc
}

type TemplateContext struct {
	Index int
}
type TemplateInfo struct {
	*resource.Info
	Modifiers         []func(TemplateContext)
	UnstructuedObject *unstructured.Unstructured
}

func NewTemplateInfo(info *resource.Info) *TemplateInfo {
	ti := &TemplateInfo{
		Info:              info,
		UnstructuedObject: info.Object.(*unstructured.Unstructured).DeepCopy(),
	}

	ti.addModifiers(ti.UnstructuedObject.Object)
	return ti
}

func (ti *TemplateInfo) addModifiers(obj map[string]interface{}) {

	// Object is a JSON compatible map with string, float, int, bool, []interface{}, or
	// map[string]interface{}
	// children.
	for k, v := range obj {
		k := k
		v := v

		switch v := v.(type) {
		case string:
			ti.maybeTemplatify(v, func(n string) {
				obj[k] = n
			})
			// test if we need a template

		case map[string]interface{}:
			ti.addModifiers(v)
		case []interface{}:
			for i, elem := range v {
				i := i
				switch elem := elem.(type) {
				case string:
					ti.maybeTemplatify(elem, func(n string) {
						v[i] = n
					})
				case map[string]interface{}:
					ti.addModifiers(elem)
				}
			}
		}
	}
}

func (ti *TemplateInfo) maybeTemplatify(originalValue string, f func(n string)) {
	// test if we need a template
	t := template.Must(template.New("test").Funcs(funcMap()).Parse(originalValue))
	var b bytes.Buffer
	// test if we need a template
	t.Execute(&b, TemplateContext{})
	if b.String() != originalValue {
		ti.Modifiers = append(ti.Modifiers, func(tc TemplateContext) {
			var b bytes.Buffer
			t.Execute(&b, tc)
			f(b.String())
		})
	}
}

func (ti *TemplateInfo) Get(index int) *unstructured.Unstructured {
	tc := TemplateContext{
		Index: index,
	}
	for _, m := range ti.Modifiers {
		m(tc)
	}
	return ti.UnstructuedObject
}
