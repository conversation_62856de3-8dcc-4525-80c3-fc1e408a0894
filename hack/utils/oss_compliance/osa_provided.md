Name|Version|License
---|---|---
[agentgateway/agentgateway](https://github.com/agentgateway/agentgateway)|v0.4.31-0.20250519135723-61536c2b3fa0|Apache License 2.0
[avast/retry-go](https://github.com/avast/retry-go)|v2.4.3+incompatible|MIT License
[retry-go/v4](https://github.com/avast/retry-go)|v4.3.3|MIT License
[caarlos0/log](https://github.com/caarlos0/log)|v0.4.6|MIT License
[xds/go](https://github.com/cncf/xds)|v0.0.0-20250121191232-2f005788dc42|Apache License 2.0
[envoyproxy/go-control-plane](https://github.com/envoyproxy/go-control-plane)|v0.13.5-0.20250507123352-93990c5ec02f|Apache License 2.0
[go-control-plane/contrib](https://github.com/envoyproxy/go-control-plane)|v1.32.5-0.20250507123352-93990c5ec02f|Apache License 2.0
[go-control-plane/envoy](https://github.com/envoyproxy/go-control-plane)|v1.32.5-0.20250507123352-93990c5ec02f|Apache License 2.0
[go-control-plane/ratelimit](https://github.com/envoyproxy/go-control-plane)|v0.1.1-0.20250507123352-93990c5ec02f|Apache License 2.0
[fsnotify/fsnotify](https://github.com/fsnotify/fsnotify)|v1.9.0|BSD 3-clause "New" or "Revised" License
[ghodss/yaml](https://github.com/ghodss/yaml)|v1.0.1-0.20190212211648-25d852aebe32|MIT License
[go-logr/logr](https://github.com/go-logr/logr)|v1.4.2|Apache License 2.0
[go-logr/zapr](https://github.com/go-logr/zapr)|v1.3.0|Apache License 2.0
[google/go-cmp](https://github.com/google/go-cmp)|v0.7.0|BSD 3-clause "New" or "Revised" License
[grpc-ecosystem/go-grpc-middleware](https://github.com/grpc-ecosystem/go-grpc-middleware)|v1.4.0|Apache License 2.0
[kelseyhightower/envconfig](https://github.com/kelseyhightower/envconfig)|v1.4.0|MIT License
[mitchellh/hashstructure](https://github.com/mitchellh/hashstructure)|v1.0.0|MIT License
[ginkgo/v2](https://github.com/onsi/ginkgo)|v2.23.3|MIT License
[onsi/gomega](https://github.com/onsi/gomega)|v1.36.3|MIT License
[pkg/errors](https://github.com/pkg/errors)|v0.9.1|BSD 2-clause "Simplified" License
[rotisserie/eris](https://github.com/rotisserie/eris)|v0.5.4|MIT License
[spf13/afero](https://github.com/spf13/afero)|v1.14.0|Apache License 2.0
[spf13/cobra](https://github.com/spf13/cobra)|v1.9.1|Apache License 2.0
[stretchr/testify](https://github.com/stretchr/testify)|v1.10.0|MIT License
[go.opencensus.io](https://go.opencensus.io)|v0.24.0|Apache License 2.0
[go.uber.org/zap](https://go.uber.org/zap)|v1.27.0|MIT License
[x/exp](https://golang.org/x/exp)|v0.0.0-20241215155358-4a5509556b9e|BSD 3-clause "New" or "Revised" License
[x/net](https://golang.org/x/net)|v0.39.0|BSD 3-clause "New" or "Revised" License
[google.golang.org/grpc](https://google.golang.org/grpc)|v1.72.0|Apache License 2.0
[google.golang.org/protobuf](https://google.golang.org/protobuf)|v1.36.6|BSD 3-clause "New" or "Revised" License
[helm/v3](https://helm.sh/helm/v3)|v3.17.3|Apache License 2.0
[istio.io/api](https://istio.io/api)|v1.25.0-alpha.0.0.20250210220544-0b64afd2de85|Apache License 2.0
[istio.io/client-go](https://istio.io/client-go)|v1.25.0-alpha.0.0.20250210220843-5a4065fded65|Apache License 2.0
[istio.io/istio](https://istio.io/istio)|v0.0.0-20250212203644-c2ac935c5888|Apache License 2.0
[k8s.io/api](https://k8s.io/api)|v0.32.3|Apache License 2.0
[k8s.io/apiextensions-apiserver](https://k8s.io/apiextensions-apiserver)|v0.32.3|Apache License 2.0
[k8s.io/apimachinery](https://k8s.io/apimachinery)|v0.32.3|Apache License 2.0
[k8s.io/client-go](https://k8s.io/client-go)|v0.32.3|Apache License 2.0
[klog/v2](https://k8s.io/klog/v2)|v2.130.1|Apache License 2.0
[k8s.io/kube-openapi](https://k8s.io/kube-openapi)|v0.0.0-20250318190949-c8a335a9a2ff|Apache License 2.0
[k8s.io/utils](https://k8s.io/utils)|v0.0.0-20241210054802-24370beab758|Apache License 2.0
[knative.dev/pkg](https://knative.dev/pkg)|v0.0.0-20211206113427-18589ac7627e|Apache License 2.0
[sigs.k8s.io/controller-runtime](https://sigs.k8s.io/controller-runtime)|v0.20.4|Apache License 2.0
[sigs.k8s.io/gateway-api](https://sigs.k8s.io/gateway-api)|v1.3.0|Apache License 2.0
[sigs.k8s.io/gateway-api-inference-extension](https://sigs.k8s.io/gateway-api-inference-extension)|v0.2.0|Apache License 2.0
[structured-merge-diff/v4](https://sigs.k8s.io/structured-merge-diff/v4)|v4.7.0|Apache License 2.0
[sigs.k8s.io/yaml](https://sigs.k8s.io/yaml)|v1.4.0|MIT License
[cmd/goimports](https://golang.org/x/tools/cmd/goimports)|latest|MIT License
[gogo/protobuf](https://github.com/gogo/protobuf)|latest|MIT License
[envoyproxy/envoy](https://github.com/envoyproxy/envoy)|latest|Apache License 2.0
[envoyproxy/protoc-gen-validate](https://github.com/envoyproxy/protoc-gen-validate)|latest|Apache License 2.0
[paulvollmer/2gobytes](https://github.com/paulvollmer/2gobytes)|latest|MIT License
