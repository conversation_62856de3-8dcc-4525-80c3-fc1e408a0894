ARG ENVOY_IMAGE
ARG BASE_IMAGE

FROM $ENVOY_IMAGE as envoy

FROM $BASE_IMAGE
ARG GOARCH=amd64

COPY --from=envoy /usr/local/bin/envoy /usr/local/bin/envoy

# Copy over the required libraries
# lib64z1 - Required by lib<PERSON><PERSON> for xslt transformations
COPY --from=envoy /usr/lib/x86_64-linux-gnu/libz.so* /usr/lib/x86_64-linux-gnu/

COPY kgateway-linux-$GOARCH /usr/local/bin/kgateway

USER 10101

ENTRYPOINT ["/usr/local/bin/kgateway"]
