# Code Generation

To run the full codegen process, use `make -B install-go-tools generated-code`. In most cases it is not necessary to run all sub-targets within `generated-code`.

Here is a description of each sub-target and its purpose:

| Target                     | Description                                                                                                                    | Use when...                                                                          | Approximate runtime                  |
|----------------------------|:-------------------------------------------------------------------------------------------------------------------------------|:-------------------------------------------------------------------------------------|--------------------------------------|
| `install-go-tools`         | Invokes `mod-download` and `go install`s a number of tools used by `generated-code`                                            | The `_output` dir is not present or you are otherwise unsure deps are installed      | 3-8s (if mod download is up-to-date) |
| `mod-download`             | Calls `go mod download all`                                                                                                    | Deps are not all present locally                                                     | 1s (if deps are already downloaded)  |
| `generated-code`           | Generates all required code, cleaning and formatting as well; this target is executed in CI                                    | You need to run all codegen steps without human error (ie prior to PR merge)         | 4-5m                                 |
| `clean-solo-kit-gen`       | Removes files generated by codegen other than docs and tests                                                                   | An existing generated file may no longer be generated by codegen (rare)              | 3s                                   |
| `go-generate-all`          | Invokes all generate directives in the repo, most notably in `generate.go` which runs solo-kit codegen, and mockgen directives | There is an any API change                                                           | 3-5m                                 |
| `go-generate-apis`         | Invokes the generate directive in `generate.go` which runs solo-kit codegen                                                    | There is a proto API change (prefer to use `generated-code-apis` which also formats) | 1m                                   |
| `go-generate-mocks`        | Invokes all mockgen generate directives in the repo                                                                            | There is an interface API change                                                     | 2:30-3m                              |
| `generate-cli-docs`        | Removes existing CLI docs and re-generates them                                                                                | CLI code has been modified                                                           | 10-15s                               |
| `generated-code-apis`      | Generates and formats code from protos                                                                                         | There is a proto API change                                                          | 1:30s                                |
| `generated-code-cleanup`   | Executes cleanup and formatting targets                                                                                        | Preparing to open a PR without API changes                                           | 30-40s                               |
| `getter-check`             | Ensures that accesses for fields which have "getter" functions are exclusively done via said "getter" functions                | Any code has been written (just in case)                                             | ~7s                                  |
| `mod-tidy`                 | Calls `go mod tidy`                                                                                                            | Dependencies have been added, updated, or removed                                    | 1-2s                                 |                                |
| `verify-enterprise-protos` | Validates that protos used in EE but not OSS are valid                                                                         | Unsure whether protos used in EE are valid                                           | 3-10s                                |
| `generate-helm-files`      | Generates helm chart and value files                                                                                           | The helm chart or values template has been modified                                  | 1-3s                                 |
| `update-licenses`          | Generates docs files containing attribution for all dependencies which require it                                              | There is a new dependency or a depency bump                                          | 7-12s                                |
| `fmt`                      | Runs [`goimports`](https://pkg.go.dev/golang.org/x/tools/cmd/goimports) which updates imports and formats code                 | Code has been modified (any change, in case it's not properly formatted)             | 9-13s                                |