# Filing Issues

GitHub issues are the main way for reporting bugs, tracking work you want to do and requesting features in kgateway.

If you encounter a bug or have a feature request, please take the following approach:
1. Search for existing issues.
2. If you find a similar issue, add a comment with additional information or add a 👍 reaction to indicate your agreement.
3. If there are no similar issues, [file a new one](https://github.com/kgateway-dev/kgateway/issues/new/choose)

**Issues in this repo should not contain any sensitive information. If sensitive information is critical to the issue, please submit via the security pipeline https://github.com/kgateway-dev/community/blob/main/CVE.md.**

## Bug Report
- The more details about the issue, the better. Please include the following information:
  - Kgateway version
  - Kubernetes version
  - Operating system
  - Steps to reproduce
  - Expected behavior
  - Actual behavior
  - Logs
  - Screenshots
  - Any other relevant information

## Feature Request
- Detailed description of the use case for the feature
- If possible, a description of how the feature is implemented in other tools

## CI Failure
- Include a link to the failed CI job.
- Copy the error message from the failed job (logs are discarded after a few months). 

## Security Issues
We take kgateway's security very seriously. If you've found a security issue or a potential security issue in kgateway, please **DO NOT** file a public Github issue, instead see https://github.com/kgateway-dev/community/blob/main/CVE.md for how to submit.

