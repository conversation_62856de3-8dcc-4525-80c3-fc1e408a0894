# Developing kgateway

Find tools and information to help you develop the kgateway project.

* `architecture`: Descriptions about high-level design and implementation details of various packages and features.
* `contributing`: Information to help you contribute to the project, such as how to open issues, review pull requests, generate code, and backport fixes.
* `debugging`: Troubleshooting steps for debugging frequent issues.
* `testing`: Descriptions on how the tests work and how to use them.

Other resources:
* [Developer guide (Gloo Edge API)](https://docs.solo.io/gloo-edge/latest/guides/dev/) - 
while this guide is written for Gloo Edge, it can be used temporarily for setting up your development environment and learning about extending kgateway functionality and related plug-ins. An updated guide specific to kgateway will be provided in the future. 
* [Product documentation (K8s Gateway API)](https://docs.solo.io/gateway/latest/)
* [Contribution guidelines (K8s Gateway API)](https://docs.solo.io/gateway/latest/reference/contribution/)
