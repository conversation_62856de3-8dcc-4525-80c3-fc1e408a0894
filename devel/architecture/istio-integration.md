An overview of some key concepts related to this test suite:

# mTLS
[mTLS explained](https://www.cloudflare.com/learning/access-management/what-is-mutual-tls/)

[mTLS in Envoy](https://www.envoyproxy.io/docs/envoy/latest/start/quick-start/securing#use-mutual-tls-mtls-to-enforce-client-certificate-authentication)

# Gloo mTLS
[Background](https://docs.solo.io/gloo-edge/latest/guides/security/tls/mtls/)

In Gloo Gateway, services that receive their configuration from the xDS server, communicate using the xDS protocol, which is done in plaintext. Since the information being provided contains sensitive information (secrets), it is preferable to encrypt this traffic. This applies to the following services:
- Gateway-proxy
- Ext-auth-service
- Rate-limiter

## How does this work?
Each component involved in the xDS communication will contain an SDS sidecar and potentially an Envoy sidecar as well (for our gateway-proxy pod, which already contains an envoy container, this Envoy sidecar is not necessary)

**Envoy Sidecar**: Responsible for TLS termination, and outgoing encryption. Receives it’s secret information via SDS, talking to the SDS sidecar. The sidecar bootstrap configuration is defined in a ConfigMap for each component.

**SDS Sidecar**: Watches the gloo-mtls-certs TLS secret in kubernetes, and serves the contents via the SDS API to the Envoy sidecar. The SDS code can be found [here](https://github.com/solo-io/gloo/tree/main/projects/sds). Anytime the secret is modified, the contents will be updated dynamically. To automate the updating of secrets, run the [CertGen Job](https://github.com/solo-io/gloo/blob/main/install/helm/gloo/templates/19-gloo-mtls-certgen-job.yaml)


### Example: Gloo <-> ExtAuth
[ExtAuth Envoy Sidecar](https://github.com/solo-io/solo-projects/blob/main/install/helm/gloo-ee/templates/24-extauth-sidecar-config.yaml)

- ExtAuth pod initiates xDS request to Gloo
- ExtAuth.EnvoySidecar hijacks request, and sends encrypted request to Gloo
- Gloo.EnvoySidecar terminates TLS request from ExtAuth
- Gloo.EnvoySidecar directs request to Gloo xDS port

# Istio mTLS

[Background](https://docs.solo.io/gloo-edge/latest/guides/integrations/service_mesh/istio/)

When services are deployed within the Istio Service Mesh, it is possible to require that all traffic between those services is encrypted with mTLS. This is handled dynamically by Istio managing certs for services within the Mesh. However, it poses a challenge when Gloo Gateway (not in the Mesh) tries to route traffic to services within the Mesh.

This challenge is that Edge encrypts traffic using the certificates that were generated by IstioD and used within the Mesh.  To accomplish this, Edge uses a similar paradigm as the Gloo mTLS solution, leveraging sidecars to encrypt traffic.

## How does this work?
To keep the mTLS communication transparent to the Edge configuration, we again leverage sidecars to handle the encryption.

[Istio](https://istio.io/latest/docs/ops/deployment/architecture/) is logically split into a data plane and a control plane. The data plane is composed of a set of intelligent proxies (Envoy) deployed as sidecars. The control plane (istiod) provides service discovery, configuration and certificate management.
Istiod acts as a Certificate Authority (CA) and generates certificates to allow secure mTLS communication in the data plane.

**Istio-Proxy Sidecar**: This is responsible for generating the certificates used for mTLS communication. These certificates are mounted to a volume, which are then provided to the gateway-proxy configuration via SDS. The gateway-proxy, with these certificates, is now able to establish mTLS communication with an upstream in the Mesh.
The Istio-Proxy Sidecar usually runs both the istio-agent and envoy. The [istio-agent](https://github.com/istio/istio/blob/master/architecture/security/istio-agent.md) is responsible for generating the certificates, and the envoy is responsible for terminating TLS and establishing mTLS communication with other services in the Mesh.
By default, the istio-proxy will run an extended version of the Envoy proxy. However, we don't need the istio-proxy Envoy functionality, we only need the istio-agent to create the CSR request to istiod and handle rotating certificates near expiration.
To avoid running the istio-proxy Envoy, we can set the `DISABLE_ENVOY` environment variable. This will cause the istio-proxy to run in proxyless mode and not start the Envoy process.

<img src="https://github.com/istio/istio/raw/master/architecture/security/docs/overview.svg">

**SDS Sidecar**: Watches the Istio certs, generated by the istio-proxy sidecar, and serves them up via SDS to the gateway-proxy. The SDS code can be found [here](https://github.com/solo-io/gloo/tree/main/projects/sds). Any time the volume is modified, the contents will be updated dynamically. This is done because Istio rotates its certificates by default, every 24 hours (can be controlled by env variable). Without this SDS sidecar, the proxies would use outdated certificates, and when new ones are rolled out by Istio, our gateway-proxy wouldn’t be able to communicate with services using the new certificates.

# SDS

Envoy provides an API for dynamically updating TLS certificates. Historically, this required restarting the proxies with the new certificates, but the [SDS API](https://www.envoyproxy.io/docs/envoy/latest/configuration/security/secret) allows a zero-downtime approach to this.

In Gloo Gateway, we build an SDS server which implements this API. Its sole responsibility is to implement the SDS API, and serve up certificates via xDS. We configure our Envoy proxies as the client to this server, and the proxies are [configured to get their certificates via the SDS API](https://github.com/solo-io/gloo/blob/main/install/helm/gloo/templates/9-gateway-proxy-configmap.yaml#L195)

While the component itself has a single responsibility, implement the SDS API, it can be used in Gloo Gateway in multiple ways:
- Serve certificates to establish mTLS communication between Gloo components (Gloo mTLS)
- Serve certificates to establish mLTS communication between Gateway-Proxy and Application running in Service Mesh (Istio mTLS)

_As a result, we have `glooMtls.enabled` to enable the former, and `istioIntegration.enabled` to enable the latter._

Note, the current Gloo SDS does not reach out to Istiod. The istio-agent is responsible for sending the CSR to Istiod.
The SDS server then reads the certs from a file written by the istio-agent and then SDS serves the certificates to the Gloo Envoy proxy.

# Validating mTLS Traffic

Istio leverages the [`x-forwarded-client-cert`](https://istio.io/latest/docs/ops/configuration/traffic-management/network-topologies/#forwarding-external-client-attributes-ip-address-certificate-info-to-destination-workloads) header to identify encrypted traffic

If the application that we’re running can logs requests that it receives, we could search the logs for the existence of that header

# Testing automtls

The Istio e2e integration tests automtls functionality with kgateway resources. This
can be manually tested by following the steps below on a kind cluster:

1. Setup environment and kind cluster

```shell
hack/kind/setup-kind.sh; make kind-build-and-load
```

2. Install Istio

```shell
./internal/kgateway/istio.sh
```

```shell
cat <<EOF | kubectl apply -f -
apiVersion: "security.istio.io/v1beta1"
kind: "PeerAuthentication"
metadata:
  name: "productpage"
  namespace: "bookinfo"
spec:
  selector:
    matchLabels:
      app: productpage
  mtls:
    mode: STRICT
EOF
```

3. Install Gloo

helm upgrade -i -n gloo-system gloo ./_test/gloo-1.0.0-ci1.tgz --create-namespace --set global.istioIntegration.enabled=true

4. Show Gateway Mode

Apply resources:

```shell
kubectl apply -f - <<EOF
apiVersion: gateway.solo.io/v1
kind: VirtualService
metadata:
  name: test-vs
  namespace: gloo-system
spec:
  virtualHost:
    domains:
      - 'www.example.com'
    routes:
      - matchers:
         - prefix: /
        routeAction:
          single:
            upstream:
              name: bookinfo-productpage-9080
              namespace: gloo-system
EOF
```

Port-forward the classic gateway:

```shell
kubectl port-forward deployment/gateway-proxy -n gloo-system 8080:8080
```

First attempt will fail because of PeerAuthentication policy:

```shell
curl -I localhost:8080/productpage -H "host: www.example.com" -v
```

Next, enable automtls in settings:
```shell
  istioOptions:
    enableAutoMtls: true
```

Should work:
```shell
curl -I localhost:8080/productpage -H "host: www.example.com" -v
```

Then edit upstream to disable automtls:
```shell
kubectl edit upstreams -n gloo-system bookinfo-productpage-9080
```

Add this line:

```shell
spec:
    disableIstioAutoMtls: true
```

This should break the curl now that automtls is disabled:
```shell
curl -I localhost:8080/productpage -H "host: www.example.com" -v
```

Test an overwrite:

```shell
glooctl istio enable-mtls --upstream bookinfo-productpage-9080 -n gloo-system
```

This adds the following settings to the upstream:

```shell
sslConfig:
  alpnProtocols:
  - istio
  sds:
    certificatesSecretName: istio_server_cert
    clusterName: gateway_proxy_sds
    targetUri: 127.0.0.1:8234
    validationContextName: istio_validation_context
```

This should fix the curl:

```shell
curl -I localhost:8080/productpage -H "host: www.example.com" -v
```

Delete resources:

```
kubectl delete VirtualService test-vs -n gloo-system
```

Remove the upstream edit:

```shell
glooctl istio disable-mtls --upstream bookinfo-productpage-9080 -n gloo-system
```

5. Test k8s Gateway Mode

Create a gateway resource:

```shell
kubectl apply -f - <<EOF
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: http
spec:
  gatewayClassName: kgateway
  listeners:
  - allowedRoutes:
      namespaces:
        from: All
    name: http
    port: 8080
    protocol: HTTP
EOF
```

This should create a new gateway in the `default` namespace named `http`:
```shell
❯ kubectl get deployments
NAME   READY   UP-TO-DATE   AVAILABLE   AGE
http   1/1     1            1           10m
```

Apply an HTTPRoute equivalent to the VirtualService we tested earlier:

```shell
kubectl apply -f- <<EOF
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: productpage
  namespace: bookinfo
  labels:
    example: productpage-route
spec:
  parentRefs:
    - name: http
      namespace: default
  hostnames:
    - "www.example.com"
  rules:
    - backendRefs:
        - name: productpage
          port: 9080
EOF
```

Port-forward the new k8s gateway:

```shell
kubectl port-forward deployment/http 8080:8080
```

Now let's send traffic with the same curl as before, this time going through the new k8s Gateway API gateway.
The first attempt will succeed because of automtls is still enabled on the settings policy:

```shell
curl -I localhost:8080/productpage -H "host: www.example.com" -v
```

If auto mtls is disabled on the settings, the same request will fail.

NOTE: Upstream support doesn't exist (yet!) for k8s gateway mode yet so there is no way to overwrite auto mtls or disable it for a specific upstream.

# Additional context
Additional context on Istio and how the Gloo integration with Istio works can be found [here](https://docs.google.com/document/d/1g7wq6yBGR6VioNJTz_eA7GLRbijlDasGR3h-eToz7jA)