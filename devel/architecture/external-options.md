# External Options (VirtualHostOption and RouteOption)

## Overview
Gloo Gateway supports decoupling certain resources from the definitions of options related to the resource. Currently there are two objects which support delegating options definition in this way:
- A VirtualHost (exists on a VirtualService) can be configured with the separate resource [VirtualHostOption](./projects/gateway/api/v1/external_options.proto).
- A Route (exists on a VirtualHost within a VirtualService or on a RouteTable) can be configured with the separate resource [RouteOption](./projects/gateway/api/v1/external_options.proto).

The Kubernetes CRDs for these resources are generated by solo-kit, NOT skv2.

The options that are defined directly on the resource, if non-nil, will always take precedence over the delegated options resources.

## Classic Gateway
<!-- TODO -->

## Kubernetes Gateway
RouteOption and VirtualHostOption resources are honored in the Kubernetes Gateway integration via plugins. These are associated with the related resources in slightly different ways, and the Statuses reported for them have subtleties as well.

### RouteOptions

#### Overview 
RouteOption resources are associated with the Kubernetes Gateway API resource [HTTPRoute](https://gateway-api.sigs.k8s.io/api-types/httproute/). They may be specified by the HTTPRoute as an extensionRef or on the targetRef field of the RouteOption resource. This "policy attachment" pattern is experimental as of this writing but widely adopted. More details on policy attachment can be found in [GEP-713](https://gateway-api.sigs.k8s.io/geps/gep-713/).

### VirtualHostOptions

#### Overview 
VirtualHostOption resources are associated with the Kubernetes Gateway API resource [Gateway](https://gateway-api.sigs.k8s.io/api-types/httproute/). They may be specified on the targetRef field of the VirtualHostOption resource. Unlike RouteOption, VirtualHostOption resources support the field `sectionName`, which if specified will (at the time of this writing) indicate which [Kubernetes Gateway API Listener](https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1.Listener) the resource should target. If `sectionName` is not specified, all Listeners on the targeted Gateway will be considered associated with the resource. This "policy attachment" pattern is experimental as of this writing but widely adopted. More details on policy attachment can be found in [GEP-713](https://gateway-api.sigs.k8s.io/geps/gep-713/).

It is important to note that since `VirtualHost` is an Envoy primitive, and transitively a Classic Gateway API, without an analogous resource in the Kubernetes Gateway API spec, users of the latter have no direct interaction with `VirtualHost`s. Instead, a Kubernetes Gateway API Listener is translated to a gloov1.Listener, and `VirtualHost`s on that gloov1.Listener are automatically assigned based on what filter chains were created during Listener translation.

#### Policy selection
In order to keep the original implementation simple, we currently only support one `VirtualHostOption` resource being attached to any given Kubernetes Gateway API Listener. Because an arbitrary number of `VirtualHostOption` resources may be attached to any given Kubernetes Gateway API Gateway with or without `sectionName`, we need some sort of prioritization. This priority order is as follows:
1. (Highest priority) Oldest `VirtualHostOption` targeting the Listener with `sectionName`
2. Newer `VirtualHostOption`s targeting the Listener with `sectionName`
3. Oldest `VirtualHostOption` targeting the Gateway on which the Listener resides, but without `sectionName` defined
4. (Lowest priority) Newer `VirtualHostOption`s targeting the Gateway on which the Listener resides, but without `sectionName` defined

#### Status
To provide some insight into which options are applied to a given Kubernetes Gateway API Listener, warnings are logged in the `Status` of the `VirtualHostOption` resource if it would have been attached to a Listener, but was not due to being lower priority than another valid `VirtualHostOption` resource. 
