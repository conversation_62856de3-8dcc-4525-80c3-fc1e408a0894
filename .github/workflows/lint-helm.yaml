name: <PERSON><PERSON>

on:
  pull_request: { }
  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  lint-helm:
    name: <PERSON><PERSON>
    runs-on: ubuntu-22.04
    timeout-minutes: 5
    steps:
    - name: Check out code into the Go module directory
      uses: actions/checkout@v4
    - name: Lint Helm Charts
      run: make lint-kgateway-charts
