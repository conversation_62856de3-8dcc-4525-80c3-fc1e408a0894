name: Lint Python Code

on:
  # Run on all PRs, but check for *.py changes in the workflow condition
  # See: https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/collaborating-on-repositories-with-code-quality-features/troubleshooting-required-status-checks#handling-skipped-but-required-checks
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11

      - name: Check for Python file changes
        id: check_python_changes
        run: |
          if git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep -E '\.py$'; then
            echo "PYTHON_CHANGED=true" >> "$GITHUB_ENV"
          else
            echo "PYTHON_CHANGED=false" >> "$GITHUB_ENV"
          fi

      - name: Install ruff
        if: env.PYTHON_CHANGED == 'true'
        run: |
          python -m pip install --upgrade pip
          pip install ruff==0.9.0

      - name: Run ruff linter
        if: env.PYTHON_CHANGED == 'true'
        run: ruff check . --output-format=github --exclude '**/*_pb2.py' --exclude '**/*_pb2.pyi' --exclude '**/*_pb2_*.py'

      - name: Run ruff format check
        if: env.PYTHON_CHANGED == 'true'
        run: |
          if ! ruff format --diff . --exclude '**/*_pb2.py' --exclude '**/*_pb2.pyi' --exclude '**/*_pb2_*.py'; then
            echo "Ruff formatting issues detected. Please run 'ruff format . --exclude '**/*_pb2.py' --exclude '**/*_pb2.pyi' --exclude '**/*_pb2_*.py'' locally to fix formatting issues."
            exit 1
          fi