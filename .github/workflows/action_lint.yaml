name: pr-github-workflow-lint

on:
  pull_request:
    paths:
      - .github/*.yaml
      - .github/*.yml
permissions:
  contents: read

jobs:
  actionlint:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: .github
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
      - run: go install github.com/rhysd/actionlint/cmd/actionlint@v1.7.6
      - run: actionlint