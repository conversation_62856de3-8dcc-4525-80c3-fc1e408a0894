name: Lint Rust Code

on:
  pull_request:
  workflow_dispatch:

jobs:
  lint_rust:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          components: clippy,rustfmt
          cache: true

      - name: Lint
        run: |
          make -C internal/envoyinit/rustformations lint
