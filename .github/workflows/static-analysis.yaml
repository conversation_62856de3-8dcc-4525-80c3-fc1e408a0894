name: Static Code Analysis

on:
  pull_request: { }
  merge_group:
    types: [checks_requested]

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  codegen:
    name: Generated Code
    runs-on: ubuntu-22.04
    timeout-minutes: 30
    steps:
    - name: Check out code into the Go module directory
      uses: actions/checkout@v4
    - name: Prep Go Runner
      uses: ./.github/actions/prep-go-runner
    - name: Generate Code
      run: make verify

  static-analysis:
    name: <PERSON><PERSON> Checks
    runs-on: ubuntu-22.04
    # The linter is intended to run quickly.
    # We define a 10-minute timeout on the linter config (.golangci.yaml) as well.
    # If we exceed this timeout, we should consider only running the linter on changed files.
    timeout-minutes: 10
    steps:
    - name: Check out code into the Go module directory
      uses: actions/checkout@v4
    - name: Prep Go Runner
      uses: ./.github/actions/prep-go-runner
    - uses: golangci/golangci-lint-action@v7
      # `make analyze` runs the linter with similar arguments to what we use here.
      # If this action fails, try running `make analyze` locally.
      with:
        version: v2.1.6
        args: --verbose --modules-download-mode=readonly --allow-parallel-runners
        skip-cache: true
        skip-save-cache: true
        only-new-issues: false
