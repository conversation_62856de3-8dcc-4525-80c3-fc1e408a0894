name: Scheduled Security Scan

on:
  # allow for version to be manually specified under actions page
  workflow_dispatch: {}
  #
  # Typically this job will execute on a weekly cadence
  # Right now, it reports results to a Solo.io specific Slack channel
  # During the ongoing donation process, we will want to find an appropriate place to report
  # the results. In the meantime, we are disabling this action, as there is no action taken
  # on the results
  #
  # schedule:
    # Monday 4am EST so as to hope for this to complete prior to a 9 AM check
    # - cron: "0 8 * * 1"

env:
  SLACK_DEBUG_TESTING: false   # when set to "true", send notifications to #slack-integration-testing.  Otherwise, post to #edge-team-bots
  GITHUB_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

jobs:
  scan-images:
    name: <PERSON><PERSON>an
    runs-on: ubuntu-22.04
    steps:
      - name: Install Trivy (latest)
        run: |
          TRIVY_VERSION=$(curl --silent "https://api.github.com/repos/aquasecurity/trivy/releases/latest" | grep '"tag_name":' | sed -E 's/.*"v([^"]+)".*/\1/') 
          echo Using Trivy v${TRIVY_VERSION}
          wget https://github.com/aquasecurity/trivy/releases/download/v${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.deb
          sudo dpkg -i trivy_${TRIVY_VERSION}_Linux-64bit.deb
      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
      - uses: ./.github/actions/prep-go-runner
      - name: Setup - gcloud / gsutil
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.GLOO_VULN_REPORTER }}'
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: solo-public
      - name: Run and Push Security Scan Files to Google Cloud Bucket
        env:
          SCAN_DIR: _output/scans
          IMAGE_REGISTRY: quay.io/solo-io
          # ON_LTS_UPDATE - bump version
          MIN_SCANNED_VERSION: 'v1.15.0' # ⚠️ you should also change docs-gen.yaml ⚠️
        run: |
          mkdir -p $SCAN_DIR
          make run-security-scan
          make publish-security-scan
      - name: Alert on workflow failure
        if: ${{ failure() }}
        run : |
          curl -X POST\
               -H 'Content-type: application/json'\
               --data '{"text":"Kgateway Vulnerability Scan has failed, visit https://github.com/kgateway-dev/kgateway/actions/runs/${{github.run_id}} to view logs."}'\
               ${{ env.SLACK_DEBUG_TESTING == true && secrets.SLACK_INTEGRATION_TESTING_WEBHOOK || secrets.EDGE_TEAM_BOTS_WEBHOOK }}
