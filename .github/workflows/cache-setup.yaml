name: Cache Setup

on:
  # We utilize this job to seed the GitHub action cache(s) for the LTS branch
  push:
    branches:
      - 'main'
      - 'v1.**.x'

jobs:
  setup-mod-cache:
    name: Setup Go Modules Cache
    runs-on: ubuntu-22.04
    timeout-minutes: 15
    steps:
    - name: Check out code into the Go module directory
      uses: actions/checkout@v4
    - name: Prep Go Runner
      uses: ./.github/actions/prep-go-runner