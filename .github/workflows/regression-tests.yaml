name: Regression Tests
on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  merge_group:
    types: [checks_requested]

env:
  VERSION: '1.0.0-ci1'
  GITHUB_TOKEN: ${{ github.token }} # necessary to pass upgrade tests

jobs:
  kube_gateway_api_conformance_tests:
    name: kubernetes gateway api conformance tests (${{matrix.image-variant}})
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    if: ${{ !github.event.pull_request.draft }}
    strategy:
      fail-fast: false
      matrix:
        kube-version: [ { node: 'v1.32.2@sha256:f226345927d7e348497136874b6d207e0b32cc52154ad8323129352923a3142f', kubectl: 'v1.32.2', kind: 'v0.27.0' } ]
        # use standard since other variants don't currently work with these tests
        # (see https://github.com/solo-io/solo-projects/issues/6094)
        image-variant:
          - standard
    steps:
    - uses: actions/checkout@v4
    - id: run-tests
      uses: ./.github/actions/kube-gateway-api-conformance-tests
