name: Run Conformance Tests

on:
  # TODO: Trigger on release events too.
  workflow_dispatch:
    inputs:
      image-variant:
        description: "The image variant to use."
        required: true
        type: choice
        options:
          - standard
          - distroless
      version:
        description: "Optional: Specify an existing kgateway release tag to deploy and test. Leave empty to use the default branch."
        required: false
        type: string

jobs:
  run-conformance-tests:
    runs-on: ubuntu-22.04
    env:
      IMAGE_REGISTRY: cr.kgateway.dev/kgateway-dev
    strategy:
      matrix:
        # TODO(tim): Avoid hardcoding versions here. It's a bit tricky based on
        # how this was setup and there's a limited # of dispatch inputs that GH
        # supports. We can revisit this later.
        kube-version:
        - node: 'v1.32.2@sha256:f226345927d7e348497136874b6d207e0b32cc52154ad8323129352923a3142f'
          kubectl: 'v1.32.2'
          kind: 'v0.27.0'
        image-variant:
          - ${{ inputs.image-variant }}
        version:
          - ${{ inputs.version }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Run Conformance Tests
        uses: ./.github/actions/kube-gateway-api-conformance-tests
