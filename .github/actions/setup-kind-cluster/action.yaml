name: Setup KinD Cluster

description: The action that will seed a KinD cluster.

inputs:
  cluster-name:
    required: true
    description: The name of the KinD cluster
  kind-version:
    required: true
    description: The version of KinD
  kind-node-version:
    required: true
    description: The Node version used by KinD
  kubectl-version:
    required: true
    description: The version of Kubectl
  helm-version:
    required: true
    description: The version of Helm
  istio-version:
    required: true
    description: The version of Istio
  kgateway-api-version:
    required: false
    default: "" # If this is undefined the setup-kind script has its own default value
    description: The version of the gateway-api to use
  localstack:
    required: false
    default: "false"
    description: Whether to install localstack
  agentgateway:
    required: false
    default: "false"
    description: Enable the agentgateway integration with kgateway

runs:
  using: "composite"
  steps:
    - uses: azure/setup-kubectl@v4
      id: kubectl
      with:
        version: ${{ inputs.kubectl-version }}
    - name: Setup test env
      shell: bash
      env:
        CLUSTER_NAME: ${{ inputs.cluster-name }}
        CLUSTER_NODE_VERSION: ${{ inputs.kind-node-version }}
        ISTIO_VERSION: ${{ inputs.istio-version }}
        CONFORMANCE_VERSION: ${{ inputs.kgateway-api-version }}
        LOCALSTACK: ${{ inputs.localstack }}
        AGENTGATEWAY: ${{ inputs.agentgateway }}
        # AI Tests rely on metal LB
        CONFORMANCE: true
      run: |
        ./hack/kind/setup-kind.sh
