name: Prep Go Runner

description: Common setup steps for Gloo actions

inputs:
  working-directory:
    description: 'directory to run setup steps in'
    required: false
    default: '.'

runs:
  using: "composite"
  steps:
    - name: Cancel Previous Actions
      uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # 0.12.1
      with:
        access_token: ${{ github.token }}
    - name: Free disk space
      shell: bash
      run: |
        echo "Before clearing disk space:"
        df -h
        docker system df -v

        # https://github.com/actions/runner-images/discussions/3242 github runners are bad at cleanup
        echo "Removing large packages"
        sudo apt-get remove -y '^dotnet-.*' || true
        sudo apt-get remove -y '^llvm-.*' || true
        sudo apt-get remove -y 'php.*' || true
        sudo apt-get remove -y '^mongodb-.*' || true
        sudo apt-get remove -y '^mysql-.*' || true
        sudo apt-get remove -y azure-cli google-chrome-stable firefox powershell mono-devel libgl1-mesa-dri || true
        sudo apt-get autoremove -y || true
        sudo apt-get clean -y || true
        echo "Done removing large packages"

        # Clean up pre-installed tools
        sudo rm -rf /usr/local/lib/android || true
        sudo rm -rf /usr/share/dotnet || true
        sudo rm -rf /usr/local/graalvm || true
        sudo rm -rf /opt/ghc || true
        sudo rm -rf /usr/local/.ghcup || true
        sudo rm -rf /usr/local/share/boost || true
        sudo rm -rf /usr/local/share/powershell || true
        sudo rm -rf /usr/local/share/chromium || true
        sudo rm -rf $AGENT_TOOLSDIRECTORY || true

        # Clean up images
        docker image rm node:18 || true
        docker image rm node:18-alpine || true
        docker image rm node:20 || true
        docker image rm node:20-alpine || true
        # remove the dangling images and containers
        docker images | tail -n +2 | awk '$1 == "<none>" {print $3}' | xargs --no-run-if-empty docker rmi
        docker ps -a | tail -n +2 | awk '$2 ~ "^[0-9a-f]+$" {print $1}' | xargs --no-run-if-empty docker rm --volumes=true

        echo "After clearing disk space:"
        df -h
        docker system df -v
    - name: Set up Go
      id: setup-go
      uses: actions/setup-go@f111f3307d8850f501ac008e886eec1fd1932a34 # v5.3.0
      with:
        # https://github.com/actions/setup-go/blob/main/action.yml
        go-version-file: ${{ inputs.working-directory }}/go.mod
        # Using the go-version-file, we will build with the latest go patch version
        check-latest: true
        # Caching in setup-go is limited, so we opt to use the more configurable https://github.com/actions/cache
        cache: false
    - name: Go Cache Paths
      id: go-cache-paths
      shell: bash
      run: |
        echo "go-mod=$(go env GOMODCACHE)" >> $GITHUB_OUTPUT
    - name: Cache
      uses: actions/cache@v4
      id: cache
      with:
        # https://github.com/actions/cache/blob/main/examples.md#go---modules
        path: |
          ${{ steps.go-cache-paths.outputs.go-mod }}
        key: ${{ runner.os }}-go-${{ steps.setup-go.outputs.go-version }}-${{ hashFiles('**/go.sum') }}
    - name: Install Dependencies
      if: steps.cache.outputs.cache-hit != 'true'
      shell: bash
      run: make mod-download
