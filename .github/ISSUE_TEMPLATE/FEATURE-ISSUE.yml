name: Feature Request
description: Suggest an idea for this project
labels: ["Type: Enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        **Note**: This is a public repo. Be sure to obscure or redact any sensitive information.
  - type: input
    id: version
    attributes:
      label: kgateway version
      description: What version of kgateway are you using? Please specify a specific patch version
      placeholder: eg v1.14.2
    validations:
      required: true
  - type: textarea
    id: problem-details
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is
      placeholder: Ex. I'm always frustrated when [...]
  - type: textarea
    id: solution-details
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen
      placeholder: Ex. kgateway should automatically [...]
  - type: textarea
    id: alternatives-details
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered
      placeholder: Ex. X feature can be used as a workaround but [...]
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context, links, screenshots etc. related to the feature request
