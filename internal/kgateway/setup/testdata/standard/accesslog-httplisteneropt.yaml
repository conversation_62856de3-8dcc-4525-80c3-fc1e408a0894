kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: http-gw-for-test
  namespace: gwtest
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: All
    - protocol: HTTP
      port: 8081
      name: other
      allowedRoutes:
        namespaces:
          from: All
---
apiVersion: v1
kind: Service
metadata:
  name: reviews
  namespace: gwtest
  labels:
    app: reviews
    service: reviews
spec:
  ports:
    - name: http
      port: 8080
      targetPort: 8080
  selector:
    app: reviews
---
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: reviews-slice
  namespace: gwtest
  labels:
    kubernetes.io/service-name: reviews
    app: reviews
    service: reviews
addressType: IPv4
endpoints:
  - addresses:
      - ***********
    conditions:
      ready: true
    nodeName: worker
    targetRef:
      kind: Pod
      name: reviews-1
      namespace: default
ports:
  - name: http
    port: 8080
    protocol: TCP
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: reviews
  namespace: gwtest
spec:
  parentRefs:
    - name: http-gw-for-test
  hostnames:
    - "www.example.com"
  rules:
    - backendRefs:
        - name: reviews
          port: 8080
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: HTTPListenerPolicy
metadata:
  name: accesslog
  namespace: gwtest
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: http-gw-for-test
  accessLog:
    - fileSink:
        path: /dev/stdout
        jsonFormat:
          start_time: "%START_TIME%"
          method: "%REQ(X-ENVOY-ORIGINAL-METHOD?:METHOD)%"
          path: "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%"
          protocol: "%PROTOCOL%"
          response_code: "%RESPONSE_CODE%"
          response_flags: "%RESPONSE_FLAGS%"
          bytes_received: "%BYTES_RECEIVED%"
          bytes_sent: "%BYTES_SENT%"
          total_duration: "%DURATION%"
          resp_upstream_service_time: "%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%"
          req_x_forwarded_for: "%REQ(X-FORWARDED-FOR)%"
          user_agent: "%REQ(USER-AGENT)%"
          request_id: "%REQ(X-REQUEST-ID)%"
          authority: "%REQ(:AUTHORITY)%"
          upstreamHost: "%UPSTREAM_HOST%"
          upstreamCluster: "%UPSTREAM_CLUSTER%"
