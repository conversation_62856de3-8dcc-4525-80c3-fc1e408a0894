clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_kubernetes_443
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_gwtest_reviews-v1_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_gwtest_reviews-v2_8080
  type: EDS
listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - disabled: true
          name: ratelimit/local
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
            statPrefix: http_local_rate_limiter
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
routes:
- ignorePortInHostMatching: true
  name: listener~8080
  virtualHosts:
  - domains:
    - www.example.com
    name: listener~8080~www_example_com
    routes:
    - match:
        prefix: /
      name: listener~8080~www_example_com-route-0-httproute-my-route-gwtest-0-0-matcher-0
      route:
        cluster: kube_gwtest_reviews-v1_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        prefix: /
      name: listener~8080~www_example_com-route-1-httproute-my-route-gwtest-1-0-matcher-0
      route:
        cluster: kube_gwtest_reviews-v2_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
      typedPerFilterConfig:
        ratelimit/local:
          '@type': type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
          filterEnabled:
            defaultValue:
              numerator: 100
            runtimeKey: local_rate_limit_enabled
          filterEnforced:
            defaultValue:
              numerator: 100
            runtimeKey: local_rate_limit_enforced
          statPrefix: http_local_rate_limiter
          tokenBucket:
            fillInterval: 30s
            maxTokens: 100
            tokensPerFill: 1
