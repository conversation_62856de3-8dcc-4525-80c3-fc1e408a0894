kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: http-gw-for-test
  namespace: gwtest
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: All
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: route-to-backend
  namespace: gwtest
spec:
  parentRefs:
    - name: http-gw-for-test
  hostnames:
    - "test"
  rules:
    - matches:
        - path:
            type: Exact
            value: /anthropic
      backendRefs:
        - name: anthropic
          kind: Backend
          group: gateway.kgateway.dev
          filters:
            - type: ExtensionRef
              extensionRef:
                group: gateway.kgateway.dev
                kind: TrafficPolicy
                name: route-test
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: route-test
  namespace: gwtest
spec:
  ai:
    defaults:
    - field: "temperature"
      value: "0.5"
      override: true
    - field: "top_p"
      value: "0.1"
    - field: "presence_penalty"
      value: "0.7"
      override: false
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: anthropic
  namespace: gwtest
spec:
  type: AI
  ai:
    llm:
      provider:
        anthropic:
          authToken:
            kind: "Passthrough"
