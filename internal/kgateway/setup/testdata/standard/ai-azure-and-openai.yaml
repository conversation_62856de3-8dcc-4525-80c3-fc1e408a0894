kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: http-gw-for-test
  namespace: gwtest
spec:
  gatewayClassName: kgateway
  listeners:
    - protocol: HTTP
      port: 8080
      name: http
      allowedRoutes:
        namespaces:
          from: All
---
apiVersion: v1
kind: Secret
metadata:
  name: openai-secret
  namespace: gwtest
type: Opaque
data:
  Authorization: bXlzZWNyZXRrZXk=
---
apiVersion: v1
kind: Secret
metadata:
  name: azure-secret
  namespace: gwtest
type: Opaque
data:
  Authorization: bXlzZWNyZXRrZXk=
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: route-to-backend
  namespace: gwtest
spec:
  parentRefs:
    - name: http-gw-for-test
  hostnames:
    - "test"
  rules:
    - matches:
        - path:
            type: Exact
            value: /openai
      backendRefs:
        - name: openai
          kind: Backend
          group: gateway.kgateway.dev
    - matches:
        - path:
            type: Exact
            value: /azure
      backendRefs:
        - name: azure
          kind: Backend
          group: gateway.kgateway.dev
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: openai
  namespace: gwtest
spec:
  type: AI
  ai:
    llm:
      provider:
        openai:
          authToken:
            kind: "SecretRef"
            secretRef:
              name: openai-secret
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: Backend
metadata:
  labels:
    app: kgateway
  name: azure
  namespace: gwtest
spec:
  type: AI
  ai:
    llm:
      provider:
        azureopenai:
          endpoint: ai-gateway.openai.azure.com
          deploymentName: gpt-4o-mini
          apiVersion: 2024-02-15-preview
          authToken:
            kind: "SecretRef"
            secretRef:
              name: azure-secret