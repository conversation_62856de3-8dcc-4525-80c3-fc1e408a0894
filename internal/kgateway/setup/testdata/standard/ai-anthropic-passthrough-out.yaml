clusters:
- loadAssignment:
    clusterName: ai_ext_proc_uds_cluster
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            pipe:
              path: '@kgateway-ai-sock'
  name: ai_ext_proc_uds_cluster
  type: STATIC
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions: {}
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_kubernetes_443
  type: EDS
- connectTimeout: 5s
  dnsLookupFamily: V4_PREFERRED
  loadAssignment:
    clusterName: backend_gwtest_anthropic_0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: api.anthropic.com
              portValue: 443
          hostname: api.anthropic.com
        metadata:
          filterMetadata:
            envoy.transport_socket_match:
              tls: default
            io.solo.transformation:
              auth_token: ""
  metadata: {}
  name: backend_gwtest_anthropic_0
  transportSocketMatches:
  - match:
      tls: default
    name: tls
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        autoHostSni: true
        commonTlsContext:
          combinedValidationContext:
            defaultValidationContext: {}
            validationContextSdsSecretConfig:
              name: SYSTEM_CA_CERT
  - match:
      tls: skipverification
    name: tls
    transportSocket:
      name: envoy.transport_sockets.tls
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        autoHostSni: true
        commonTlsContext: {}
  - match: {}
    name: plaintext
    transportSocket:
      name: envoy.transport_sockets.raw_buffer
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.transport_sockets.raw_buffer.v3.RawBuffer
  type: STRICT_DNS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      commonHttpProtocolOptions:
        idleTimeout: 30s
      explicitHttpConfig:
        httpProtocolOptions: {}
      httpFilters:
      - name: io.kgateway.wait
        typedConfig:
          '@type': type.googleapis.com/envoy.config.filter.http.upstream_wait.v2.UpstreamWaitFilterConfig
      - name: ai.policy.transformation.kgateway.io
        typedConfig:
          '@type': type.googleapis.com/envoy.api.v2.filter.http.FilterTransformations
      - name: ai.backend.transformation.kgateway.io
        typedConfig:
          '@type': type.googleapis.com/envoy.api.v2.filter.http.FilterTransformations
      - name: envoy.filters.http.upstream_codec
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: ai.extproc.kgateway.io
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
            grpcService:
              envoyGrpc:
                clusterName: ai_ext_proc_uds_cluster
            messageTimeout: 5s
            metadataOptions:
              forwardingNamespaces:
                typed:
                - envoy.filters.ai.solo.io
                untyped:
                - io.solo.transformation
                - envoy.filters.ai.solo.io
              receivingNamespaces:
                untyped:
                - ai.kgateway.io
            processingMode:
              requestBodyMode: STREAMED
              requestHeaderMode: SEND
              requestTrailerMode: SKIP
              responseBodyMode: STREAMED
              responseHeaderMode: SEND
              responseTrailerMode: SKIP
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
routes:
- ignorePortInHostMatching: true
  name: listener~8080
  virtualHosts:
  - domains:
    - test
    name: listener~8080~test
    routes:
    - match:
        path: /anthropic
      name: listener~8080~test-route-0-httproute-route-to-backend-gwtest-0-0-matcher-0
      route:
        autoHostRewrite: true
        cluster: backend_gwtest_anthropic_0
      typedPerFilterConfig:
        ai.backend.transformation.kgateway.io:
          '@type': type.googleapis.com/envoy.api.v2.filter.http.RouteTransformations
          transformations:
          - requestMatch:
              requestTransformation:
                logRequestResponseInfo: false
                transformationTemplate:
                  headers:
                    :path:
                      text: /v1/messages
                    x-api-key:
                      text: '{% if host_metadata("auth_token") != "" %}{{host_metadata("auth_token")}}{%
                        else %}{{dynamic_metadata("auth_token","ai.kgateway.io")}}{%
                        endif %}'
                  mergeJsonKeys:
                    jsonKeys:
                      model:
                        tmpl:
                          text: '{% if host_metadata("model") != "" %}"{{host_metadata("model")}}"{%
                            else %}"{{model}}"{% endif %}'
        ai.extproc.kgateway.io:
          '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExtProcPerRoute
          overrides:
            grpcInitialMetadata:
            - key: x-llm-provider
              value: anthropic
            - key: x-request-id
              value: '%REQ(X-REQUEST-ID)%'
        ai.policy.transformation.kgateway.io:
          '@type': type.googleapis.com/envoy.api.v2.filter.http.RouteTransformations
          transformations:
          - requestMatch:
              requestTransformation:
                logRequestResponseInfo: false
                transformationTemplate:
                  mergeJsonKeys:
                    jsonKeys:
                      presence_penalty:
                        tmpl:
                          text: '{{ default(presence_penalty, "0.7") }}'
                      temperature:
                        tmpl:
                          text: '"0.5"'
                      top_p:
                        tmpl:
                          text: '{{ default(top_p, "0.1") }}'
