clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_kubernetes_443
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_gwtest_log-test_50051
  type: EDS
  typedExtensionProtocolOptions:
    envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
      '@type': type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
      explicitHttpConfig:
        http2ProtocolOptions: {}
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_gwtest_reviews_8080
  type: EDS
endpoints:
- clusterName: kube_gwtest_log-test_50051
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: ***********
            portValue: 50051
      loadBalancingWeight: 1
    loadBalancingWeight: 1
- clusterName: kube_gwtest_reviews_8080
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: ***********
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        accessLog:
        - filter:
            headerFilter:
              header:
                name: x-my-cool-test-filter
                stringMatch:
                  exact: test
          name: envoy.access_loggers.http_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: kube_gwtest_log-test_50051
              logName: test-accesslog-service
              transportApiVersion: V3
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8081
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        accessLog:
        - filter:
            headerFilter:
              header:
                name: x-my-cool-test-filter
                stringMatch:
                  exact: test
          name: envoy.access_loggers.http_grpc
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.access_loggers.grpc.v3.HttpGrpcAccessLogConfig
            commonConfig:
              grpcService:
                envoyGrpc:
                  clusterName: kube_gwtest_log-test_50051
              logName: test-accesslog-service
              transportApiVersion: V3
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8081
        statPrefix: http
        useRemoteAddress: true
    name: listener~8081
  name: listener~8081
routes:
- ignorePortInHostMatching: true
  name: listener~8080
  virtualHosts:
  - domains:
    - www.example.com
    name: listener~8080~www_example_com
    routes:
    - match:
        prefix: /
      name: listener~8080~www_example_com-route-0-httproute-reviews-gwtest-0-0-matcher-0
      route:
        cluster: kube_gwtest_reviews_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
- ignorePortInHostMatching: true
  name: listener~8081
  virtualHosts:
  - domains:
    - www.example.com
    name: listener~8081~www_example_com
    routes:
    - match:
        prefix: /
      name: listener~8081~www_example_com-route-0-httproute-reviews-gwtest-0-0-matcher-0
      route:
        cluster: kube_gwtest_reviews_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
