kind: Gateway
apiVersion: gateway.networking.k8s.io/v1
metadata:
  name: http-gw-for-test
  namespace: gwtest
spec:
  gatewayClassName: kgateway
  listeners:
  - protocol: HTTP
    port: 8080
    name: http
    allowedRoutes:
      namespaces:
        from: All
---
apiVersion: v1
kind: Service
metadata:
  name: reviews
  namespace: gwtest
  labels:
    app: reviews
    service: reviews
spec:
  ports:
    - name: http
      port: 8080
      targetPort: 8080
  selector:
    app: reviews
---
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: reviews-slice
  namespace: gwtest
  labels:
    kubernetes.io/service-name: reviews
    app: reviews
    service: reviews
addressType: IPv4
endpoints:
  - addresses:
      - ***********
    conditions:
      ready: true
    nodeName: worker
    targetRef:
      kind: Pod
      name: reviews-1
      namespace: default
ports:
  - name: http
    port: 8080
    protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: ratings
  namespace: gwtest
  labels:
    app: ratings
    service: ratings
spec:
  ports:
    - name: http
      port: 8080
      targetPort: 8080
  selector:
    app: ratings
---
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: ratings-slice
  namespace: gwtest
  labels:
    kubernetes.io/service-name: ratings
    app: ratings
    service: ratings
addressType: IPv4
endpoints:
  - addresses:
      - ***********
    conditions:
      ready: true
    nodeName: worker
    targetRef:
      kind: Pod
      name: ratings-1
      namespace: default
ports:
  - name: http
    port: 8080
    protocol: TCP
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: my-reviews-route
  namespace: gwtest
spec:
  parentRefs:
    - name: http-gw-for-test
  hostnames:
    - "www.example.com"
  rules:
    - backendRefs:
        - name: reviews
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /reviews
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: my-ratings-route
  namespace: gwtest
spec:
  parentRefs:
    - name: http-gw-for-test
  hostnames:
    - "www.example.com"
  rules:
    - backendRefs:
        - name: ratings
          port: 8080
      matches:
      - path:
          type: PathPrefix
          value: /ratings
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: gw-route-rl-policy
  namespace: gwtest
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: http-gw-for-test
  rateLimit:
    local:
      tokenBucket:
        maxTokens: 100
        tokensPerFill: 1
        fillInterval: 30s
---
apiVersion: gateway.kgateway.dev/v1alpha1
kind: TrafficPolicy
metadata:
  name: route-rl-policy
  namespace: gwtest
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: my-ratings-route
  rateLimit:
    local: {}