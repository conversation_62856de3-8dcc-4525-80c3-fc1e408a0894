clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_kubernetes_443
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_gwtest_reviews_8080
  type: EDS
endpoints:
- clusterName: kube_gwtest_reviews_8080
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: ***********
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: r1
      subZone: r1z2s3
      zone: r1z2
listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - disabled: true
          name: transformation
          typedConfig:
            '@type': type.googleapis.com/envoy.api.v2.filter.http.FilterTransformations
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
routes:
- ignorePortInHostMatching: true
  name: listener~8080
  typedPerFilterConfig:
    transformation:
      '@type': type.googleapis.com/envoy.api.v2.filter.http.RouteTransformations
      transformations:
      - requestMatch:
          responseTransformation:
            transformationTemplate:
              body:
                text: '{{ body }}'
              headersToAppend:
              - key: original-greatness
                value:
                  text: '{{request_headers("x-greatness")}}'
  virtualHosts:
  - domains:
    - www.example-gateway-only.com
    name: listener~8080~www_example-gateway-only_com
    routes:
    - match:
        prefix: /
      name: listener~8080~www_example-gateway-only_com-route-0-httproute-happypath-gwtest-0-0-matcher-0
      route:
        cluster: kube_gwtest_reviews_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
  - domains:
    - www.example-route-override.com
    name: listener~8080~www_example-route-override_com
    routes:
    - match:
        prefix: /
      name: listener~8080~www_example-route-override_com-route-0-httproute-happypath-override-gwtest-0-0-matcher-0
      route:
        cluster: kube_gwtest_reviews_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
      typedPerFilterConfig:
        transformation:
          '@type': type.googleapis.com/envoy.api.v2.filter.http.RouteTransformations
          transformations:
          - requestMatch:
              requestTransformation:
                transformationTemplate:
                  headersToAppend:
                  - key: x-greatness
                    value:
                      text: '{{ length(headers(":path")) }}'
                  parseBodyBehavior: DontParse
                  passthrough: {}
