{{- $endpointPicker := .Values.inferenceExtension.endpointPicker }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}
subjects:
- kind: ServiceAccount
  name: {{ .Release.Name }}
  namespace: {{ $endpointPicker.poolNamespace }}
roleRef:
  kind: ClusterRole
  name: kgateway-inference-extension
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    app.kubernetes.io/component: endpoint-picker
    app.kubernetes.io/name: {{ .Release.Name }}
    app.kubernetes.io/instance: kgateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
    spec:
      serviceAccountName: {{ .Release.Name }}
      containers:
      - name: endpoint-picker
        args:
          - -poolName
          - {{ $endpointPicker.poolName }}
          - -v
          - "4"
          - -grpcPort
          - "9002"
          - -grpcHealthPort
          - "9003"
        env:
        - name: USE_STREAMING
          value: "true"
        image: "registry.k8s.io/gateway-api-inference-extension/epp:v0.2.0"
        imagePullPolicy: IfNotPresent
        ports:
          - containerPort: 9002
          - containerPort: 9003
          - name: metrics
            containerPort: 9090
        livenessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
        readinessProbe:
          grpc:
            port: 9003
            service: inference-extension
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}
  labels:
    app.kubernetes.io/component: endpoint-picker
    app.kubernetes.io/name: {{ .Release.Name }}
    app.kubernetes.io/instance: kgateway
spec:
  selector:
    app: {{ .Release.Name }}
  ports:
    - name: grpc
      protocol: TCP
      port: 9002
      targetPort: 9002
    - name: metrics
      protocol: TCP
      port: 9090
      targetPort: 9090
  type: ClusterIP
