# These values represent configurable values for the dynamic inference extension chart
# They are not intended to be actual "defaults," rather they are just placeholder values
# meant to allow rendering of the chart/template, as the real values will come from:
# * The `InferencePool` resource driving the inference extension provisioning
# * A (possibly merged) GatewayParameters object translated to helm values
# The actual defaults for these values should come from the "default GatewayParameters" object
# See: (install/helm/kgateway/templates/gatewayparameters.yaml)

inferenceExtension:
  endpointPicker:
    poolName: default
    poolNamespace: default
