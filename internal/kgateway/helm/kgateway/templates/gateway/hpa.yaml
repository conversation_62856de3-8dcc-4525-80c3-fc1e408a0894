{{- if .Values.gateway.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "kgateway.gateway.fullname" . }}
  minReplicas: {{ .Values.gateway.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.gateway.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.gateway.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          averageUtilization: {{ .Values.gateway.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.gateway.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          averageUtilization: {{ .Values.gateway.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
