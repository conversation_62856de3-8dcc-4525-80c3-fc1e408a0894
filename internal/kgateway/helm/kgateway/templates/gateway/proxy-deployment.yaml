{{- $gateway := .Values.gateway }}
{{- if not $gateway.agentGateway.enabled }}
{{- $statsConfig := $gateway.stats }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
spec:
  {{- if not $gateway.autoscaling.enabled }}
  replicas: {{ $gateway.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "kgateway.gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
      {{- with $gateway.extraPodAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if $statsConfig.enabled }}
        prometheus.io/path: /metrics
        prometheus.io/port: "9091"
        prometheus.io/scrape: "true"
      {{- end}}
      {{- if (($gateway.aiExtension).enabled) }}
      {{- if $gateway.aiExtension.stats }}
        solo.io/ai-stats-config-hash: {{ sha256sum $gateway.aiExtension.stats | trunc 64 }}
      {{- end }}
      {{- end}}
      labels:
        {{- include "kgateway.gateway.constLabels" . | nindent 8 }}
        {{- include "kgateway.gateway.selectorLabels" . | nindent 8 }}
        {{- with $gateway.extraPodLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with $gateway.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "kgateway.gateway.fullname" . }}
      {{- if $gateway.podSecurityContext }}
      securityContext:
        {{- toYaml $gateway.podSecurityContext | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        {{- if $gateway.securityContext }}
        securityContext:
          {{- toYaml $gateway.securityContext | nindent 10 }}
        {{- end }}
        args:
        - "--disable-hot-restart"
        - "--service-node"
        - $(POD_NAME).$(POD_NAMESPACE)
        {{- with $gateway.logLevel }}
        - "--log-level"
        - "{{ . }}"
        {{- end }}{{/* with $gateway.logLevel */}}
        {{- with $gateway.componentLogLevel }}
        - "--component-log-level"
        - "{{ . }}"
        {{- end }}{{/* with $gateway.componentLogLevel */}}
        image: "{{ template "kgateway.gateway.image" $gateway.image }}"
        imagePullPolicy: {{ $gateway.image.pullPolicy }}
        volumeMounts:
        - mountPath: /etc/envoy
          name: envoy-config
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: ENVOY_UID
          value: "0"
        ports:
        {{- range $p := $gateway.ports }}
        - name: {{ $p.name }}
          protocol: {{ $p.protocol }}
          containerPort: {{ $p.targetPort }}
        {{- end }}
        {{- if $statsConfig.enabled }}
        - name: http-monitoring
          containerPort: 9091
        {{- end }}
{{- if $gateway.readinessProbe }}
        readinessProbe:
{{ toYaml $gateway.readinessProbe | indent 10}}
{{- end }}{{/*if $gateway.readinessProbe*/}}
{{- if $gateway.livenessProbe }}
        livenessProbe:
{{ toYaml $gateway.livenessProbe | indent 10}}
{{- end }}{{/*if $gateway.livenessProbe*/}}
{{- if ($gateway.gracefulShutdown).enabled }}
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - wget --post-data "" -O /dev/null 127.0.0.1:19000/healthcheck/fail; sleep {{ $gateway.gracefulShutdown.sleepTimeSeconds | default "10" }}
{{- end}}{{/*if ($gateway.gracefulShutdown).enabled */}}
{{- if $gateway.resources }}
        resources:
          {{- toYaml $gateway.resources | nindent 10 }}
{{- end }} {{/* if $gateway.resources */}}
{{- if and $gateway.istio.enabled ($gateway.sdsContainer).image }}
      - name: sds
        image: "{{ template "kgateway.gateway.image" $gateway.sdsContainer.image }}"
        {{- if $gateway.sdsContainer.image.pullPolicy }}
        imagePullPolicy: {{ $gateway.sdsContainer.image.pullPolicy }}
        {{- end }}
        env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: ISTIO_MTLS_SDS_ENABLED
            value: "true"
          - name: LOG_LEVEL
            value: {{ $gateway.sdsContainer.sdsBootstrap.logLevel }}
        ports:
          - containerPort: 8234
            name: sds
            protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 3
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 8234
          timeoutSeconds: 1
        {{- if $gateway.sdsContainer.resources }}
        resources:
          {{- toYaml $gateway.sdsContainer.resources | nindent 10 }}
        {{- end }}
        {{- if $gateway.sdsContainer.securityContext }}
        securityContext:
          {{- toYaml $gateway.sdsContainer.securityContext | nindent 10 }}
        {{- end }}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /etc/envoy
            name: envoy-config
{{- end }} {{/* if $gateway.istio.enabled */}}
{{- if and $gateway.istio.enabled ($gateway.istioContainer).image }}
          - mountPath: /etc/istio-certs/
            name: istio-certs
      - name: istio-proxy
        image: "{{ template "kgateway.gateway.image" $gateway.istioContainer.image }}"
        {{- if $gateway.istioContainer.image.pullPolicy }}
        imagePullPolicy: {{ $gateway.istioContainer.image.pullPolicy }}
        {{- end }}
        args:
          - proxy
          - sidecar
          - --domain
          - $(POD_NAMESPACE).svc.cluster.local
          - --configPath
          - /etc/istio/proxy
          - --serviceCluster
          - istio-proxy-prometheus
          - --drainDuration
          - 45s
          - --parentShutdownDuration
          - 1m0s
          - --proxyLogLevel={{ $gateway.istioContainer.logLevel }}
          - --proxyComponentLogLevel=misc:error
          - --connectTimeout
          - 10s
          - --controlPlaneAuthPolicy
          - NONE
          - --dnsRefreshRate
          - 300s
          - --controlPlaneBootstrap=false
        env:
          - name: OUTPUT_CERTS
            value: "/etc/istio-certs"
          - name: JWT_POLICY
            value: third-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: CA_ADDR
            value: {{ $gateway.istioContainer.istioDiscoveryAddress }}
          - name: ISTIO_META_MESH_ID
            value: {{ $gateway.istioContainer.istioMetaMeshId }}
          - name: ISTIO_META_CLUSTER_ID
            value: {{ $gateway.istioContainer.istioMetaClusterId }}
          - name: PROXY_CONFIG
            value: |
              {"discoveryAddress": {{ $gateway.istioContainer.istioDiscoveryAddress }} }
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: INSTANCE_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
          - name: HOST_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: ISTIO_META_POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: ISTIO_META_CONFIG_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: DISABLE_ENVOY
            value: "true"
        {{- if $gateway.istioContainer.resources }}
        resources:
          {{- toYaml $gateway.istioContainer.resources | nindent 10 }}
        {{- end }}
        {{- if $gateway.istioContainer.securityContext }}
        securityContext:
          {{- toYaml $gateway.istioContainer.securityContext | nindent 10 }}
        {{- end }}
        volumeMounts:
          - mountPath: /var/run/secrets/istio
            name: istiod-ca-cert
          - mountPath: /etc/istio/proxy
            name: istio-envoy
          - mountPath: /etc/istio-certs/
            name: istio-certs
          - mountPath: /var/run/secrets/tokens
            name: istio-token
          - mountPath: /var/run/secrets/credential-uds
            name: credential-socket
          - mountPath: /var/run/secrets/workload-spiffe-uds
            name: workload-socket
          - mountPath: /var/run/secrets/workload-spiffe-credentials
            name: workload-certs
{{- end }} {{/* if $gateway.istio.enabled */}}
{{- if and ($gateway.aiExtension).enabled ($gateway.aiExtension).image }}
      - name: kgateway-ai-extension
        image: "{{ template "kgateway.gateway.image" $gateway.aiExtension.image }}"
        {{- if $gateway.aiExtension.image.pullPolicy }}
        imagePullPolicy: {{ $gateway.aiExtension.image.pullPolicy }}
        {{- end }}
        env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
{{- if $gateway.aiExtension.env }}
{{ toYaml $gateway.aiExtension.env | indent 10 }}
{{- end }} {{/* if $gateway.aiExtension.env */}}
{{- if $gateway.aiExtension.ports }}
        ports:
{{ toYaml $gateway.aiExtension.ports | indent 10 }}
{{- end }} {{/* if $gateway.aiExtension.ports */}}
        {{- if $gateway.aiExtension.resources }}
        resources:
          {{- toYaml $gateway.aiExtension.resources | nindent 10 }}
        {{- end }}
        {{- if $gateway.aiExtension.securityContext }}
        securityContext:
          {{- toYaml $gateway.aiExtension.securityContext | nindent 10 }}
        {{- end }}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /var/run/stats
            name: ai-stats-config
{{- end }} {{/* if (($gateway.aiExtension).enabled) */}}
      {{- with $gateway.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $gateway.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $gateway.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if $gateway.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ $gateway.terminationGracePeriodSeconds }}
      {{- end }}
      volumes:
      - configMap:
          name: {{ include "kgateway.gateway.fullname" . }}
        name: envoy-config
{{- if (($gateway.aiExtension).enabled) }}
      - configMap:
          name: {{ include "kgateway.gateway.fullname" . }}-ai-stats-config
        name: ai-stats-config
{{- end }} {{/* if (($gateway.aiExtension).enabled) */}}
{{- if $gateway.istio.enabled }}
      - emptyDir:
          medium: Memory
        name: istio-certs
      - configMap:
          defaultMode: 420
          name: istio-ca-root-cert
        name: istiod-ca-cert
      - emptyDir:
          medium: Memory
        name: istio-envoy
      - name: istio-token
        projected:
          defaultMode: 420
          sources:
            - serviceAccountToken:
                audience: istio-ca
                expirationSeconds: 43200
                path: istio-token
      - name: credential-socket
        emptyDir: {}
      - name: workload-socket
        emptyDir: {}
      - name: workload-certs
        emptyDir: {}
{{- end }} {{/* if $gateway.istio.enabled */}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  {{- with (($gateway.serviceAccount).extraAnnotations) }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
    {{- with (($gateway.serviceAccount).extraLabels) }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
automountServiceAccountToken: false
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  {{- with $gateway.service.extraAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
    {{- with $gateway.service.extraLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ $gateway.service.type }}
  {{- with $gateway.service.clusterIP }}
  clusterIP: {{ . }}
  {{- end }}
  ports:
  {{- range $p := $gateway.ports }}
  - name: {{ $p.name }}
    protocol: {{ $p.protocol }}
    targetPort: {{ $p.targetPort }}
    port: {{ $p.port }}
    {{- if $p.nodePort }}
    nodePort: {{ $p.nodePort }}
    {{- end }}
  {{- end }}
  selector:
    {{- include "kgateway.gateway.selectorLabels" . | nindent 4 }}
---
{{- if (($gateway.aiExtension).enabled) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}-ai-stats-config
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
{{- if $gateway.aiExtension.stats }}
data:
  stats.json: {{ $gateway.aiExtension.stats }}
{{- end}}
{{- end}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
data:
  envoy.yaml: |
    admin:
      address:
        socket_address: { address: 127.0.0.1, port_value: 19000 }
    layered_runtime:
      layers:
      - name: static_layer
        static_layer:
          envoy.restart_features.use_eds_cache_for_ads: true
      - name: admin_layer
        admin_layer: {}
    node:
      cluster: {{ include "kgateway.gateway.fullname" . }}.{{ .Release.Namespace }}
      metadata:
        role: kgateway-kube-gateway-api~{{ $gateway.gatewayNamespace }}~{{ $gateway.gatewayName | default (include "kgateway.gateway.fullname" .) }}
    static_resources:
      listeners:
      - name: readiness_listener
        address:
          socket_address: { address: 0.0.0.0, port_value: 8082 }
        filter_chains:
          - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                stat_prefix: ingress_http
                codec_type: AUTO
                route_config:
                  name: main_route
                  virtual_hosts:
                    - name: local_service
                      domains: ["*"]
                      routes:
                        - match:
                            path: "/ready"
                            headers:
                              - name: ":method"
                                string_match:
                                  exact: GET
                          route:
                            cluster: admin_port_cluster
                http_filters:
{{- if $gateway.readinessProbe }}
                  - name: envoy.filters.http.health_check
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.health_check.v3.HealthCheck
                      pass_through_mode: false
                      headers:
                      - name: ":path"
                        exact_match: "/envoy-hc"
{{- end }}{{/*if $gateway.readinessProbe*/}}
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
{{- if $statsConfig.enabled }}
      - name: prometheus_listener
        address:
          socket_address:
            address: 0.0.0.0
            port_value: 9091
        filter_chains:
          - filters:
              - name: envoy.filters.network.http_connection_manager
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                  codec_type: AUTO
                  stat_prefix: prometheus
                  route_config:
                    name: prometheus_route
                    virtual_hosts:
                      - name: prometheus_host
                        domains:
                          - "*"
                        routes:
                          - match:
                              path: "/ready"
                              headers:
                                - name: ":method"
                                  exact_match: GET
                            route:
                              cluster: admin_port_cluster
                          - match:
                              prefix: "/metrics"
                              headers:
                                - name: ":method"
                                  exact_match: GET
                            route:
                              prefix_rewrite: {{ $statsConfig.routePrefixRewrite }}
                              cluster: admin_port_cluster
                        {{- if $statsConfig.enableStatsRoute}}
                          - match:
                              prefix: "/stats"
                              headers:
                                - name: ":method"
                                  exact_match: GET
                            route:
                              prefix_rewrite: {{ $statsConfig.statsPrefixRewrite }}
                              cluster: admin_port_cluster
                        {{- end }}
                  http_filters:
                    - name: envoy.filters.http.router
                      typed_config:
                        "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
{{- end }} {{/* if $gateway.stats.enabled */}}
      clusters:
        - name: xds_cluster
          alt_stat_name: xds_cluster
          connect_timeout: 5.000s
          load_assignment:
            cluster_name: xds_cluster
            endpoints:
            - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: {{ $gateway.xds.host }}
                      port_value: {{ $gateway.xds.port }}
          typed_extension_protocol_options:
            envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
              "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
              explicit_http_config:
                http2_protocol_options: {}
          upstream_connection_options:
            tcp_keepalive:
              keepalive_time: 10
          type: STRICT_DNS
          respect_dns_ttl: true
        - name: admin_port_cluster
          connect_timeout: 5.000s
          type: STATIC
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: admin_port_cluster
            endpoints:
            - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 19000
        {{- if $gateway.istio.enabled }}
        - name: gateway_proxy_sds
          connect_timeout: 0.25s
          http2_protocol_options: {}
          load_assignment:
            cluster_name: gateway_proxy_sds
            endpoints:
              - lb_endpoints:
                - endpoint:
                    address:
                      socket_address:
                        address: 127.0.0.1
                        port_value: 8234
        {{- end }} {{/* if $gateway.istio.enabled */}}
    dynamic_resources:
      ads_config:
        transport_api_version: V3
        api_type: GRPC
        rate_limit_settings: {}
        grpc_services:
        - envoy_grpc:
            cluster_name: xds_cluster
      cds_config:
        resource_api_version: V3
        ads: {}
      lds_config:
        resource_api_version: V3
        ads: {}
{{- end }} {{/* if not $gateway.agentGateway.enabled */}}
---