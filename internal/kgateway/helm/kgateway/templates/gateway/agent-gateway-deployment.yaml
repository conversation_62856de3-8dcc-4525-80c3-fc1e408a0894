{{- $gateway := .Values.gateway }}
{{- if $gateway.agentGateway.enabled }}

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
spec:
  {{- if not $gateway.autoscaling.enabled }}
  replicas: {{ $gateway.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
          {{- include "kgateway.gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
      {{- with $gateway.extraPodAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "kgateway.gateway.selectorLabels" . | nindent 8 }}
        {{- with $gateway.extraPodLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      containers:
        - name: agent-gateway
          image: ghcr.io/agentgateway/agentgateway:0.4.29
          args:
            - --file=/config/config.json
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: GW_NAME
              value: {{ include "kgateway.gateway.fullname" . }}
            - name: RUST_BACKTRACE
              value: "1"
            - name: RUST_LOG
              value: {{ $gateway.agentGateway.logLevel }}
          volumeMounts:
            - name: config-volume
              mountPath: /config
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "kgateway.gateway.fullname" . }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  {{- with $gateway.service.extraAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
    {{- with $gateway.service.extraLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ $gateway.service.type }}
  {{- with $gateway.service.clusterIP }}
  clusterIP: {{ . }}
  {{- end }}
  ports:
  {{- range $p := $gateway.ports }}
    - name: {{ $p.name }}
      protocol: {{ $p.protocol }}
      targetPort: {{ $p.targetPort }}
      port: {{ $p.port }}
    {{- if $p.nodePort }}
      nodePort: {{ $p.nodePort }}
    {{- end }}
  {{- end }}
  selector:
    {{- include "kgateway.gateway.selectorLabels" . | nindent 4 }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "kgateway.gateway.fullname" . }}
  labels:
    {{- include "kgateway.gateway.constLabels" . | nindent 4 }}
    {{- include "kgateway.gateway.labels" . | nindent 4 }}
data:
  config.json: |
    {
      "type": "xds",
      "xds_address": "http://{{ $gateway.xds.host }}:{{ $gateway.xds.port }}",
      "metadata": {},
      "alt_xds_hostname": "{{ $gateway.gatewayName | default (include "kgateway.gateway.fullname" .) }}.{{ $gateway.gatewayNamespace }}.svc.cluster.local",
      "listeners": []
    }

{{- end }} {{/* if $gateway.agentGateway.enabled */}}
