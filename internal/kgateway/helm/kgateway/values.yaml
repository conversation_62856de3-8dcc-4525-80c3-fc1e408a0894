# These values represent configurable values for the dynamic proxy chart
# They are not intended to be actual "defaults," rather they are just placeholder values
# meant to allow rendering of the chart/template, as the real values will come from:
# * The control plane
# * The `Gateway` resource driving the proxy provisioning
# * A (possibly merged) GatewayParameters object translated to helm values
# The actual defaults for these values should come from the "default GatewayParameters" object
# See: (/install/helm/kgateway/templates/gatewayparameters.yaml)
# TODO: This file/workflow can be shored up via https://github.com/kgateway-dev/kgateway/issues/10696

gateway:
  # xds values actually come from the control plane
  xds:
    host: ""
    port: 8080

  # actual default set in default GatewayParam proxyDeployment.replicas
  replicaCount: 1

  # actual default set in default GatewayParam service.type
  service:
    type: LoadBalancer

  image:
    pullPolicy: IfNotPresent

  istio:
    enabled: false

  # list of ports actually come from the Gateway resource driving this proxy
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http

  # leftover autoscaling config; not actually wired up for public use yet
  # see: https://github.com/kgateway-dev/kgateway/issues/10697
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    targetCPUUtilizationPercentage: 80
    # targetMemoryUtilizationPercentage: 80

  agentGateway:
    enabled: false
    logLevel: info
