apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: http
spec:
  gatewayClassName: kgateway
  listeners:
  - allowedRoutes:
      namespaces:
        from: All
    hostname: httpbin.example.com
    name: https-httpbin
    port: 443
    protocol: HTTPS
    tls:
      certificateRefs:
      - group: ""
        kind: Secret
        name: httpbin-tls-secret
      mode: Terminate
  - allowedRoutes:
      namespaces:
        from: All
    hostname: bookinfo.example.com
    name: https-bookinfo
    port: 443
    protocol: HTTPS
    tls:
      certificateRefs:
      - group: ""
        kind: Secret
        name: bookinfo-tls-secret
      mode: Terminate
