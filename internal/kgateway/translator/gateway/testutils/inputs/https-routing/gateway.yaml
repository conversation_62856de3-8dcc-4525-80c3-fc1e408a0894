#$ Used in:
#$ - site-src/guides/http-routing.md
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-gateway
spec:
  gatewayClassName: example-gateway-class
  listeners:
  - name: https
    protocol: HTTPS
    port: 443
    tls:
      mode: Terminate
      certificateRefs:
        - name: https
          kind: Secret
  - name: https-with-hostname
    hostname: second-example.org
    protocol: HTTPS
    port: 443
    tls:
      mode: Terminate
      certificateRefs:
        - name: https
          kind: Secret
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 80
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: second-example-route
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "second-example.org"
  rules:
  - backendRefs:
    - name: example-svc
      port: 80
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test
---
kind: Secret
apiVersion: v1
metadata:
  creationTimestamp: null
  name: https
type: kubernetes.io/tls
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVQVENDQWlXZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFiTVFvd0NBWURWUVFEREFFcU1RMHcKQ3dZRFZRUUtEQVJ5YjI5ME1CNFhEVEl6TVRFd09ERTJORFExTjFvWERUTXpNVEV3TlRFMk5EUTFOMW93SGpFSwpNQWdHQTFVRUF3d0JLakVRTUE0R0ExVUVDZ3dIWjJGMFpYZGhlVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFECmdnRVBBRENDQVFvQ2dnRUJBTit0V2hoa3QvNVFQTUw4UGorZ1JScUM1blp5TG9Sem5iK3hPazdQTVozRndtYUcKNThvbVhPRm16ZmJlK0VaaGE0UlBhK1BpdFhFbitjZkM5allYRU42dGM1WExWUjlKK1dCRXRhSUpoZlh2VzAvbgpraEg0MWFZa2NCQVMyTEh1U3l4WWd3VERMRzI1OUxVdVJFT3VGSVhtWUZJaGVlZTZ6V3dRMXk0Ujk1VzRoVGFzCi9JVk9wYmttbSsyM0ZVQ1Q3RTcvNzN0RFh3Q1dpekc3UnUyZ1p2aS9tK0ZRVUJCZmFPTGxzelQvVHNwNTB3YmUKY0hxY29UbWJNWUJpWDk1RFBYTWtnZ2g5M1R2bnBWb0taYVZhWDNOdHlGRGJOZnEyLzZaT2daNFlNZVgzb0VMUgpiVllpY01rU3lZRHJWbW9jeHZBMWdQQUsxd2NkVE1OcjlnY0F1b0VDQXdFQUFhT0JpRENCaFRBSkJnTlZIUk1FCkFqQUFNQXNHQTFVZER3UUVBd0lGNERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFnWUlLd1lCQlFVSEF3RXcKREFZRFZSMFJCQVV3QTRJQktqQWRCZ05WSFE0RUZnUVVxaW40SEhXN08wR3NLbGgwUEJ2SFFZSzRQT013SHdZRApWUjBqQkJnd0ZvQVVZb3l3TXpJN1BpWGtJd3FMSTdkNzA5SmJnVmN3RFFZSktvWklodmNOQVFFTEJRQURnZ0lCCkFCQ3p2NUUrb3hYT3RBUi9UWWE2YWJMVm94WENPWFZVeVFRell5VFJoekJOY0Vjbk1NeDBHc2V1NHRXeUlmem8KNmQ5LzRkOWdmdDlRNnVTS1RZUkhYU0VIQUFsMmlEWGdQTTZoSk0vNmpxQlE2N1ErVkVrUTJWVUMySDZEYjF1Qwo2VGdldk9MdlA1eDhrS2FjZVNnYTdmSHZJcW95OHVmbm1BSzlhd2ZobE9hajBjUWcxQXV6aW14blhzTVQvdkNVCm9Xa2xPZSt0TDA2OEd3LytIMFJQMTJ6N2t6VGJDbXZuRWU4bk1QSXNrU21NZTcvUnZLcUFuYTd0NHFDOVdyRXgKWlFZK0NlOVhrTnI3RGJaZnprTmpqUFV2OEozdHN2dzY5Zm1HcVBEWVpHMm1HQjRzeGFyNy9mZG4rbGd3MDVsRwpBbEhhaWpXTlVGTWtmcXgvUzZnampEL0NPZVpRcVltM1hLY3RCWkFSUjNJRUR0RWNEUkRzNllvczhCUTBzbS9ECkhnNG1XWWR4Y283WlpnUzF6ZWlhdkNwRWxDaXEzWnB5c0EvS1NLS056Y1RIRk5acXBxYWFNdVQvaTBuaUI1MmEKZmpFSDUzdk1wYXhUK1IvcFIxQ1NMREZ6VDI3OTFMaEprZkJWVWwzQkdnOVY2VCt6bkl1Nkk1aVU1RlZZbVl3UApubS9EYitncE9JalEyU0w5Wm1nVkNvdjFvNTBTU3ZLN1RYSFVIaXFoZXlucDAyR1ZYYXpFT295ODBWcG04dW9JCklvS2NFOS9IUldOT0F5Uk41cWtYRDBnalpJOWM1aUtjV25kQ1B1cnBFZENNZUdiems1cWRUNGhZSFFsM2RKeW8KNHJoZm9MN051R1VUa0o1ZCtGdFBwaXRISkNEVGdUb05LTVNGcTcxbzRoMXkKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  tls.key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
