apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: http
spec:
  gatewayClassName: kgateway
  listeners:
  - allowedRoutes:
      namespaces:
        from: All
    hostname: httpbin.example.com
    name: http-httpbin
    port: 80
    protocol: HTTP
  - allowedRoutes:
      namespaces:
        from: All
    hostname: bookinfo.example.com
    name: http-bookinfo
    port: 80
    protocol: HTTP
