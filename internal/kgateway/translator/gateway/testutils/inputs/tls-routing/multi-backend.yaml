apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TLSRoute
metadata:
  name: example-tls-route
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-tls-svc
      port: 443
      weight: 65
    - name: example-tls-svc-2
      port: 443
      weight: 35
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-gateway
spec:
  gatewayClassName: example-gateway-class
  listeners:
  - name: tls
    protocol: TLS
    hostname: "example.com"
    tls:
      mode: Passthrough
    port: 8443
---
apiVersion: v1
kind: Service
metadata:
  name: example-tls-svc
spec:
  selector:
    app: example
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8443
---
apiVersion: v1
kind: Service
metadata:
  name: example-tls-svc-2
spec:
  selector:
    app: example2
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8443
