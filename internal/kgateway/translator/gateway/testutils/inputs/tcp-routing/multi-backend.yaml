apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TCPRoute
metadata:
  name: example-tcp-route
spec:
  parentRefs:
  - name: example-tcp-gateway
  rules:
  - backendRefs:
    - name: example-tcp-svc-1
      port: 8080
      weight: 65
    - name: example-tcp-svc-2
      port: 8081
      weight: 35
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-tcp-gateway
spec:
  gatewayClassName: example-gateway-class
  listeners:
  - name: tcp
    protocol: TCP
    port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: example-tcp-svc-1
spec:
  selector:
    app: example1
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: example-tcp-svc-2
spec:
  selector:
    app: example2
  ports:
    - protocol: TCP
      port: 8081
      targetPort: 80
