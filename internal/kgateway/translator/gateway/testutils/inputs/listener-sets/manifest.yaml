apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: bar-route
spec:
  parentRefs:
  - name: bar-listenerset
    group: gateway.networking.x-k8s.io
    kind: XListenerSet
  hostnames:
  - "bar.example.com"
  rules:
  - matches:
    - headers:
      - type: Exact
        name: env
        value: canary
    backendRefs:
    - name: bar-svc-canary
      port: 8080
  - backendRefs:
    - name: bar-svc
      port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: bar-svc-canary
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test
---
apiVersion: v1
kind: Service
metadata:
  name: bar-svc
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test
---
apiVersion: gateway.networking.x-k8s.io/v1alpha1
kind: XListenerSet
metadata:
  name: bar-listenerset
spec:
  parentRef:
    name: example-gateway
    kind: Gateway
    group: gateway.networking.k8s.io
  listeners:
  - name: bar
    protocol: HTTP
    port: 8082
    allowedRoutes:
        namespaces:
          from: All
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: foo-route
spec:
  parentRefs:
  - name: foo-listenerset
    group: gateway.networking.x-k8s.io
    kind: XListenerSet
  hostnames:
  - "foo.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /login
    backendRefs:
    - name: foo-svc
      port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: foo-svc
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: foo-route
spec:
  parentRefs:
  - name: foo-listenerset
    group: gateway.networking.x-k8s.io
    kind: XListenerSet
  hostnames:
  - "foo.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /login
    backendRefs:
    - name: foo-svc
      port: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: foo-svc
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test
---
apiVersion: gateway.networking.x-k8s.io/v1alpha1
kind: XListenerSet
metadata:
  name: foo-listenerset
spec:
  parentRef:
    name: example-gateway
    kind: Gateway
    group: gateway.networking.k8s.io
  listeners:
  - name: foo
    protocol: HTTP
    port: 8081
    allowedRoutes:
        namespaces:
          from: All
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-gateway
spec:
  gatewayClassName: example-gateway-class
  listeners:
  - name: http
    protocol: HTTP
    port: 8080
  allowedListeners:
    namespaces:
      from: Same
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
  - name: example-gateway
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 80
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    test: test
  ports:
    - protocol: TCP
      port: 80
      targetPort: test