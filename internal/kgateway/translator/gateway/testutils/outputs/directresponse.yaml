Clusters:
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
Routes:
- ignorePortInHostMatching: true
  name: listener~8080
  virtualHosts:
  - domains:
    - example.com
    name: listener~8080~example_com
    routes:
    - directResponse:
        body:
          inlineString: |
            User-agent: *
            Disallow: /direct-response
        status: 510
      match:
        prefix: /
      name: listener~8080~example_com-route-0-httproute-example-default-0-0-matcher-0
