Clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_example-svc_8080
  type: EDS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~8080
        statPrefix: http
        useRemoteAddress: true
    name: listener~8080
  name: listener~8080
Routes:
- ignorePortInHostMatching: true
  name: listener~8080
  virtualHosts:
  - domains:
    - example.com
    name: listener~8080~example_com
    routes:
    - match:
        pathSeparatedPrefix: /header
      name: listener~8080~example_com-route-0-httproute-example-route-default-1-0-matcher-0
      requestHeadersToAdd:
      - header:
          key: X-Header-Add-1
          value: header-add-1
      - header:
          key: X-Header-Add-2
          value: header-add-2
      - header:
          key: X-Header-Add-3
          value: header-add-3
      - appendAction: OVERWRITE_IF_EXISTS_OR_ADD
        header:
          key: X-Header-Set-1
          value: header-set-1
      - appendAction: OVERWRITE_IF_EXISTS_OR_ADD
        header:
          key: X-Header-Set-2
          value: header-set-2
      requestHeadersToRemove:
      - X-Header-Remove-1
      - X-Header-Remove-2
      route:
        cluster: kube_default_example-svc_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        prefix: /
      name: listener~8080~example_com-route-1-httproute-example-route-default-0-0-matcher-0
      route:
        cluster: kube_default_example-svc_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
