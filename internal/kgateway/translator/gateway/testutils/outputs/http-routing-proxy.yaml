Clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_bar-svc-canary_80
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_bar-svc_80
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_example-svc_80
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_default_foo-svc_80
  type: EDS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~80
        statPrefix: http
        useRemoteAddress: true
    name: listener~80
  name: listener~80
Routes:
- ignorePortInHostMatching: true
  name: listener~80
  virtualHosts:
  - domains:
    - bar.example.com
    name: listener~80~bar_example_com
    routes:
    - match:
        headers:
        - name: env
          stringMatch:
            exact: canary
        prefix: /
      name: listener~80~bar_example_com-route-0-httproute-bar-route-default-0-0-matcher-0
      route:
        cluster: kube_default_bar-svc-canary_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        prefix: /
      name: listener~80~bar_example_com-route-1-httproute-bar-route-default-1-0-matcher-0
      route:
        cluster: kube_default_bar-svc_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
  - domains:
    - example.com
    name: listener~80~example_com
    routes:
    - match:
        prefix: /
      name: listener~80~example_com-route-0-httproute-example-route-default-0-0-matcher-0
      route:
        cluster: kube_default_example-svc_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
  - domains:
    - foo.example.com
    name: listener~80~foo_example_com
    routes:
    - match:
        pathSeparatedPrefix: /login
      name: listener~80~foo_example_com-route-0-httproute-foo-route-default-0-0-matcher-0
      route:
        cluster: kube_default_foo-svc_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
