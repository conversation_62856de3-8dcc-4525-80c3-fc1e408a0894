listeners:
- aggregateListener:
    httpFilterChains:
    - matcher: {}
      virtualHostRefs:
      - http~example_com
    httpResources:
      virtualHosts:
        http~example_com:
          domains:
          - example.com
          name: http~example_com
          routes:
          - matchers:
            - prefix: /
            options: {}
            name: httproute-example-route-default-0-0
            routeAction:
              single:
                destinationSpec:
                  azure:
                    functionName: uppercase
                upstream:
                  name: azure-upstream
                  namespace: default
  bindAddress: '::'
  bindPort: 8080
  name: http
metadata:
  labels:
    created_by: kgateway-kube-gateway-api
    gateway_namespace: default
  name: default-gw
  namespace: gloo-system