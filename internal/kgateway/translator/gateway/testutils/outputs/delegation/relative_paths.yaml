Clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a_svc-a_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a_svc-b_8090
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_b_svc-b_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_infra_example-svc_80
  type: EDS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~80
        statPrefix: http
        useRemoteAddress: true
    name: listener~80
  name: listener~80
Routes:
- ignorePortInHostMatching: true
  name: listener~80
  virtualHosts:
  - domains:
    - example.com
    name: listener~80~example_com
    routes:
    - match:
        headers:
        - name: :method
          stringMatch:
            exact: PUT
        path: /a/a/1
      name: listener~80~example_com-route-0-httproute-route-a-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        safeRegex:
          googleRe2: {}
          regex: /a/a/2/.*
      name: listener~80~example_com-route-1-httproute-route-a-a-1-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: headerA
          stringMatch:
            exact: valA
        pathSeparatedPrefix: /a/a/3
        queryParameters:
        - name: queryA
          stringMatch:
            exact: valA
      name: listener~80~example_com-route-2-httproute-route-a-a-2-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        pathSeparatedPrefix: /a
      name: listener~80~example_com-route-3-httproute-route-b-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-b_8090
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        prefix: /
      name: listener~80~example_com-route-5-httproute-example-route-infra-0-0-matcher-0
      route:
        cluster: kube_infra_example-svc_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
  - domains:
    - foo.com
    name: listener~80~foo_com
    routes:
    - match:
        headers:
        - name: header1
          stringMatch:
            exact: val1
        - name: :method
          stringMatch:
            exact: GET
        path: /x/a/1
        queryParameters:
        - name: query1
          stringMatch:
            exact: val1
      name: listener~80~foo_com-route-0-httproute-route-a-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: header1
          stringMatch:
            exact: val1
        - name: :method
          stringMatch:
            exact: GET
        queryParameters:
        - name: query1
          stringMatch:
            exact: val1
        safeRegex:
          googleRe2: {}
          regex: /x/a/2/.*
      name: listener~80~foo_com-route-1-httproute-route-a-a-1-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: header1
          stringMatch:
            exact: val1
        - name: headerA
          stringMatch:
            exact: valA
        - name: :method
          stringMatch:
            exact: GET
        pathSeparatedPrefix: /x/a/3
        queryParameters:
        - name: query1
          stringMatch:
            exact: val1
        - name: queryA
          stringMatch:
            exact: valA
      name: listener~80~foo_com-route-2-httproute-route-a-a-2-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: header1
          stringMatch:
            exact: val1
        - name: :method
          stringMatch:
            exact: GET
        pathSeparatedPrefix: /x
        queryParameters:
        - name: query1
          stringMatch:
            exact: val1
      name: listener~80~foo_com-route-3-httproute-route-b-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-b_8090
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
