Clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a-b_svc-a-b_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a_svc-a_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_b_svc-b_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_infra_example-svc_80
  type: EDS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~80
        statPrefix: http
        useRemoteAddress: true
    name: listener~80
  name: listener~80
Routes:
- ignorePortInHostMatching: true
  name: listener~80
  virtualHosts:
  - domains:
    - example.com
    name: listener~80~example_com
    routes:
    - match:
        pathSeparatedPrefix: /a/b/1
      name: listener~80~example_com-route-0-httproute-route-a-b-a-b-0-0-matcher-0
      route:
        cluster: kube_a-b_svc-a-b_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        pathSeparatedPrefix: /a/1
      name: listener~80~example_com-route-1-httproute-route-a-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        prefix: /
      name: listener~80~example_com-route-4-httproute-example-route-infra-0-0-matcher-0
      route:
        cluster: kube_infra_example-svc_80
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
