Clusters:
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a-b-d_svc-a-b-d_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a-b_svc-a-b_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_a_svc-a_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_b_svc-b_8080
  type: EDS
- connectTimeout: 5s
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
  metadata: {}
  name: kube_infra_example-svc_80
  type: EDS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~80
        statPrefix: http
        useRemoteAddress: true
    name: listener~80
  name: listener~80
Routes:
- ignorePortInHostMatching: true
  name: listener~80
  virtualHosts:
  - domains:
    - example.com
    name: listener~80~example_com
    routes:
    - match:
        headers:
        - name: parent-header
          stringMatch:
            exact: parent-header-val
        - name: route-a-b-header-2
          stringMatch:
            exact: route-a-b-header-val-2
        - name: route-a-b-d-header
          stringMatch:
            exact: route-a-b-d-header-val
        - name: :method
          stringMatch:
            exact: GET
        pathSeparatedPrefix: /a/b/d/1
        queryParameters:
        - name: parent-query
          stringMatch:
            exact: parent-query-val
        - name: route-a-b-query-2
          stringMatch:
            exact: route-a-b-query-val-2
        - name: route-a-b-d-query
          stringMatch:
            exact: route-a-b-d-query-val
      name: listener~80~example_com-route-0-httproute-route-a-b-d-a-b-d-0-0-matcher-0
      route:
        cluster: kube_a-b-d_svc-a-b-d_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: parent-header
          stringMatch:
            exact: parent-header-val
        - name: route-a-b-header
          stringMatch:
            exact: route-a-b-header-val
        - name: :method
          stringMatch:
            exact: GET
        pathSeparatedPrefix: /a/b/c
        queryParameters:
        - name: parent-query
          stringMatch:
            exact: parent-query-val
        - name: route-a-b-query
          stringMatch:
            exact: route-a-b-query-val
      name: listener~80~example_com-route-1-httproute-route-a-b-a-b-0-0-matcher-0
      route:
        cluster: kube_a-b_svc-a-b_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
    - match:
        headers:
        - name: parent-header
          stringMatch:
            exact: parent-header-val
        - name: route-a-header
          stringMatch:
            exact: route-a-header-val
        pathSeparatedPrefix: /a/1
        queryParameters:
        - name: parent-query
          stringMatch:
            exact: parent-query-val
        - name: route-a-query
          stringMatch:
            exact: route-a-query-val
      name: listener~80~example_com-route-4-httproute-route-a-a-0-0-matcher-0
      route:
        cluster: kube_a_svc-a_8080
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
