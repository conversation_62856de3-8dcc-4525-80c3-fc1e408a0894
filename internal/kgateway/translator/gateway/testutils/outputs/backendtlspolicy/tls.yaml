Clusters:
- connectTimeout: 5s
  dnsLookupFamily: V4_PREFERRED
  loadAssignment:
    clusterName: backend_default_backend1_0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: example.com
              portValue: 8080
          healthCheckConfig:
            hostname: example.com
          hostname: example.com
  metadata: {}
  name: backend_default_backend1_0
  transportSocket:
    name: envoy.transport_sockets.tls
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
      commonTlsContext:
        tlsParams: {}
        validationContext:
          matchTypedSubjectAltNames:
          - matcher:
              exact: example.com
            sanType: DNS
          trustedCa:
            inlineString: |
              -----BEGIN CERTIFICATE-----
              MIIC1jCCAb4CCQCJczLyBBZ1GTANBgkqhkiG9w0BAQsFADAtMRUwEwYDVQQKDAxl
              eGFtcGxlIEluYy4xFDASBgNVBAMMC2V4YW1wbGUuY29tMB4XDTI1MDMwNzE0Mjkx
              NloXDTI2MDMwNzE0MjkxNlowLTEVMBMGA1UECgwMZXhhbXBsZSBJbmMuMRQwEgYD
              VQQDDAtleGFtcGxlLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB
              AN0U6TVYECkwqnxh1Kt3dS+LialrXBOXKagj9tE582T6dwmqThD75VZPrNKkRoYO
              aUzCctfDkUBXRemOTMut7ES5xoAtSAhr2GAnqgM3+yBCLOxooSjEFdlpFT7dhi1w
              jOPa5iMh6ve/pHuRHvEuaF/J6P8tr83wGutx/xFZVuGA9V1AmBmYhePM+JhdcwaB
              1+IbJp30gGyPfY4vdRQ9VQWbThE8psEzah+3SgTKJSIT7NAdwiIu3O3rXORbaYYU
              oycgXUHdOKRbJnbvy3pTnFZJ50sg1HIA4yBdX7c0diy8Zz3Suoondg3DforWr0pB
              Hs6tySAQoz2RiAqDqcE2rbMCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAWPkz3dJW
              b+LFtnv7MlOVM79Y4PqeiHnazP1G9FwnWBHARkjISsax3b0zX8/RHnU83c3tLP5D
              VwenYb9B9mzXbLiWI8aaX0UXP//D593ti15y0Od7yC2hQszlqIbxYnkFVwXoT9fQ
              bdQ9OtpCt8EZnKEyCxck+hlKEyYTcH2PqZ7Ndp0M8I2znz3Kut/uYHLUddfoPF/m
              O0V6fbyB/Mx/G1uLiv/BVpx3AdP+3ygJyKtelXkD+IdlY3y110fzmVr6NgxAbz/h
              n9KpuK4SEloIycZUaKVXAaX7T42SFYw7msmB+Uu7z5oLOijsjX6TjeofdFBZ/Byl
              SxODgqhtaPnOxQ==
              -----END CERTIFICATE-----
      sni: example.com
  type: STRICT_DNS
- connectTimeout: 5s
  dnsLookupFamily: V4_PREFERRED
  loadAssignment:
    clusterName: backend_default_backend2_0
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: example2.com
              portValue: 8080
          healthCheckConfig:
            hostname: example2.com
          hostname: example2.com
  metadata: {}
  name: backend_default_backend2_0
  transportSocket:
    name: envoy.transport_sockets.tls
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
      commonTlsContext:
        combinedValidationContext:
          defaultValidationContext:
            matchTypedSubjectAltNames:
            - matcher:
                exact: example2.com
              sanType: DNS
          validationContextSdsSecretConfig:
            name: SYSTEM_CA_CERT
      sni: example2.com
  type: STRICT_DNS
- connectTimeout: 5s
  metadata: {}
  name: test-backend-plugin_default_example-svc_80
Listeners:
- address:
    socketAddress:
      address: '::'
      ipv4Compat: true
      portValue: 8080
  filterChains:
  - filters:
    - name: envoy.filters.network.http_connection_manager
      typedConfig:
        '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
        httpFilters:
        - name: envoy.filters.http.router
          typedConfig:
            '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
        mergeSlashes: true
        normalizePath: true
        rds:
          configSource:
            ads: {}
            resourceApiVersion: V3
          routeConfigName: listener~80
        statPrefix: http
        useRemoteAddress: true
    name: listener~80
  name: listener~80
Routes:
- ignorePortInHostMatching: true
  name: listener~80
  virtualHosts:
  - domains:
    - '*'
    name: listener~80~*
    routes:
    - match:
        prefix: /
      name: listener~80~*-route-0-httproute-example-route-default-0-0-matcher-0
      route:
        cluster: backend_default_backend1_0
        clusterNotFoundResponseCode: INTERNAL_SERVER_ERROR
      typedPerFilterConfig:
        ai.extproc.kgateway.io:
          '@type': type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExtProcPerRoute
          disabled: true
