package backendconfigpolicy

import (
	"fmt"
	"testing"

	envoyauth "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"

	"github.com/kgateway-dev/kgateway/v2/api/v1alpha1"
	"github.com/kgateway-dev/kgateway/v2/internal/kgateway/ir"
)

// MockSecretGetter implements SecretGetter for testing
type MockSecretGetter struct {
	secrets map[string]*ir.Secret
}

func NewMockSecretGetter() *MockSecretGetter {
	return &MockSecretGetter{
		secrets: make(map[string]*ir.Secret),
	}
}

func (m *MockSecretGetter) AddSecret(name, namespace string, secret *ir.Secret) {
	key := namespace + "/" + name
	m.secrets[key] = secret
}

func (m *MockSecretGetter) GetSecret(name, namespace string) (*ir.Secret, error) {
	key := namespace + "/" + name
	if secret, ok := m.secrets[key]; ok {
		return secret, nil
	}
	return nil, fmt.Errorf("secret %s/%s not found", namespace, name)
}

// openssl req -x509 -newkey rsa:2048 -keyout test.key -out test.crt -days 365 -nodes -subj "/CN=test.example.com" -addext "subjectAltName=DNS:test.example.com"
var CACert = `-----BEGIN CERTIFICATE-----
MIIDNDCCAhygAwIBAgIUL6jJHHVicPbTrxNXjTX2ti/2swgwDQYJKoZIhvcNAQEL
BQAwGzEZMBcGA1UEAwwQdGVzdC5leGFtcGxlLmNvbTAeFw0yNTA1MjkxNTQ5MTha
Fw0yNjA1MjkxNTQ5MThaMBsxGTAXBgNVBAMMEHRlc3QuZXhhbXBsZS5jb20wggEi
MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC0zE9AuZN4Uc5VOsUbLYHZaEh/
db2HiHsYdyxpuLx1C2aXYUpIyGjwVSs84+TwS46XRCstZHsTDrSvlM6hwU2x+B7E
FksEM5TPU/0e6+lUde0yUweiYCYIKnJU1PzWO7pldS8K8ayvTYbIMSWawzCgWeq1
OWPgwCfSK0GF2MyfhfAqMYazZB9rZhGycyaaE1iKX97JyYU79klhnaEdZE3bhCNr
wH2s5h55jbIrizUAbjz6+t5B+euakUrfKCeGXfCb3TNz48IEWdNIMPmyfgSWzXlz
MXKpfZ0tza6SzeqrDLZN2nl/YydM1yHmI7MALrIXJo0hXk4N469f/MIdCKZdAgMB
AAGjcDBuMB0GA1UdDgQWBBS1oJXQN8/QuWWlo+UfZe2SKxy2ezAfBgNVHSMEGDAW
gBS1oJXQN8/QuWWlo+UfZe2SKxy2ezAPBgNVHRMBAf8EBTADAQH/MBsGA1UdEQQU
MBKCEHRlc3QuZXhhbXBsZS5jb20wDQYJKoZIhvcNAQELBQADggEBAFtjff8nA/+I
2vLVq6SE3eLe/x4w09RtpdNZ+qirAQsbu0DrI1F9/MNxSYhKMA+4DCj1OXpUaaPO
mwZIwEtFklUyDqz8aaBK8xCBjzvc++rbaiY2XVDo+/e6ML0c90LXyGI3pDK6bUU1
15dFeYikl+7iVf4L+DrWgj7imK5LtWqKS7VTUX/+yFnA19d7LJF2/uOnprIeEHsj
LSlVx4yPJjGQYighFyK6VQKi3rsiuFU/LsedNEq2kJonn/NfT9pCvoReQqjijlyS
D8sD7wlIiyowZO09KIU7MUfPUqGlGsNXQ9Hy9sHJgPmsz4ZM4NofSOdt8MGETulJ
Tr8dXUTlbn0=
-----END CERTIFICATE-----
`

var TLSKey = `************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************` // must have this newline at the end

func TestTranslateSSLConfig(t *testing.T) {
	tests := []struct {
		name      string
		sslConfig *v1alpha1.SSLConfig
		secret    *ir.Secret
		wantErr   bool
		check     func(t *testing.T, result *envoyauth.UpstreamTlsContext)
	}{
		{
			name: "secret-based SSL config",
			sslConfig: &v1alpha1.SSLConfig{
				SecretRef: &corev1.LocalObjectReference{
					Name: "test-secret",
				},
				Sni:                "test.example.com",
				AllowRenegotiation: ptr.To(true),
				AlpnProtocols:      []string{"h2", "http/1.1"},
			},
			secret: &ir.Secret{
				ObjectSource: ir.ObjectSource{
					Group:     "",
					Kind:      "Secret",
					Namespace: "default",
					Name:      "test-secret",
				},
				Obj: &corev1.Secret{},
				Data: map[string][]byte{
					"tls.crt": []byte(CACert),
					"tls.key": []byte(TLSKey),
					"ca.crt":  []byte(CACert),
				},
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
				assert.Equal(t, "test.example.com", result.Sni)
				assert.True(t, result.AllowRenegotiation)
				assert.NotNil(t, result.CommonTlsContext)
				assert.Equal(t, []string{"h2", "http/1.1"}, result.CommonTlsContext.AlpnProtocols)
				assert.Len(t, result.CommonTlsContext.TlsCertificates, 1)
				validateCommonTlsContextInline(t, result)
			},
		},
		{
			name: "file-based SSL config",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					TLSCertificate: CACert,
					TLSKey:         TLSKey,
					RootCA:         CACert,
				},
				Sni:                "test.example.com",
				AllowRenegotiation: ptr.To(true),
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
				assert.Equal(t, "test.example.com", result.Sni)
				assert.True(t, result.AllowRenegotiation)
				assert.NotNil(t, result.CommonTlsContext)
				assert.Len(t, result.CommonTlsContext.TlsCertificates, 1)
				validateCommonTlsContextFiles(t, result)
			},
		},
		{
			name: "SSL config with parameters",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					TLSCertificate: CACert,
					TLSKey:         TLSKey,
				},
				SSLParameters: &v1alpha1.SSLParameters{
					TLSMinVersion: ptr.To(v1alpha1.TLSVersion1_2),
					TLSMaxVersion: ptr.To(v1alpha1.TLSVersion1_3),
					CipherSuites:  []string{"TLS_AES_128_GCM_SHA256"},
					EcdhCurves:    []string{"X25519"},
				},
				AllowRenegotiation: ptr.To(true),
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
				assert.NotNil(t, result.CommonTlsContext.TlsParams)
				assert.Equal(t, envoyauth.TlsParameters_TLSv1_2, result.CommonTlsContext.TlsParams.TlsMinimumProtocolVersion)
				assert.Equal(t, envoyauth.TlsParameters_TLSv1_3, result.CommonTlsContext.TlsParams.TlsMaximumProtocolVersion)
				assert.Equal(t, []string{"TLS_AES_128_GCM_SHA256"}, result.CommonTlsContext.TlsParams.CipherSuites)
				assert.Equal(t, []string{"X25519"}, result.CommonTlsContext.TlsParams.EcdhCurves)
			},
		},
		{
			name: "invalid SSL config - missing both secret and files",
			sslConfig: &v1alpha1.SSLConfig{
				AllowRenegotiation: ptr.To(true),
			},
			wantErr: true,
		},
		{
			name: "invalid SSL config - missing secret",
			sslConfig: &v1alpha1.SSLConfig{
				SecretRef: &corev1.LocalObjectReference{
					Name: "non-existent-secret",
				},
				AllowRenegotiation: ptr.To(true),
			},
			wantErr: true,
		},
		{
			name: "should not error with only rootca",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					RootCA: CACert,
				},
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
			},
		},
		{
			name: "should error with san and no rootca",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					TLSCertificate: CACert,
					TLSKey:         TLSKey,
				},
				VerifySubjectAltName: []string{"test.example.com"},
			},
			wantErr: true,
		},
		{
			name: "should error with only cert and no key",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					TLSCertificate: CACert,
				},
			},
			wantErr: true,
		},
		{
			name: "should not have validation context if one way tls",
			sslConfig: &v1alpha1.SSLConfig{
				SSLFiles: &v1alpha1.SSLFiles{
					TLSCertificate: CACert,
					TLSKey:         TLSKey,
					RootCA:         CACert,
				},
				OneWayTLS: ptr.To(true),
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
				assert.Nil(t, result.CommonTlsContext.GetValidationContext())
			},
		},
		{
			name: "should not have validation context if no rootca",
			sslConfig: &v1alpha1.SSLConfig{
				SecretRef: &corev1.LocalObjectReference{
					Name: "test-secret",
				},
			},
			secret: &ir.Secret{
				ObjectSource: ir.ObjectSource{
					Group:     "",
					Kind:      "Secret",
					Namespace: "default",
					Name:      "test-secret",
				},
				Obj: &corev1.Secret{},
				Data: map[string][]byte{
					"tls.crt": []byte(CACert),
					"tls.key": []byte(TLSKey),
				},
			},
			wantErr: false,
			check: func(t *testing.T, result *envoyauth.UpstreamTlsContext) {
				assert.NotNil(t, result)
				assert.Nil(t, result.CommonTlsContext.GetValidationContext())
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock secret getter
			secretGetter := NewMockSecretGetter()

			// Add secret to mock if provided
			if tt.secret != nil {
				secretGetter.AddSecret(tt.secret.Name, tt.secret.Namespace, tt.secret)
			}

			// Call the function
			result, err := translateSSLConfig(secretGetter, tt.sslConfig, "default")

			// Check error
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			if tt.check != nil {
				tt.check(t, result)
			}
		})
	}
}

func validateCommonTlsContextInline(t *testing.T, result *envoyauth.UpstreamTlsContext) {
	assert.NotNil(t, result)
	assert.NotNil(t, result.CommonTlsContext)
	assert.Len(t, result.CommonTlsContext.TlsCertificates, 1)
	validationCtx := result.CommonTlsContext.GetValidationContext()
	assert.NotNil(t, validationCtx)
	assert.Equal(t, CACert, validationCtx.TrustedCa.GetInlineString())

	assert.Equal(t, CACert, result.CommonTlsContext.TlsCertificates[0].GetCertificateChain().GetInlineString())
	assert.Equal(t, TLSKey, result.CommonTlsContext.TlsCertificates[0].GetPrivateKey().GetInlineString())
}

func validateCommonTlsContextFiles(t *testing.T, result *envoyauth.UpstreamTlsContext) {
	assert.NotNil(t, result)
	assert.NotNil(t, result.CommonTlsContext)
	assert.Len(t, result.CommonTlsContext.TlsCertificates, 1)
	validationCtx := result.CommonTlsContext.GetValidationContext()
	assert.NotNil(t, validationCtx)
	assert.Equal(t, CACert, validationCtx.TrustedCa.GetFilename())

	assert.Equal(t, CACert, result.CommonTlsContext.TlsCertificates[0].GetCertificateChain().GetFilename())
	assert.Equal(t, TLSKey, result.CommonTlsContext.TlsCertificates[0].GetPrivateKey().GetFilename())
}
