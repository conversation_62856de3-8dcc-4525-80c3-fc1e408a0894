---
# The Namespace capture should apply to all the Services in the namespace
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: example-waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `istio-se` backend
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: se-a
  namespace: infra
spec:
  hosts:
  - se-a.example.com
  addresses:
  - *******
  ports:
  - number: 5000
    name: http
    protocol: HTTP
  location: MESH_INTERNAL
  resolution: STATIC
  workloadSelector:
    labels:
      app: a
---
# without addresses, we can't do anything unless zTunnel
# starts sending a hostname for us on TLVs that we can use in FCM
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: se-headless
  namespace: infra
spec:
  hosts:
  - se-headless.example.com
  ports:
  - number: 9000
    name: http
    protocol: HTTP
  location: MESH_INTERNAL
  resolution: STATIC
  workloadSelector:
    labels:
      app: headless
