---
# The Namespace capture should apply to all the Services in the namespace
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: example-waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-a
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: svc-a
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-b
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 9000
    name: http
  selector:
    app: svc-b
---
# TODO headless services aren't supported so this won't have a corresponding
# filter chain in the output xDS
apiVersion: v1
kind: Service
metadata:
  name: helloworld-headless
  namespace: infra
spec:
  clusterIP: None
  ports:
  - port: 5000
    name: http
  selector:
    app: helloworld
---
# ServiceEntry should also work with NS waypoint attachment
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `istio-se` backend
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: se-c
  namespace: infra
spec:
  hosts:
  - se-c.infra.svc.cluster.local
  addresses:
  - *******
  ports:
  - number: 9000
    name: http
    protocol: HTTP
  location: MESH_INTERNAL
  resolution: STATIC
  endpoints:
  - address: *******
    labels:
      app: se-c
---
