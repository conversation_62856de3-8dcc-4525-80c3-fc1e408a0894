
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: helloworld
  namespace: infra
  labels:
    istio.io/use-waypoint: example-waypoint
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: helloworld
---
# TODO headless services aren't supported so this won't have a corresponding
# filter chain in the output xDS
apiVersion: v1
kind: Service
metadata:
  name: helloworld-headless
  namespace: infra
  labels:
    istio.io/use-waypoint: example-waypoint
spec:
  clusterIP: None
  ports:
  - port: 5000
    name: http
  selector:
    app: helloworld
