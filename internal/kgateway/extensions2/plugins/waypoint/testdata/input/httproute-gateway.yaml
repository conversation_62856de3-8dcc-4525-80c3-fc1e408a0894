---
# The Namespace capture should apply to all the Services in the namespace
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: example-waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# HTTPRoute parented to the gateway should affect every filter chain
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: waypoint-route
  namespace: infra
spec:
  parentRefs:
  - name: example-waypoint
    namespace: infra
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /a
    backendRefs:
    - name: svc-a
      port: 5000
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "traversed-waypoint"
          value: "kgateway-waypoint"
  - matches:
    - path:
        type: PathPrefix
        value: /b
    backendRefs:
    - name: svc-b
      port: 9000
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "traversed-waypoint"
          value: "kgateway-waypoint"
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-a
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: svc-a
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-b
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 9000
    name: http
  selector:
    app: svc-b
---
