---
# The Namespace capture should apply to all the Services in the namespace
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: example-waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-a
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: svc-a
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: svc-b
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 9000
    name: http
  selector:
    app: svc-b
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `kube` Service backend
apiVersion: v1
kind: Service
metadata:
  name: helloworld
  namespace: infra
  labels:
    istio.io/use-waypoint: example-waypoint
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: helloworld    
---
# we should get a filter chain with two filters that reflect this policy
# that denies GET requests to helloworld and svc-a services
apiVersion: security.istio.io/v1
kind: AuthorizationPolicy
metadata:
  name: helloworld-authz-svc
  namespace: infra
spec:
  action: DENY
  rules:
  - to:
    - operation:
        methods:
        - GET
  targetRefs:
  - group: ""
    kind: Service
    name: helloworld
  - group: ""
    kind: Service
    name: svc-a