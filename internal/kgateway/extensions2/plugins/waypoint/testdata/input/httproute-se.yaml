---
# The Namespace capture should apply to all the Services in the namespace
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: example-waypoint
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# HTTPRoute parented to the Service only affects one chain
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: waypoint-route
  namespace: infra
spec:
  parentRefs:
  - name: se-a
    namespace: infra
    group: "networking.istio.io"
    kind: ServiceEntry
  rules:
  - backendRefs:
    - name: se-a
      group: networking.istio.io
      kind: ServiceEntry
      port: 5000
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "traversed-waypoint"
          value: "kgateway-waypoint"
---
# we should get a filter chain matching *******
# with a default virtualhost that sends traffic to the HTTPRoute
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: se-a
  namespace: infra
spec:
  hosts:
  - se-a.example.com
  addresses:
  - *******
  ports:
  - number: 5000
    name: http
    protocol: HTTP
  location: MESH_INTERNAL
  resolution: STATIC
  endpoints:
  - address: *******
    labels:
      app: se-b
---
# we should get a filter chain with a default virtualhost that just
# sends traffic to the corresponding `istio-se` backend
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: se-b
  namespace: infra
spec:
  hosts:
  - se-b.example.com
  addresses:
  - *******
  ports:
  - number: 9000
    name: http
    protocol: HTTP
  location: MESH_INTERNAL
  resolution: STATIC
  endpoints:
  - address: *******
    labels:
      app: se-b
---
