---
# this does't point at our waypoint
apiVersion: v1
kind: Namespace
metadata:
  name: infra
  labels:
    istio.io/use-waypoint: not-our-waypoint
---
# we should get no listeners in the output
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: example-waypoint
  namespace: infra
spec:
  gatewayClassName: kgateway-waypoint
  listeners:
  - name: proxy
    port: 15088
    protocol: istio.io/PROXY
---
# this service isn't using our waypoint
apiVersion: v1
kind: Service
metadata:
  name: svc-a
  namespace: infra
spec:
  clusterIP: *******
  ports:
  - port: 5000
    name: http
  selector:
    app: svc-a
---
