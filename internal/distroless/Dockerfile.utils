# The base image should be $(GLOO_DISTROLESS_BASE_IMAGE)
ARG BASE_IMAGE
ARG UTILS_DONOR_IMAGE

FROM $UTILS_DONOR_IMAGE as donor

FROM $BASE_IMAGE

# Copy over the required binaries for probes and lifecycle hooks
COPY --from=donor /bin/sh /bin/sh
COPY --from=donor /bin/wget /bin/wget
COPY --from=donor /bin/sleep /bin/sleep
COPY --from=donor /bin/nc /bin/nc
COPY --from=donor /bin/echo /bin/echo
# Copy over these binaries for basic debugging. They are non essential and can be removed to further minify the images
# Keeping them in for now to investigate any potential issues with distroless builds. They can be removed after a couple of releases
COPY --from=donor /bin/ls /bin/ls
COPY --from=donor /bin/cat /bin/cat
COPY --from=donor /bin/vi /bin/vi

