package main

import (
	// Import all Envoy and Gloo filter types so they are registered and deserialization does
	// not fail when using them in the "typed_config" attributes.
	// The filter types are autogenerated by looking at all packages in go-control-plane and gloo
	// api extensions. As a result, this will need to be re-run when updating go-control-plane
	// or gloo extensions if new packages are added.
	"github.com/kgateway-dev/kgateway/v2/pkg/envoyinit"
	_ "github.com/kgateway-dev/kgateway/v2/pkg/utils/filter_types"
)

func main() {
	envoyExecutable := envoyinit.GetEnvoyExecutable()
	inputPath := envoyinit.GetInputConfigPath()
	outputPath := envoyinit.GetOutputConfigPath()

	envoyinit.RunEnvoy(envoyExecutable, inputPath, outputPath)
}
