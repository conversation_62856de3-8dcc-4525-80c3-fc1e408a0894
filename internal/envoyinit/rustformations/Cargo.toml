[package]
name = "rustformations"
version = "0.1.0"
edition = "2021"

[dependencies]
# The SDK version must match the Envoy version due to the strict compatibility requirements.
envoy-proxy-dynamic-modules-rust-sdk = { git = "https://github.com/envoyproxy/envoy", rev = "c435eeccd4201f8d6a200922b166f5dcee08272b" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rand = "0.9.0"
matchers = "0.2.0"
minijinja = { version = "2.7.0" }
tempfile = "3.16.0"
mockall = "0.13.1"

[lib]
name = "rust_module"
path = "src/lib.rs"
crate-type = ["cdylib"]


