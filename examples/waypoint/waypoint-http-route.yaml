apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: waypoint-add-header
  namespace: httpbin
spec:
  parentRefs:
  - name: httpbin
    group: ""
    kind: Service
  rules:
  - backendRefs:
    - name: httpbin
      port: 8000
    filters:
    - type: ResponseHeaderModifier
      responseHeaderModifier:
        add:
        - name: "Traversed-Waypoint"
          value: "httpbin-waypoint"
