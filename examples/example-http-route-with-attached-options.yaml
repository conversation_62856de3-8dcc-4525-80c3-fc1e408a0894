apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
  - name: gw
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 8080
  - matches:
    - path:
        type: PathPrefix
        value: /timeout
    backendRefs:
    - name: example-svc
      port: 8080
---
apiVersion: gateway.solo.io/v1
kind: RouteOption
metadata:
  name: policy-attached
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: example-route
  options:
    faults:
      abort:
        percentage: 100
        httpStatus: 500
    timeout: 11s
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: nginx
  template:
    metadata:
      labels:
        app.kubernetes.io/name: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:stable
        ports:
        - containerPort: 80
          name: http-web-svc
