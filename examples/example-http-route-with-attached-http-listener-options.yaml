apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
  - name: gw
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 8080
  - matches:
    - path:
        type: PathPrefix
        value: /timeout
    backendRefs:
    - name: example-svc
      port: 8080
---
apiVersion: gateway.solo.io/v1
kind: HttpListenerOption
metadata:
  name: server-name
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: gw
  options:
    httpConnectionManagerSettings:
      serverName: "unit-test v4.19"
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  containers:
  - name: nginx
    image: nginx:stable
    ports:
    - containerPort: 80
      name: http-web-svc
