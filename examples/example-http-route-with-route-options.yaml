apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: example-route
spec:
  parentRefs:
  - name: gw
  hostnames:
  - "example.com"
  rules:
  - backendRefs:
    - name: example-svc
      port: 8080
    filters:
    - type: ExtensionRef
      extensionRef:
        group: gateway.solo.io
        kind: RouteOption
        name: foo
---
apiVersion: gateway.solo.io/v1
kind: RouteOption
metadata:
  name: foo
spec:
  options:
    retries:
      retryOn: 'connect-failure'
      numRetries: 3
      perTryTimeout: '5s'
    timeout: 10s
---
apiVersion: v1
kind: Service
metadata:
  name: example-svc
spec:
  selector:
    app.kubernetes.io/name: nginx
  ports:
    - protocol: TCP
      port: 8080
      targetPort: http-web-svc
---
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  labels:
    app.kubernetes.io/name: nginx
spec:
  containers:
  - name: nginx
    image: nginx:stable
    ports:
    - containerPort: 80
      name: http-web-svc
